/*
 * Copyright (c) 2021 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.upstream.proto;

import cn.hutool.core.util.HexUtil;
import com.google.protobuf.util.JsonFormat;
import jdx.rover.guardian.dto.LocalView;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 */
@Slf4j
class LocalViewProtoTest {
  @Test
  void buildLocalViewTest() throws Exception {
    LocalView.LocalViewDTO infoOrign = buildLocalView();
    byte[] result = infoOrign.toByteArray();
    char[] charResult = HexUtil.encodeHex(result);
    log.info("charResult={}",result);
    for (char c : charResult) {
      System.out.print(c);
    }
    System.out.println();

    //反序列化
    LocalView.LocalViewDTO infoObj = LocalView.LocalViewDTO.parseFrom(result);
    String message = JsonFormat.printer().print(infoObj);
    System.out.println(message);
  }

  private LocalView.LocalViewDTO buildLocalView() {
    LocalView.LocalViewDTO.Builder localViewDTOBuilder = LocalView.LocalViewDTO.newBuilder();
    LocalView.AuthorizeInfo.Builder authInfoBuilder = LocalView.AuthorizeInfo.newBuilder();
    authInfoBuilder.setApiKey("BukPTIAnMv3oyZfRhQ6iV5aEpX2l7gwG7P9WHDdELNyKMzoAJZftaG12U5SxeYbv");
    authInfoBuilder.setVehicleId("JDK8135");
    authInfoBuilder.setVehicleSn("AKK21068135");

    localViewDTOBuilder.setAuthInfo(authInfoBuilder);
    LocalView.LocalViewDTO info = localViewDTOBuilder.build();
    return info;
  }
}
