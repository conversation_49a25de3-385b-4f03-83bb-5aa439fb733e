/*
 * Copyright (c) 2021 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.upstream.proto;

import cn.hutool.core.util.HexUtil;
import com.google.protobuf.util.JsonFormat;
import jdx.rover.guardian.data.dto.GuardianDataDto;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 */
@Slf4j
public class GuardianProtoTest {
  private static final long NANOSECOND_TO_MILLISECOND = 1000000L;

  private static String vehicleName = "JD40005";

  @Test
  void buildGuardianTest() throws Exception {
    GuardianDataDto.GuardianInfoDTO infoOrign = GuardianProtoTest.buildGuardianInfoDTO();
    byte[] result = infoOrign.toByteArray();
    char[] charResult = HexUtil.encodeHex(result);
    log.info("charResult={}", result);
    for (char c : charResult) {
      System.out.print(c);
    }
    System.out.println();

    //反序列化
    GuardianDataDto.GuardianInfoDTO infoObj = GuardianDataDto.GuardianInfoDTO.parseFrom(result);
    String message = JsonFormat.printer().print(infoObj);
    System.out.println(message);
  }

  public static GuardianDataDto.GuardianInfoDTO buildGuardianInfoDTO() {
    GuardianDataDto.DrivableDirection.Builder drivableDirectionBuilder = GuardianDataDto.DrivableDirection.newBuilder();
    drivableDirectionBuilder.setEnableFront(true);
    drivableDirectionBuilder.setEnableBack(true);
    drivableDirectionBuilder.setEnableLeft(true);
    drivableDirectionBuilder.setEnableRight(true);

    GuardianDataDto.Abnormal.Builder abnormalBuilder = GuardianDataDto.Abnormal.newBuilder();
    abnormalBuilder.setTimestamp(System.currentTimeMillis() * NANOSECOND_TO_MILLISECOND);
    abnormalBuilder.setErrorCode(-13000);
    abnormalBuilder.setErrorLevel("FATAL");
    abnormalBuilder.setErrorMsg("ErrorCode: ERROR_GUARDIAN_NOT_INITIALIZED, Message: Component not initialized.");
    abnormalBuilder.setModuleName("LOCALIZATION_INIT");

    GuardianDataDto.Abnormal.Builder abnormalBuilder2 = GuardianDataDto.Abnormal.newBuilder();
    abnormalBuilder2.setTimestamp(System.currentTimeMillis() * NANOSECOND_TO_MILLISECOND);
    abnormalBuilder2.setErrorCode(-1350);
    abnormalBuilder2.setErrorLevel("FATAL");
    abnormalBuilder2.setErrorMsg("ErrorCode: ERROR_GUARDIAN_COMPONENT_NOT_EXIST, Detail: [ Not found required following component: CONTROL PNC_STATUS PLANNING DRIVERS_JOYSTICK ARTIFICIAL_OBSTACLE PREDICTION ].");
    abnormalBuilder2.setModuleName("10.10.1.103@ComponentExistance");

    GuardianDataDto.SolutionStatus.Builder solutionStatusBuilder = GuardianDataDto.SolutionStatus.newBuilder();
    solutionStatusBuilder.setState(GuardianDataDto.SolutionStatus.SolutionState.valueOf("REPAIRING"));

    GuardianDataDto.ChassisStatus.Builder chassisStatusBuilder = GuardianDataDto.ChassisStatus.newBuilder();
    chassisStatusBuilder.setLat(11.11f);
    chassisStatusBuilder.setLon(11.11f);
    GuardianDataDto.ChassisStatus.DrivingMode drivingMode = GuardianDataDto.ChassisStatus.DrivingMode.valueOf("DRIVEMODE_ENGAGED_GUARDIAN_NORMAL");
    chassisStatusBuilder.setDrivingMode(drivingMode);
    chassisStatusBuilder.setHeading(11.11);
    chassisStatusBuilder.setBatteryCapacity(80.4000015258789);
    chassisStatusBuilder.setVelocity(11.11);

    GuardianDataDto.WheelEntity.Builder wheelEntityBuilder = GuardianDataDto.WheelEntity.newBuilder();
    wheelEntityBuilder.setPosition(GuardianDataDto.WheelEntity.WheelPosition.valueOf("WHEEL_POSITION_FRONT_LEFT"));
    wheelEntityBuilder.setPressureStatus(GuardianDataDto.WheelEntity.PressureStatus.valueOf("TIRE_PRESSURE_UNDER"));
    wheelEntityBuilder.setPressure(240);

//    wheelEntityBuilder.setTemperature(temperature);

    GuardianDataDto.LaunchStatus.Builder launchStatusBuilder = GuardianDataDto.LaunchStatus.newBuilder();
    launchStatusBuilder.setState(GuardianDataDto.LaunchStatus.LaunchState.valueOf("STARTING"));
    launchStatusBuilder.setCurrentStartingModuleName("LOCALIZATION_INIT");
    launchStatusBuilder.setIp("***********");
    launchStatusBuilder.addNoexistRequiredModule("test11");
    launchStatusBuilder.addNoexistRequiredModule("test12");

    GuardianDataDto.PlanningStatus.ResultCode.Builder resultCodeBuilder = GuardianDataDto.PlanningStatus.ResultCode.newBuilder();
    resultCodeBuilder.setMsg(GuardianDataDto.PlanningStatus.ResultCode.Msg.valueOf("ACTIVE_PLANNING_TOO_MANY_FAILURE"));
    resultCodeBuilder.setType(GuardianDataDto.PlanningStatus.ResultCode.Type.valueOf("NOTREADY"));

    GuardianDataDto.PlanningTrafficLightInfo.Builder interestedLightBuilder = GuardianDataDto.PlanningTrafficLightInfo.newBuilder();
    interestedLightBuilder.setId("test11");
    interestedLightBuilder.setSource(GuardianDataDto.PlanningTrafficLightInfo.StateSource.valueOf("SUPERVISOR"));

    GuardianDataDto.RoutingPosition.Builder routingPositionBuilder = GuardianDataDto.RoutingPosition.newBuilder();
    routingPositionBuilder.setLatitude(11.11);
    routingPositionBuilder.setLongitude(11.11);
    routingPositionBuilder.setMapX(11.11);
    routingPositionBuilder.setMapY(11.11);
    routingPositionBuilder.setXcsX(11.11);
    routingPositionBuilder.setXcsY(11.11);

    GuardianDataDto.StopResult.Builder stopResultBuilder = GuardianDataDto.StopResult.newBuilder();
    stopResultBuilder.addRoutingPoint(routingPositionBuilder.build());
    stopResultBuilder.setStopId(11);
    stopResultBuilder.setRoutingMileage(11.11);

    GuardianDataDto.RoutingStatus.ResultCode.Builder routingResultCodeBuilder = GuardianDataDto.RoutingStatus.ResultCode.newBuilder();
    routingResultCodeBuilder.setMsg(GuardianDataDto.RoutingStatus.ResultCode.Msg.valueOf("PROCESSING_SMOTHING"));
    routingResultCodeBuilder.setType(GuardianDataDto.RoutingStatus.ResultCode.Type.valueOf("OK"));

    GuardianDataDto.RoutingStatus.Builder routingStatusBuilder = GuardianDataDto.RoutingStatus.newBuilder();
    routingStatusBuilder.addStopResult(stopResultBuilder.build());
    routingStatusBuilder.setResultCode(routingResultCodeBuilder.build());
    routingStatusBuilder.setRoutingId(11);
    routingStatusBuilder.setCurrentStopIndex(11);
    routingStatusBuilder.setGlobalMileage(11.11);
    routingStatusBuilder.setMileageToNextStop(11.11);
    routingStatusBuilder.setCurrentStopFinishedMileage(22.22);
    routingStatusBuilder.setNaviSequenceNum(11);
    routingStatusBuilder.setNaviRefreshId(11);

    GuardianDataDto.ServiceInfo.Builder serviceInfoBuilder = GuardianDataDto.ServiceInfo.newBuilder();
    serviceInfoBuilder.setServiceName("MCU_RESPONSE");
    serviceInfoBuilder.setIp("*************");
    serviceInfoBuilder.setServiceState(GuardianDataDto.ServiceInfo.ServiceState.valueOf("PREPARE_SUCCESS"));
    serviceInfoBuilder.setProgressPermill(20);
    serviceInfoBuilder.setMsg("CODE_HARDWARE_GET_MCU_RESPONSE");
    serviceInfoBuilder.setTimestamp(1654585393000000000L);
    serviceInfoBuilder.setErrorCode(1004);
    serviceInfoBuilder.setElapsedTime(60);

    GuardianDataDto.ServiceInfo.Builder serviceInfoBuilder2 = GuardianDataDto.ServiceInfo.newBuilder();
    serviceInfoBuilder2.setServiceName("NETWORK_CHECK");
    serviceInfoBuilder2.setIp("*************");
    serviceInfoBuilder2.setServiceState(GuardianDataDto.ServiceInfo.ServiceState.valueOf("PREPARE_FAILED"));
    serviceInfoBuilder2.setProgressPermill(40);
    serviceInfoBuilder2.setMsg("CODE_HARDWARE_NET_ANOTHER_XAVIER_4G_NET_ERR");
    serviceInfoBuilder2.setTimestamp(1654585392000000000L);
    serviceInfoBuilder2.setErrorCode(1205);
    serviceInfoBuilder2.setElapsedTime(110);

    GuardianDataDto.ServiceInfo.Builder serviceInfoBuilder3 = GuardianDataDto.ServiceInfo.newBuilder();
    serviceInfoBuilder3.setServiceName("hawk");
    serviceInfoBuilder3.setIp("*************");
    serviceInfoBuilder3.setServiceState(GuardianDataDto.ServiceInfo.ServiceState.valueOf("START_SUCCESS"));
    serviceInfoBuilder3.setProgressPermill(100);
    serviceInfoBuilder3.setMsg("[v3.3.4]video start finish");
    serviceInfoBuilder3.setTimestamp(1654525786206491904L);

    GuardianDataDto.GuardianInfoDTO.Builder guardianInfoBuilder = GuardianDataDto.GuardianInfoDTO.newBuilder();
    guardianInfoBuilder.setVehicleId(vehicleName);
    guardianInfoBuilder.setTimestamp(System.currentTimeMillis() * 1000000L + System.nanoTime() % 1000000L);
    guardianInfoBuilder.setDrivableDirection(drivableDirectionBuilder);
    guardianInfoBuilder.addAbnormal(abnormalBuilder);
    guardianInfoBuilder.addAbnormal(abnormalBuilder2);
    guardianInfoBuilder.setSolutionStatus(solutionStatusBuilder);
    guardianInfoBuilder.setChassisStatus(chassisStatusBuilder);
    guardianInfoBuilder.addWheelEntity(wheelEntityBuilder);
    guardianInfoBuilder.addLaunchStatus(launchStatusBuilder);
    guardianInfoBuilder.setRoutingStatus(routingStatusBuilder);
    guardianInfoBuilder.addServiceInfo(serviceInfoBuilder);
    guardianInfoBuilder.addServiceInfo(serviceInfoBuilder2);
    guardianInfoBuilder.addServiceInfo(serviceInfoBuilder3);

    GuardianDataDto.GuardianInfoDTO info = guardianInfoBuilder.build();
    return info;
  }
}
