package com.jdx.rover.server.upstream.web.websocket;

import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.util.JsonFormat;
import com.jdx.rover.common.constant.NumberConstant;
import jdx.rover.guardian.data.dto.GuardianDataDto;
import lombok.extern.slf4j.Slf4j;
import org.java_websocket.enums.ReadyState;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import java.net.URI;
import java.util.HashMap;
import java.util.Map;

/**
 * 发送guardian消息
 *
 * <AUTHOR>
 */
@Slf4j
class GuardianWebSocketSendTest {
    public static final String VEHICLE_NAME = "JDE0056";

    @Disabled
    @Test
    void sendGuardian() throws Exception {
        // 正常驾驶模式
        // 正常驾驶模式有routing
//        String message = "{\"vehicleId\":\"JDE0185\",\"timestamp\":\"1724227413243207187\",\"sequenceNum\":10667,\"drivableDirection\":{\"enableFront\":true,\"enableBack\":true,\"enableLeft\":true,\"enableRight\":true},\"chassisStatus\":{\"batteryCapacity\":77.**************,\"velocity\":4.***************,\"heading\":2.***************,\"lat\":39.108574,\"lon\":117.04443,\"drivingMode\":\"DRIVEMODE_ENGAGED_AUTO_CTRL\",\"chargeState\":\"DISCHARGING\",\"steerZero\":-0.*****************},\"launchStatus\":[{\"state\":\"START_SUCCESS\",\"currentStartingModuleName\":\"UNIFORM\",\"ip\":\"***********\"},{\"state\":\"START_SUCCESS\",\"currentStartingModuleName\":\"ARTIFICIAL_OBSTACLE\",\"ip\":\"***********\"}],\"solutionStatus\":{},\"planningStatus\":{\"resultCode\":{\"type\":\"ACTIVE\"},\"intersectionStatus\":\"NEAR_INTERSECTION\",\"interestedLight\":[{\"id\":\"7_33005\",\"source\":\"PERCEPTION\"}],\"passNoSignalIntersectionStatus\":{}},\"routingStatus\":{\"resultCode\":{\"type\":\"OK\"},\"routingId\":1001,\"globalMileage\":8023.************,\"stopResult\":[{\"stopId\":4270,\"routingMileage\":7319.************},{\"stopId\":4267,\"routingMileage\":704.*************}],\"mileageToNextStop\":3165.************,\"currentStopFinishedMileage\":4153.************,\"naviRefreshId\":2,\"naviSequenceNum\":4,\"missionStatus\":\"RUNNING\"},\"wheelEntity\":[{\"position\":\"WHEEL_POSITION_FRONT_LEFT\",\"pressure\":225,\"pressureStatus\":\"TIRE_PRESSURE_NORMAL\"},{\"position\":\"WHEEL_POSITION_FRONT_RIGHT\",\"pressure\":202,\"pressureStatus\":\"TIRE_PRESSURE_NORMAL\"},{\"position\":\"WHEEL_POSITION_BACK_LEFT\",\"pressure\":235,\"pressureStatus\":\"TIRE_PRESSURE_NORMAL\"},{\"position\":\"WHEEL_POSITION_BACK_RIGHT\",\"pressure\":225,\"pressureStatus\":\"TIRE_PRESSURE_NORMAL\"}],\"otaUpdaterStatus\":{\"versions\":[{\"name\":\"rover\",\"curVersion\":\"1.28.8_tianjin_JP46_engine\",\"otaVersion\":\"rover[2-1.28.8_tianjin_JP46_engine]\",\"localVersions\":[\"rover[2-1.26.5.20240323_release_JP46_engine]\",\"rover[2-1.27.2.20240420_release_JP46_engine]\",\"rover[2-1.28.7.20240629_release_JP46_engine]\",\"rover[2-1.28.8.20240629_release_JP46_engine]\",\"rover[2-1.28.8_tianjin_JP46_engine]\"]},{\"name\":\"rover-conf\",\"curVersion\":\"conf202408191323360003\",\"otaVersion\":\"rover-conf[2-conf202408191323360003]\",\"localVersions\":[\"rover-conf[2-conf202309100911330310]\",\"rover-conf[2-conf202309131701070002]\",\"rover-conf[2-conf202312251908390002]\",\"rover-conf[2-conf202408191314280003]\",\"rover-conf[2-conf202408191323360003]\"]},{\"name\":\"jdmap\",\"curVersion\":\"161-210446\",\"localVersions\":[\"[2-187-210661]\",\"[2-187-210662]\",\"[2-161-210446]\",\"[2-161-210444]\",\"[2-149-201460]\",\"[2-149-201456]\",\"[2-149-201455]\"]}],\"updating\":{\"processStatus\":{\"loadProgress\":{}}}},\"localizationConfidence\":{\"confidenceValue\":4.0192574646978345,\"matchingIterations\":2,\"ndtTransLikelihood\":2.444832297760367},\"roverStatus\":{\"runMapState\":\"RUN_HAVE_MAP\"}}";
        String message = "{\"vehicleId\":\"JDK8158\",\"timestamp\":\"1724245656132520801\",\"sequenceNum\":13058,\"drivableDirection\":{\"enableFront\":true,\"enableBack\":true,\"enableLeft\":true,\"enableRight\":true},\"abnormal\":[{\"moduleName\":\"DRIVERS_CHASSIS_RX\",\"errorCode\":-1029,\"errorLevel\":\"ERROR\",\"errorMsg\":\"ErrorCode: ERROR_DRIVER_CHASSIS_WHEEL_ALARM, Message: [WHEEL_POSITION_FRONT_RIGHT: TIRE_PRESSURE_UNDER_LIMIT].\",\"errorCodeString\":\"ERROR_DRIVER_CHASSIS_WHEEL_ALARM\",\"timestamp\":\"1724245654080603756\"}],\"chassisStatus\":{\"batteryCapacity\":84.0,\"velocity\":3.581814236111111,\"heading\":0.014856949201539127,\"lat\":31.612879,\"lon\":120.80685,\"drivingMode\":\"DRIVEMODE_ENGAGED_AUTO_CTRL\",\"chargeState\":\"DISCHARGING\",\"steerZero\":-0.****************},\"launchStatus\":[{\"state\":\"START_SUCCESS\",\"currentStartingModuleName\":\"UNIFORM\",\"ip\":\"***********\"},{\"state\":\"START_SUCCESS\",\"currentStartingModuleName\":\"ARTIFICIAL_OBSTACLE\",\"ip\":\"***********\"}],\"solutionStatus\":{},\"planningStatus\":{\"resultCode\":{\"type\":\"ACTIVE\"},\"intersectionStatus\":\"NEAR_INTERSECTION\",\"passNoSignalIntersectionStatus\":{}},\"routingStatus\":{\"resultCode\":{\"type\":\"OK\"},\"routingId\":1000,\"globalMileage\":3964.*************,\"stopResult\":[{\"stopId\":3449},{\"stopId\":2526,\"routingMileage\":3964.*************}],\"currentStopIndex\":1,\"mileageToNextStop\":3136.*************,\"currentStopFinishedMileage\":828.*************,\"naviRefreshId\":2,\"naviSequenceNum\":4,\"missionStatus\":\"RUNNING\"},\"wheelEntity\":[{\"position\":\"WHEEL_POSITION_FRONT_LEFT\",\"pressure\":236,\"pressureStatus\":\"TIRE_PRESSURE_NORMAL\"},{\"position\":\"WHEEL_POSITION_FRONT_RIGHT\",\"pressure\":204,\"pressureStatus\":\"TIRE_PRESSURE_UNDER_LIMIT\"},{\"position\":\"WHEEL_POSITION_BACK_LEFT\",\"pressure\":256,\"pressureStatus\":\"TIRE_PRESSURE_NORMAL\"},{\"position\":\"WHEEL_POSITION_BACK_RIGHT\",\"pressure\":244,\"pressureStatus\":\"TIRE_PRESSURE_NORMAL\"}],\"otaUpdaterStatus\":{\"versions\":[{\"name\":\"rover\",\"curVersion\":\"1.28.9.20240629_release_JP46_engine\",\"otaVersion\":\"rover[2-1.28.9.20240629_release_JP46_engine]\",\"localVersions\":[\"rover[2-1.27.0.20240420_release_JP46_engine]\",\"rover[2-1.27.2.20240420_release_JP46_engine]\",\"rover[2-1.28.5.20240629_release_JP46_engine]\",\"rover[2-1.28.7.20240629_release_JP46_engine]\",\"rover[2-1.28.9.20240629_release_JP46_engine]\"]},{\"name\":\"rover-conf\",\"curVersion\":\"conf202312251909220002\",\"otaVersion\":\"rover-conf[2-conf202312251909220002]\",\"localVersions\":[\"rover-conf[2-conf202309071612440002]\",\"rover-conf[2-conf202309072132090002]\",\"rover-conf[2-conf202309100912080002]\",\"rover-conf[2-conf202309131700110002]\",\"rover-conf[2-conf202312251909220002]\"]},{\"name\":\"jdmap\",\"curVersion\":\"130-201111\",\"localVersions\":[\"[2-130-201111]\",\"[2-130-201109]\"]}],\"updating\":{\"processStatus\":{\"loadProgress\":{}}}},\"localizationConfidence\":{\"confidenceValue\":5.0,\"matchingIterations\":3,\"ndtTransLikelihood\":3.3958339658194845},\"roverStatus\":{\"runMapState\":\"RUN_HAVE_MAP\"}}";
//        String message = "{\"vehicleId\":\"JDZ0038\",\"timestamp\":\"1693893449369473548\",\"sequenceNum\":9384,\"drivableDirection\":{\"enableFront\":true,\"enableBack\":true,\"enableLeft\":true,\"enableRight\":true},\"chassisStatus\":{\"batteryCapacity\":85.26315307617188,\"velocity\":0.1599999999999966,\"heading\":2.171853672273784,\"lat\":39.784058,\"lon\":116.554726,\"drivingMode\":\"DRIVEMODE_ENGAGED_AUTO_CTRL\",\"chargeState\":\"DISCHARGING\"},\"launchStatus\":[{\"state\":\"START_SUCCESS\",\"currentStartingModuleName\":\"ARTIFICIAL_OBSTACLE\",\"ip\":\"***********\"},{\"state\":\"START_SUCCESS\",\"currentStartingModuleName\":\"RECORDER_LITTLE_BAG\",\"ip\":\"***********\"}],\"solutionStatus\":{},\"planningStatus\":{\"resultCode\":{\"type\":\"ACTIVE\"},\"intersectionStatus\":\"NEAR_INTERSECTION\",\"passNoSignalIntersectionStatus\":{}},\"routingStatus\":{\"resultCode\":{\"type\":\"OK\"},\"routingId\":1001,\"globalMileage\":91.**************,\"stopResult\":[{\"stopId\":171,\"routingMileage\":58.**************},{\"stopId\":164,\"routingMileage\":33.**************}],\"mileageToNextStop\":58.**************,\"naviRefreshId\":2,\"naviSequenceNum\":5,\"missionStatus\":\"RUNNING\"},\"wheelEntity\":[{\"position\":\"WHEEL_POSITION_BACK_RIGHT\",\"pressure\":218,\"pressureStatus\":\"TIRE_PRESSURE_NORMAL\"},{\"position\":\"WHEEL_POSITION_FRONT_LEFT\",\"pressure\":230,\"pressureStatus\":\"TIRE_PRESSURE_NORMAL\"},{\"position\":\"WHEEL_POSITION_FRONT_RIGHT\",\"pressure\":227,\"pressureStatus\":\"TIRE_PRESSURE_NORMAL\"},{\"position\":\"WHEEL_POSITION_BACK_LEFT\",\"pressure\":237,\"pressureStatus\":\"TIRE_PRESSURE_NORMAL\"}],\"otaUpdaterStatus\":{\"versions\":[{\"name\":\"rover\",\"curVersion\":\"1.20.4.230731_release_JP46_engine\",\"otaVersion\":\"rover[2-1.20.4.230731_release_JP46_engine]\",\"localVersions\":[\"rover[2-QA_20230406_daily_JP46]\",\"rover[2-QA_20230513_daily_JP46]\",\"rover[2-QA_20230618_daily_JP46]\",\"rover[2-1.19.3.230629_release_JP46_engine]\",\"rover[2-1.20.4.230731_release_JP46_engine]\"]},{\"name\":\"rover-conf\",\"curVersion\":\"conf202304071041420212\",\"otaVersion\":\"rover-conf[2-conf202304071041420212]\",\"localVersions\":[\"rover-conf[2-conf2023011***********]\",\"rover-conf[2-conf202303200944130002]\",\"rover-conf[2-conf202304061351300002]\",\"rover-conf[2-conf202304071041420212]\"]},{\"name\":\"jdmap\",\"curVersion\":\"149-201455\",\"localVersions\":[\"[2-122-200519]\",\"[2-161-210376]\",\"[2-142-201104]\",\"[2-149-201454]\",\"[2-149-201455]\"]}],\"updating\":{\"processStatus\":{\"loadProgress\":{}}}},\"localizationConfidence\":{\"confidenceValue\":5.0,\"matchingIterations\":2,\"ndtTransLikelihood\":3.099729794758693}}";
//        String message = "{\"vehicleId\":\"JDXT002\",\"timestamp\":\"1694154250307585584\",\"sequenceNum\":5787,\"drivableDirection\":{\"enableFront\":true,\"enableBack\":true,\"enableLeft\":true,\"enableRight\":true},\"abnormal\":[{\"moduleName\":\"ComponentFrequence\",\"errorCode\":-13501,\"errorLevel\":\"FATAL\",\"errorMsg\":\"ErrorCode: ERROR_GUARDIAN_COMPONENT_HZ_NOT_MATCH, Detail: [LOCALIZATION_LIDAR hz fatal,].\",\"errorCodeString\":\"ERROR_GUARDIAN_COMPONENT_HZ_NOT_MATCH\",\"timestamp\":\"1694154249416030758\"},{\"moduleName\":\"DRIVERS_CHASSIS_RX\",\"errorCode\":-1029,\"errorLevel\":\"ERROR\",\"errorMsg\":\"ErrorCode: ERROR_DRIVER_CHASSIS_WHEEL_ALARM, Message: [WHEEL_POSITION_FRONT_RIGHT: TIRE_PRESSURE_UNDER_LIMIT].\",\"errorCodeString\":\"ERROR_DRIVER_CHASSIS_WHEEL_ALARM\",\"timestamp\":\"1694154249015410352\"},{\"moduleName\":\"DRIVERS_CHASSIS_RX\",\"errorCode\":-1056,\"errorLevel\":\"ERROR\",\"errorMsg\":\"ErrorCode: ERROR_DRIVER_CHASSIS_STOP_FAULT, Message: LEVEL: 2, TYPE: FAULT_STOP_EHB, CODE: 0x1e54291(31802001).\",\"errorCodeString\":\"ERROR_DRIVER_CHASSIS_STOP_FAULT\",\"timestamp\":\"1694148540177263899\"}],\"chassisStatus\":{\"batteryCapacity\":88.70588684082031,\"velocity\":3.271484375,\"heading\":3.117427696554457,\"lat\":33.95237,\"lon\":118.34783,\"drivingMode\":\"DRIVEMODE_ENGAGED_GUARDIAN_NORMAL\",\"chargeState\":\"DISCHARGING\"},\"launchStatus\":[{\"state\":\"START_SUCCESS\",\"currentStartingModuleName\":\"DRIVERS_JOYSTICK\",\"ip\":\"***********\"},{\"state\":\"START_SUCCESS\",\"currentStartingModuleName\":\"RECORDER_LITTLE_BAG\",\"ip\":\"***********\"}],\"solutionStatus\":{},\"planningStatus\":{\"resultCode\":{\"type\":\"ACTIVE\"},\"intersectionStatus\":\"NEAR_INTERSECTION\",\"passNoSignalIntersectionStatus\":{}},\"routingStatus\":{\"resultCode\":{\"type\":\"OK\"},\"routingId\":1000,\"globalMileage\":8145.************,\"stopResult\":[{\"stopId\":3505,\"routingMileage\":3893.************},{\"stopId\":426,\"routingMileage\":4251.************}],\"mileageToNextStop\":41.**************,\"currentStopFinishedMileage\":3852.************,\"naviRefreshId\":1,\"naviSequenceNum\":2,\"missionStatus\":\"RUNNING\"},\"wheelEntity\":[{\"position\":\"WHEEL_POSITION_BACK_LEFT\",\"pressure\":280,\"pressureStatus\":\"TIRE_PRESSURE_NORMAL\"},{\"position\":\"WHEEL_POSITION_BACK_RIGHT\",\"pressure\":264,\"pressureStatus\":\"TIRE_PRESSURE_NORMAL\"},{\"position\":\"WHEEL_POSITION_FRONT_LEFT\",\"pressure\":252,\"pressureStatus\":\"TIRE_PRESSURE_NORMAL\"},{\"position\":\"WHEEL_POSITION_FRONT_RIGHT\",\"pressure\":44,\"pressureStatus\":\"TIRE_PRESSURE_UNDER_LIMIT\"}],\"otaUpdaterStatus\":{\"versions\":[{\"name\":\"rover\",\"curVersion\":\"1.20.4.230731_release_JP46_engine\",\"otaVersion\":\"rover[2-1.20.4.230731_release_JP46_engine]\",\"localVersions\":[\"rover[2-1.17.3.230427_release_JP46_engine]\",\"rover[2-********.230518_release_JP46_engine]\",\"rover[2-********_fixcore_release_JP46_engine]\",\"rover[2-1.19.3.230629_release_JP46_engine]\",\"rover[2-1.20.4.230731_release_JP46_engine]\"]},{\"name\":\"rover-conf\",\"curVersion\":\"conf202309072132090002\",\"otaVersion\":\"rover-conf[2-conf202309072132090002]\",\"localVersions\":[\"rover-conf[2-conf2023**************]\",\"rover-conf[2-conf202304201037320002]\",\"rover-conf[2-conf202305092054500002]\",\"rover-conf[2-conf202307141141390002]\",\"rover-conf[2-conf202309072132090002]\"]},{\"name\":\"jdmap\",\"curVersion\":\"166-210464\",\"localVersions\":[\"[2-166-210463]\",\"[2-166-210464]\"]}],\"updating\":{\"processStatus\":{\"loadProgress\":{}}}},\"localizationConfidence\":{\"confidenceValue\":4.**************,\"matchingIterations\":3,\"ndtTransLikelihood\":2.****************}}";
        // 按钮急停有routing
//        String message = "{\"vehicleId\":\"JDXT0002\",\"timestamp\":\"1692777264730849348\",\"sequenceNum\":29701,\"drivableDirection\":{\"enableFront\":true,\"enableBack\":true,\"enableLeft\":true,\"enableRight\":true},\"abnormal\":[{\"moduleName\":\"FeatureCollector\",\"errorCode\":-13053,\"errorLevel\":\"ERROR\",\"errorMsg\":\"ErrorCode: ERROR_GUARDIAN_CONTROL_BRAKE_RESPONSE, Detail: [Brake response is abnormal. Mean response percentage: 22.400000].\",\"errorCodeString\":\"ERROR_GUARDIAN_CONTROL_BRAKE_RESPONSE\",\"timestamp\":\"1692749370352258777\"}],\"chassisStatus\":{\"batteryCapacity\":66.**************,\"heading\":2.***************,\"lat\":39.77272,\"lon\":116.51994,\"drivingMode\":\"DRIVEMODE_TAKEOVER_EMERGENCY_STOP\",\"chargeState\":\"DISCHARGING\"},\"launchStatus\":[{\"state\":\"START_SUCCESS\",\"currentStartingModuleName\":\"UNIFORM\",\"ip\":\"***********\"},{\"state\":\"START_SUCCESS\",\"currentStartingModuleName\":\"DRIVERS_JOYSTICK\",\"ip\":\"***********\"}],\"solutionStatus\":{},\"planningStatus\":{\"resultCode\":{\"type\":\"ACCOMPLISHED\"},\"passNoSignalIntersectionStatus\":{}},\"routingStatus\":{\"resultCode\":{\"type\":\"OK\"},\"routingId\":1002,\"globalMileage\":4013.*************,\"stopResult\":[{\"stopId\":546,\"routingMileage\":1624.*************},{\"stopId\":544,\"routingMileage\":2389.*************}],\"mileageToNextStop\":1624.*************,\"naviRefreshId\":3,\"naviSequenceNum\":9,\"missionStatus\":\"AS_ARRIVED\"},\"wheelEntity\":[{\"position\":\"WHEEL_POSITION_FRONT_RIGHT\",\"pressure\":220,\"pressureStatus\":\"TIRE_PRESSURE_NORMAL\"},{\"position\":\"WHEEL_POSITION_BACK_LEFT\",\"pressure\":224,\"pressureStatus\":\"TIRE_PRESSURE_NORMAL\"},{\"position\":\"WHEEL_POSITION_BACK_RIGHT\",\"pressure\":236,\"pressureStatus\":\"TIRE_PRESSURE_NORMAL\"},{\"position\":\"WHEEL_POSITION_FRONT_LEFT\",\"pressure\":240,\"pressureStatus\":\"TIRE_PRESSURE_NORMAL\"}],\"otaUpdaterStatus\":{\"versions\":[{\"name\":\"rover\",\"curVersion\":\"1.19.3.230629_release_JP46_engine\",\"otaVersion\":\"rover[2-1.20.4.230731_release_JP46_engine]\",\"localVersions\":[\"rover[2-1.17.3_ukf_cyclist_release_JP46_engine]\",\"rover[2-********.230518_release_JP46_engine]\",\"rover[2-********_fixcore_release_JP46_engine]\",\"rover[2-1.19.3.230629_release_JP46_engine]\",\"rover[2-1.20.4.230731_release_JP46_engine]\"]},{\"name\":\"rover-conf\",\"curVersion\":\"conf202307141141390002\",\"otaVersion\":\"rover-conf[2-conf202307141141390002]\",\"localVersions\":[\"rover-conf[2-conf202303181252450003]\",\"rover-conf[2-conf202304201037320002]\",\"rover-conf[2-conf202304261538240006]\",\"rover-conf[2-conf202305092054500002]\",\"rover-conf[2-conf202307141141390002]\"]},{\"name\":\"jdmap\",\"curVersion\":\"149-201451\",\"localVersions\":[\"[2-149-201452]\",\"[2-149-201451]\",\"[2-149-201449]\"]}],\"updating\":{\"processStatus\":{\"loadProgress\":{}}}},\"localizationConfidence\":{}}";
//        String message = "{\"vehicleId\":\"JDZ0038\",\"timestamp\":\"1693893450370017661\",\"sequenceNum\":9385,\"drivableDirection\":{\"enableFront\":true,\"enableBack\":true,\"enableLeft\":true,\"enableRight\":true},\"chassisStatus\":{\"batteryCapacity\":85.26315307617188,\"heading\":2.1725860435033884,\"lat\":39.784058,\"lon\":116.554726,\"drivingMode\":\"DRIVEMODE_TAKEOVER_EMERGENCY_STOP\",\"chargeState\":\"DISCHARGING\"},\"launchStatus\":[{\"state\":\"START_SUCCESS\",\"currentStartingModuleName\":\"ARTIFICIAL_OBSTACLE\",\"ip\":\"***********\"},{\"state\":\"START_SUCCESS\",\"currentStartingModuleName\":\"RECORDER_LITTLE_BAG\",\"ip\":\"***********\"}],\"solutionStatus\":{},\"planningStatus\":{\"resultCode\":{\"type\":\"ACTIVE\"},\"intersectionStatus\":\"NEAR_INTERSECTION\",\"passNoSignalIntersectionStatus\":{}},\"routingStatus\":{\"resultCode\":{\"type\":\"OK\"},\"routingId\":1001,\"globalMileage\":91.**************,\"stopResult\":[{\"stopId\":171,\"routingMileage\":58.**************},{\"stopId\":164,\"routingMileage\":33.**************}],\"mileageToNextStop\":58.**************,\"naviRefreshId\":2,\"naviSequenceNum\":5,\"missionStatus\":\"RUNNING\"},\"wheelEntity\":[{\"position\":\"WHEEL_POSITION_BACK_RIGHT\",\"pressure\":218,\"pressureStatus\":\"TIRE_PRESSURE_NORMAL\"},{\"position\":\"WHEEL_POSITION_FRONT_LEFT\",\"pressure\":230,\"pressureStatus\":\"TIRE_PRESSURE_NORMAL\"},{\"position\":\"WHEEL_POSITION_FRONT_RIGHT\",\"pressure\":227,\"pressureStatus\":\"TIRE_PRESSURE_NORMAL\"},{\"position\":\"WHEEL_POSITION_BACK_LEFT\",\"pressure\":237,\"pressureStatus\":\"TIRE_PRESSURE_NORMAL\"}],\"otaUpdaterStatus\":{\"versions\":[{\"name\":\"rover\",\"curVersion\":\"1.20.4.230731_release_JP46_engine\",\"otaVersion\":\"rover[2-1.20.4.230731_release_JP46_engine]\",\"localVersions\":[\"rover[2-QA_20230406_daily_JP46]\",\"rover[2-QA_20230513_daily_JP46]\",\"rover[2-QA_20230618_daily_JP46]\",\"rover[2-1.19.3.230629_release_JP46_engine]\",\"rover[2-1.20.4.230731_release_JP46_engine]\"]},{\"name\":\"rover-conf\",\"curVersion\":\"conf202304071041420212\",\"otaVersion\":\"rover-conf[2-conf202304071041420212]\",\"localVersions\":[\"rover-conf[2-conf2023011***********]\",\"rover-conf[2-conf202303200944130002]\",\"rover-conf[2-conf202304061351300002]\",\"rover-conf[2-conf202304071041420212]\"]},{\"name\":\"jdmap\",\"curVersion\":\"149-201455\",\"localVersions\":[\"[2-122-200519]\",\"[2-161-210376]\",\"[2-142-201104]\",\"[2-149-201454]\",\"[2-149-201455]\"]}],\"updating\":{\"processStatus\":{\"loadProgress\":{}}}},\"localizationConfidence\":{\"confidenceValue\":5.0,\"matchingIterations\":3,\"ndtTransLikelihood\":3.****************}}";
        // 按钮急停无routing
//        String message = "{\"vehicleId\":\"JDZ0038\",\"timestamp\":\"1692777264730849348\",\"sequenceNum\":29701,\"drivableDirection\":{\"enableFront\":true,\"enableBack\":true,\"enableLeft\":true,\"enableRight\":true},\"abnormal\":[{\"moduleName\":\"FeatureCollector\",\"errorCode\":-13053,\"errorLevel\":\"ERROR\",\"errorMsg\":\"ErrorCode: ERROR_GUARDIAN_CONTROL_BRAKE_RESPONSE, Detail: [Brake response is abnormal. Mean response percentage: 22.400000].\",\"errorCodeString\":\"ERROR_GUARDIAN_CONTROL_BRAKE_RESPONSE\",\"timestamp\":\"1692749370352258777\"}],\"chassisStatus\":{\"batteryCapacity\":66.**************,\"heading\":2.***************,\"lat\":39.77272,\"lon\":116.51994,\"drivingMode\":\"DRIVEMODE_TAKEOVER_EMERGENCY_STOP\",\"chargeState\":\"DISCHARGING\"},\"launchStatus\":[{\"state\":\"START_SUCCESS\",\"currentStartingModuleName\":\"UNIFORM\",\"ip\":\"***********\"},{\"state\":\"START_SUCCESS\",\"currentStartingModuleName\":\"DRIVERS_JOYSTICK\",\"ip\":\"***********\"}],\"solutionStatus\":{},\"planningStatus\":{\"resultCode\":{\"type\":\"ACCOMPLISHED\"},\"passNoSignalIntersectionStatus\":{}},\"routingStatus\":{},\"wheelEntity\":[{\"position\":\"WHEEL_POSITION_FRONT_RIGHT\",\"pressure\":220,\"pressureStatus\":\"TIRE_PRESSURE_NORMAL\"},{\"position\":\"WHEEL_POSITION_BACK_LEFT\",\"pressure\":224,\"pressureStatus\":\"TIRE_PRESSURE_NORMAL\"},{\"position\":\"WHEEL_POSITION_BACK_RIGHT\",\"pressure\":236,\"pressureStatus\":\"TIRE_PRESSURE_NORMAL\"},{\"position\":\"WHEEL_POSITION_FRONT_LEFT\",\"pressure\":240,\"pressureStatus\":\"TIRE_PRESSURE_NORMAL\"}],\"otaUpdaterStatus\":{\"versions\":[{\"name\":\"rover\",\"curVersion\":\"1.19.3.230629_release_JP46_engine\",\"otaVersion\":\"rover[2-1.20.4.230731_release_JP46_engine]\",\"localVersions\":[\"rover[2-1.17.3_ukf_cyclist_release_JP46_engine]\",\"rover[2-********.230518_release_JP46_engine]\",\"rover[2-********_fixcore_release_JP46_engine]\",\"rover[2-1.19.3.230629_release_JP46_engine]\",\"rover[2-1.20.4.230731_release_JP46_engine]\"]},{\"name\":\"rover-conf\",\"curVersion\":\"conf202307141141390002\",\"otaVersion\":\"rover-conf[2-conf202307141141390002]\",\"localVersions\":[\"rover-conf[2-conf202303181252450003]\",\"rover-conf[2-conf202304201037320002]\",\"rover-conf[2-conf202304261538240006]\",\"rover-conf[2-conf202305092054500002]\",\"rover-conf[2-conf202307141141390002]\"]},{\"name\":\"jdmap\",\"curVersion\":\"149-201451\",\"localVersions\":[\"[2-149-201452]\",\"[2-149-201451]\",\"[2-149-201449]\"]}],\"updating\":{\"processStatus\":{\"loadProgress\":{}}}},\"localizationConfidence\":{}}";
//      远程接管
//        String message = "{\"vehicleId\":\"JDZ0038\",\"timestamp\":\"1693902748669041324\",\"sequenceNum\":18677,\"drivableDirection\":{\"enableLeft\":true,\"enableRight\":true},\"abnormal\":[{\"moduleName\":\"StuckAlarm\",\"errorCode\":-13200,\"errorLevel\":\"ERROR\",\"errorMsg\":\"ErrorCode: ERROR_GUARDIAN_VEHICLE_STUCK, Detail: [The vehicle is stuck!].\",\"errorCodeString\":\"ERROR_GUARDIAN_VEHICLE_STUCK\",\"timestamp\":\"1693902747736989184\"}],\"chassisStatus\":{\"batteryCapacity\":75.78947448730469,\"heading\":2.1632107742381264,\"lat\":39.784058,\"lon\":116.554726,\"drivingMode\":\"DRIVEMODE_TAKEOVER_CHASSIS_CTRL\",\"chargeState\":\"DISCHARGING\"},\"launchStatus\":[{\"state\":\"START_SUCCESS\",\"currentStartingModuleName\":\"ARTIFICIAL_OBSTACLE\",\"ip\":\"***********\"},{\"state\":\"START_SUCCESS\",\"currentStartingModuleName\":\"RECORDER_LITTLE_BAG\",\"ip\":\"***********\"}],\"solutionStatus\":{},\"planningStatus\":{\"resultCode\":{\"type\":\"ACTIVE\"},\"intersectionStatus\":\"NEAR_INTERSECTION\",\"passNoSignalIntersectionStatus\":{}},\"routingStatus\":{\"resultCode\":{\"type\":\"OK\"},\"routingId\":1003,\"globalMileage\":92.**************,\"stopResult\":[{\"stopId\":171,\"routingMileage\":58.***************},{\"stopId\":164,\"routingMileage\":33.**************}],\"mileageToNextStop\":58.***************,\"naviRefreshId\":4,\"naviSequenceNum\":11,\"missionStatus\":\"RUNNING\"},\"wheelEntity\":[{\"position\":\"WHEEL_POSITION_BACK_RIGHT\",\"pressure\":218,\"pressureStatus\":\"TIRE_PRESSURE_NORMAL\"},{\"position\":\"WHEEL_POSITION_FRONT_LEFT\",\"pressure\":224,\"pressureStatus\":\"TIRE_PRESSURE_NORMAL\"},{\"position\":\"WHEEL_POSITION_FRONT_RIGHT\",\"pressure\":227,\"pressureStatus\":\"TIRE_PRESSURE_NORMAL\"},{\"position\":\"WHEEL_POSITION_BACK_LEFT\",\"pressure\":234,\"pressureStatus\":\"TIRE_PRESSURE_NORMAL\"}],\"otaUpdaterStatus\":{\"versions\":[{\"name\":\"rover\",\"curVersion\":\"1.20.4.230731_release_JP46_engine\",\"otaVersion\":\"rover[2-1.20.4.230731_release_JP46_engine]\",\"localVersions\":[\"rover[2-QA_20230406_daily_JP46]\",\"rover[2-QA_20230513_daily_JP46]\",\"rover[2-QA_20230618_daily_JP46]\",\"rover[2-1.19.3.230629_release_JP46_engine]\",\"rover[2-1.20.4.230731_release_JP46_engine]\"]},{\"name\":\"rover-conf\",\"curVersion\":\"conf202304071041420212\",\"otaVersion\":\"rover-conf[2-conf202304071041420212]\",\"localVersions\":[\"rover-conf[2-conf2023011***********]\",\"rover-conf[2-conf202303200944130002]\",\"rover-conf[2-conf202304061351300002]\",\"rover-conf[2-conf202304071041420212]\"]},{\"name\":\"jdmap\",\"curVersion\":\"149-201455\",\"localVersions\":[\"[2-122-200519]\",\"[2-161-210376]\",\"[2-142-201104]\",\"[2-149-201454]\",\"[2-149-201455]\"]}],\"updating\":{\"processStatus\":{\"loadProgress\":{}}}},\"localizationConfidence\":{\"confidenceValue\":5.0,\"matchingIterations\":3,\"ndtTransLikelihood\":3.0810675201226356}}";


        String host = "127.0.0.1:8011";
        // 行云测试站
//        host = "***********:8011";
        // beta环境
        host = "jdxserver-up-beta.jd.local";
        String url = "ws://" + host + "/upstream/ws/guardian";
        try {
            Map<String, String> httpHeaders = new HashMap<>();
            httpHeaders.put("vehicleName", VEHICLE_NAME);
            TestWebSocketClient myClient = new TestWebSocketClient(new URI(url), httpHeaders);
            myClient.connect();
            while (!myClient.getReadyState().equals(ReadyState.OPEN)) {
                log.info("连接中。。。");
                Thread.sleep(1000);
            }

            GuardianDataDto.GuardianInfoDTO.Builder infoOrignBuilder1 = buildMessage(message);
            setDisableFrontAndBack(infoOrignBuilder1);
            setDrivingMode(infoOrignBuilder1, GuardianDataDto.ChassisStatus.DrivingMode.DRIVEMODE_TAKEOVER_REMOTE_CTRL);
            sendMessage(myClient, infoOrignBuilder1);
            log.info("发送guardian信息1完成!");

            Thread.sleep(130000);

            for (int i = 0; i < 1000; i++) {
                // 连接成功往websocket服务端发送数据
                GuardianDataDto.GuardianInfoDTO.Builder infoOrignBuilder = buildMessage(message);
                sendMessage(myClient, infoOrignBuilder);

                log.info("发送guardian信息完成!");
                Thread.sleep(9000);
            }
            myClient.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
        Thread.sleep(1000);
    }

    /**
     * 发送消息
     *
     * @param myClient
     * @param msg
     */
    private void sendMessage(TestWebSocketClient myClient, String msg) {
        GuardianDataDto.GuardianInfoDTO.Builder infoOrignBuilder = GuardianDataDto.GuardianInfoDTO.newBuilder();
        try {
            // 直接解析json,得到protobuf数据,也可以自己构造,麻烦一些
            JsonFormat.parser().merge(msg, infoOrignBuilder);
        } catch (InvalidProtocolBufferException e) {
            e.printStackTrace();
        }
        // 设置车号
        infoOrignBuilder.setVehicleId(VEHICLE_NAME);
        // 设置上报时间为当前时间
        infoOrignBuilder.setTimestamp(System.currentTimeMillis() * NumberConstant.MILLION);
        // 设置前后使能为false
//        setDisableFrontAndBack(infoOrignBuilder);
        // 设置按钮急停驾驶模式
//        setDrivingMode(infoOrignBuilder, GuardianDataDto.ChassisStatus.DrivingMode.DRIVEMODE_TAKEOVER_EMERGENCY_STOP);
        // 设置远程遥控驾驶模式
//        setDrivingMode(infoOrignBuilder, GuardianDataDto.ChassisStatus.DrivingMode.DRIVEMODE_TAKEOVER_REMOTE_CTRL);
        // 设置异常
        setFatalAbnormal(infoOrignBuilder);

        GuardianDataDto.GuardianInfoDTO infoOrign = infoOrignBuilder.build();
        byte[] result = infoOrign.toByteArray();
        myClient.send(result);
    }

    /**
     * 发送消息
     */
    private void sendMessage(TestWebSocketClient myClient, GuardianDataDto.GuardianInfoDTO.Builder infoOrignBuilder) {
        GuardianDataDto.GuardianInfoDTO infoOrign = infoOrignBuilder.build();
        byte[] result = infoOrign.toByteArray();
        myClient.send(result);
    }

    private GuardianDataDto.GuardianInfoDTO.Builder buildMessage(String msg) {
        GuardianDataDto.GuardianInfoDTO.Builder infoOrignBuilder = GuardianDataDto.GuardianInfoDTO.newBuilder();
        try {
            // 直接解析json,得到protobuf数据,也可以自己构造,麻烦一些
            JsonFormat.parser().merge(msg, infoOrignBuilder);
        } catch (InvalidProtocolBufferException e) {
            e.printStackTrace();
        }
        // 设置车号
        infoOrignBuilder.setVehicleId(VEHICLE_NAME);
        // 设置上报时间为当前时间
        infoOrignBuilder.setTimestamp(System.currentTimeMillis() * NumberConstant.MILLION);
        // 设置前后使能为false
//        setDisableFrontAndBack(infoOrignBuilder);
        // 设置按钮急停驾驶模式
//        setDrivingMode(infoOrignBuilder, GuardianDataDto.ChassisStatus.DrivingMode.DRIVEMODE_TAKEOVER_EMERGENCY_STOP);
        // 设置远程遥控驾驶模式
//        setDrivingMode(infoOrignBuilder, GuardianDataDto.ChassisStatus.DrivingMode.DRIVEMODE_TAKEOVER_REMOTE_CTRL);
        // 设置异常
        setFatalAbnormal(infoOrignBuilder);
        return infoOrignBuilder;
    }

    private static void setDisableFrontAndBack(GuardianDataDto.GuardianInfoDTO.Builder infoOrignBuilder) {
        infoOrignBuilder.getDrivableDirectionBuilder().setEnableFront(false);
        infoOrignBuilder.getDrivableDirectionBuilder().setEnableBack(false);
    }

    private static void setDrivingMode(GuardianDataDto.GuardianInfoDTO.Builder infoOrignBuilder, GuardianDataDto.ChassisStatus.DrivingMode drivingMode) {
        infoOrignBuilder.getChassisStatusBuilder().setDrivingMode(drivingMode);
    }

    private static void setFatalAbnormal(GuardianDataDto.GuardianInfoDTO.Builder infoOrignBuilder) {
        GuardianDataDto.Abnormal.Builder abnormalBuilder = GuardianDataDto.Abnormal.newBuilder();
        abnormalBuilder.setTimestamp(System.currentTimeMillis() * NumberConstant.MILLION);
        abnormalBuilder.setErrorCode(-13000);
        abnormalBuilder.setErrorLevel("FATAL");
        abnormalBuilder.setErrorMsg("ErrorCode: ERROR_GUARDIAN_NOT_INITIALIZED, Message: Component not initialized.");
        abnormalBuilder.setModuleName("LOCALIZATION_INIT");
        infoOrignBuilder.addAbnormal(abnormalBuilder);
    }
}