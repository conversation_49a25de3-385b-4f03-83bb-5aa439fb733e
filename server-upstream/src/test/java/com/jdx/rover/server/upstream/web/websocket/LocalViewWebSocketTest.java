package com.jdx.rover.server.upstream.web.websocket;

import cn.hutool.core.util.HexUtil;
import com.jdx.rover.common.constant.NumberConstant;
import jdx.rover.guardian.dto.LocalView;
import lombok.extern.slf4j.Slf4j;
import org.java_websocket.enums.ReadyState;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import java.net.URI;
import java.util.HashMap;
import java.util.Map;


@Slf4j
class LocalViewWebSocketTest {
    @Disabled
    @Test
    void send() throws Exception {
        // 正常驾驶模式
//        String message = "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";
        String message = "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";

        String host = "127.0.0.1:8011";
        // 行云测试站
//        host = "***********:8011";
        // beta环境
//    host = "jdxserver-up-beta.jd.local";
        String url = "ws://" + host + "/upstream/ws/3dmap";
        try {
            Map<String, String> httpHeaders = new HashMap<>();
            httpHeaders.put("vehicleName", "JDXT002");
            TestWebSocketClient myClient = new TestWebSocketClient(new URI(url), httpHeaders);
            myClient.connect();
            while (!myClient.getReadyState().equals(ReadyState.OPEN)) {
                log.info("连接中。。。");
                Thread.sleep(1000);
            }
            for (int i = 0; i < 1000; i++) {
                // 连接成功往websocket服务端发送数据
                sendMessage(myClient, message);
                log.info("发送完成!");
                Thread.sleep(1000);
            }
            myClient.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
        Thread.sleep(1000);
    }

    /**
     * 发送消息
     *
     * @param myClient
     * @param msg
     */
    private void sendMessage(TestWebSocketClient myClient, String msg) throws Exception {
        LocalView.LocalViewDTO dto = LocalView.LocalViewDTO.parseFrom(HexUtil.decodeHex(msg));
        LocalView.LocalViewDTO.Builder infoOrignBuilder = LocalView.LocalViewDTO.newBuilder(dto);
        infoOrignBuilder.setTimestamp(System.currentTimeMillis() * NumberConstant.MILLION);
        infoOrignBuilder.setCreateTimestamp(System.currentTimeMillis() * NumberConstant.MILLION);
        infoOrignBuilder.getAuthInfoBuilder().setVehicleId("JDXT002");
        infoOrignBuilder.getCarBuilder().setVelocity(0);
        LocalView.LocalViewDTO infoOrign = infoOrignBuilder.build();
        byte[] result = infoOrign.toByteArray();
        myClient.send(result);
    }
}