/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.server.upstream.web.websocket;

import com.google.protobuf.util.JsonFormat;
import com.jdx.rover.upstream.proto.GuardianProtoTest;
import jdx.rover.guardian.data.dto.GuardianDataDto;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

import java.lang.reflect.Method;

/**
 * <AUTHOR>
 */
//@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Slf4j
class GuardianWebSocketTest {
  private GuardianWebSocket guardianWebSocket = new GuardianWebSocket();

  @Test
  void printfLog() throws Exception {
    GuardianDataDto.GuardianInfoDTO info = GuardianProtoTest.buildGuardianInfoDTO();
    String guardianInfoDTOStr = JsonFormat.printer().print(info);

//    log.info("guardianInfoDTOStr={}", guardianInfoDTOStr);
    Method checkParameterMethod = guardianWebSocket.getClass().getDeclaredMethod("printfLog", String.class, String.class);
    checkParameterMethod.setAccessible(true);
    checkParameterMethod.invoke(guardianWebSocket, guardianInfoDTOStr, info.getVehicleId());
  }
}