package com.jdx.rover.server.upstream.web.websocket;

import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.util.JsonFormat;
import com.jdx.rover.common.constant.NumberConstant;
import jdx.rover.report.dto.proto.ReportDto;
import jdx.rover.report.statistics.proto.ReportStatistics;
import lombok.extern.slf4j.Slf4j;
import org.java_websocket.enums.ReadyState;
import org.junit.jupiter.api.Test;

import java.net.URI;
import java.util.Objects;

/**
 * 发送guardian消息
 *
 * <AUTHOR>
 */
@Slf4j
class ReportWebSocketSendTest {
    public static final String VEHICLE_NAME = "JDXT002";
    public static final long START_TIME_NANOSECOND = System.currentTimeMillis() * NumberConstant.MILLION;
    //    public static final String MESSAGE = "{\"requestHeader\":{\"messageTypeRequest\":[{\"messageType\":\"ABNORMAL\",\"needResponse\":true,\"retry\":true}],\"requestId\":\"1692002485628000052\",\"vehicleName\":\"ZBJ051\",\"requestTime\":\"1692002485628884975\"},\"reportStatisticsData\":{\"runtimeUuid\":\"1692002429779475130\",\"abnormalData\":[{\"moduleName\":\"ComponentFrequence\",\"errorCode\":-13501,\"errorLevel\":\"FATAL\",\"errorMsg\":\"ErrorCode: ERROR_GUARDIAN_COMPONENT_HZ_NOT_MATCH, Detail: [LOCALIZATION_TRANSFORM MAP_UPDATER hz fatal,].\",\"startTime\":\"1692002485412618657\"}]}}";
//    public static final String MESSAGE = "{\"requestHeader\":{\"messageTypeRequest\":[{\"messageType\":\"ABNORMAL\",\"needResponse\":true,\"retry\":true}],\"requestId\":\"1692002487647000055\",\"vehicleName\":\"ZBJ051\",\"requestTime\":\"1692002487647295187\"},\"reportStatisticsData\":{\"runtimeUuid\":\"1692002429779475130\",\"abnormalData\":[{\"moduleName\":\"ComponentFrequence\",\"errorCode\":-13501,\"errorLevel\":\"FATAL\",\"errorMsg\":\"ErrorCode: ERROR_GUARDIAN_COMPONENT_HZ_NOT_MATCH, Detail: [LOCALIZATION_TRANSFORM MAP_UPDATER hz fatal,].\",\"startTime\":\"1692002485412618657\",\"endTime\":\"1692002487646915825\"}]}}";
    // 启动中
//    public static final String MESSAGE = "{\"requestHeader\":{\"messageTypeRequest\":[{\"messageType\":\"BOOT\",\"needResponse\":true,\"retry\":true}],\"requestId\":\"1692085696286000112\",\"vehicleName\":\"JD0055\",\"requestTime\":\"1692085696286947637\"},\"reportStatisticsData\":{\"runtimeUuid\":\"1692085583949506467\",\"vehicleBoot\":{\"bootId\":\"1\",\"startTime\":\"1692085596697786143\",\"allBootStatus\":\"STARTING\",\"nodeBoot\":[{\"nodeName\":\"Control\",\"ip\":\"***********\",\"startTime\":\"1692085597305794521\",\"endTime\":\"1692085695085794521\",\"bootStatus\":\"STARTING\",\"moduleBoot\":[{\"bootCount\":1,\"moduleBootDetail\":[{\"name\":\"DRIVERS_CHASSIS_RX\",\"startTime\":\"1692085597306379192\",\"endTime\":\"1692085598329379192\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_CHASSIS_TX\",\"startTime\":\"1692085598330470419\",\"endTime\":\"1692085598345470419\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_CAMERA_TRAFFIC_LEFT\",\"startTime\":\"1692085598346476913\",\"endTime\":\"1692085599387476913\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_CAMERA_TRAFFIC_RIGHT\",\"startTime\":\"1692085599388732247\",\"endTime\":\"1692085600404732247\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_JOYSTICK\",\"startTime\":\"1692085600405742537\",\"endTime\":\"1692085600412742537\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"MAP_INITIALIZATION\",\"startTime\":\"1692085600413358903\",\"endTime\":\"1692085646429358903\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"LOCALIZATION_TRANSFORM\",\"startTime\":\"1692085646430202614\",\"endTime\":\"1692085646441202614\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"MAP_UPDATER\",\"startTime\":\"1692085646441808158\",\"endTime\":\"1692085669453808158\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"PERC2_TRAFFIC_LIGHT\",\"startTime\":\"1692085669454898614\",\"endTime\":\"1692085675047898614\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"ROUTING\",\"startTime\":\"1692085675049210125\",\"endTime\":\"1692085695085210125\",\"bootStatus\":\"STARTING\",\"errorCode\":-7019,\"errorMsg\":\"ROUTING init is not ready.\"},{\"name\":\"PREDICTION\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PLANNING\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PNC_STATUS\",\"startTime\":\"9223372036854775807\"},{\"name\":\"CONTROL\",\"startTime\":\"9223372036854775807\"},{\"name\":\"ARTIFICIAL_OBSTACLE\",\"startTime\":\"9223372036854775807\"}]}],\"hardwareModule\":{\"bootCount\":1,\"moduleBootDetail\":[{\"name\":\"SELFCHECK_CONFIG\",\"startTime\":\"1692085519000000000\",\"endTime\":\"1692085519030000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":1013,\"errorMsg\":\"CODE_HARDWARE_CONFIG_SELECT_FINISH\"},{\"name\":\"NETWORK_CHECK\",\"startTime\":\"1692085519000000000\",\"endTime\":\"1692085533670000000\",\"bootStatus\":\"START_FAILED\",\"errorCode\":1205,\"errorMsg\":\"CODE_HARDWARE_NET_ANOTHER_XAVIER_4G_NET_ERR\"},{\"name\":\"USB_CHECK\",\"startTime\":\"1692085519000000000\",\"endTime\":\"1692085519250000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":1305,\"errorMsg\":\"CODE_HARDWARE_USB_CHIP_NUM_CHECK_OK\"},{\"name\":\"PING_CHECK\",\"startTime\":\"1692085523000000000\",\"endTime\":\"1692085527330000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2019,\"errorMsg\":\"network check passed\"},{\"name\":\"CLOCK_SYNC\",\"startTime\":\"1692085559000000000\",\"endTime\":\"1692085594560000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2021,\"errorMsg\":\"clock sync success\"},{\"name\":\"DISK_CHECK\",\"startTime\":\"1692085559000000000\",\"endTime\":\"1692085559100000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2023,\"errorMsg\":\"xavier disk check passed\"},{\"name\":\"MOUNT_NFS\",\"startTime\":\"1692085559000000000\",\"endTime\":\"1692085559040000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2025,\"errorMsg\":\"mount nfs success\"},{\"name\":\"ROVER\",\"startTime\":\"1692085591000000000\",\"endTime\":\"1692085593080000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2018,\"errorMsg\":\"rover5 start finish!\"},{\"name\":\"PREPARE_ENGINE\",\"startTime\":\"1692085577000000000\",\"endTime\":\"1692085591480000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2017,\"errorMsg\":\"generate perception engine finish\"}]}},{\"nodeName\":\"Compute\",\"ip\":\"***********\",\"startTime\":\"1692085596697786143\",\"endTime\":\"1692085676791786143\",\"bootStatus\":\"START_SUCCESS\",\"moduleBoot\":[{\"bootCount\":1,\"moduleBootDetail\":[{\"name\":\"DRIVERS_LIDAR_TOP\",\"startTime\":\"1692085596698264831\",\"endTime\":\"1692085597736264831\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_LIDAR_FRONT\",\"startTime\":\"1692085597737587977\",\"endTime\":\"1692085598744587977\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_LIDAR_RIGHT\",\"startTime\":\"1692085598745274758\",\"endTime\":\"1692085599751274758\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_LIDAR_LEFT\",\"startTime\":\"1692085599752604604\",\"endTime\":\"1692085600758604604\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_IMU\",\"startTime\":\"1692085600759920368\",\"endTime\":\"1692085601766920368\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_GPS_RAC\",\"startTime\":\"1692085601767609485\",\"endTime\":\"1692085603791609485\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_CAMERA360_FRONT\",\"startTime\":\"1692085603792577005\",\"endTime\":\"1692085604836577005\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_CAMERA360_LEFT\",\"startTime\":\"1692085604837683200\",\"endTime\":\"1692085605857683200\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_CAMERA360_RIGHT\",\"startTime\":\"1692085605859146314\",\"endTime\":\"1692085606875146314\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_CAMERA360_REAR\",\"startTime\":\"1692085606876480213\",\"endTime\":\"1692085607895480213\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_LIDAR_REAR\",\"startTime\":\"1692085607897037414\",\"endTime\":\"1692085608902037414\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"MAP_INITIALIZATION\",\"startTime\":\"1692085608903518565\",\"endTime\":\"1692085645920518565\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"LOCALIZATION_IMU\",\"startTime\":\"1692085645921569283\",\"endTime\":\"1692085646932569283\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"LOCALIZATION_INIT\",\"startTime\":\"1692085646933364912\",\"endTime\":\"1692085666185364912\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"LOCALIZATION_LIDAR\",\"startTime\":\"1692085666187503474\",\"endTime\":\"1692085669201503474\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"LOCALIZATION_EKF\",\"startTime\":\"1692085669202686350\",\"endTime\":\"1692085669211686350\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"LOCALIZATION_TRANSFORM\",\"startTime\":\"1692085669212813262\",\"endTime\":\"1692085669217813262\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"PERC2_CLOUD\",\"startTime\":\"1692085669218965839\",\"endTime\":\"1692085671239965839\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"PERC2_GEO\",\"startTime\":\"1692085671240707322\",\"endTime\":\"1692085671259707322\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"PERC2_IMAGE_F\",\"startTime\":\"1692085671261132121\",\"endTime\":\"1692085674803132121\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"PERC2_IMAGE_B\",\"startTime\":\"1692085674804052595\",\"endTime\":\"1692085675644052595\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"PERC2_SOD\",\"startTime\":\"1692085675645435002\",\"endTime\":\"1692085676692435002\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"PERC2_FUSION\",\"startTime\":\"1692085676693433727\",\"endTime\":\"1692085676731433727\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"UNIFORM\",\"startTime\":\"1692085676733289793\",\"endTime\":\"1692085676745289793\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"RECORDER_LITTLE_BAG\",\"startTime\":\"1692085676746055874\",\"endTime\":\"1692085676792055874\",\"bootStatus\":\"START_SUCCESS\"}]}],\"hardwareModule\":{\"bootCount\":1,\"moduleBootDetail\":[{\"name\":\"SELFCHECK_CONFIG\",\"startTime\":\"1692085539000000000\",\"endTime\":\"1692085539300000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":1013,\"errorMsg\":\"CODE_HARDWARE_CONFIG_SELECT_FINISH\"},{\"name\":\"MCU_RESPONSE\",\"startTime\":\"1692085539000000000\",\"endTime\":\"1692085539030000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":1004,\"errorMsg\":\"CODE_HARDWARE_GET_MCU_RESPONSE\"},{\"name\":\"MCU_UPDATE\",\"startTime\":\"1692085539000000000\",\"endTime\":\"1692085539250000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":1005,\"errorMsg\":\"CODE_HARDWARE_MCU_FIRMWARE_UPDATE_SUCCESS\"},{\"name\":\"NETWORK_CHECK\",\"startTime\":\"1692085539000000000\",\"endTime\":\"1692085539000000000\",\"bootStatus\":\"START_FAILED\",\"errorCode\":1211,\"errorMsg\":\"CODE_HARDWARE_NETWORK_CHECK\"},{\"name\":\"USB_CHECK\",\"startTime\":\"1692085539000000000\",\"endTime\":\"1692085539290000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":1305,\"errorMsg\":\"CODE_HARDWARE_USB_CHIP_NUM_CHECK_OK\"},{\"name\":\"PING_CHECK\",\"startTime\":\"1692085540000000000\",\"endTime\":\"1692085540210000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2019,\"errorMsg\":\"network check passed\"},{\"name\":\"CLOCK_SYNC\",\"startTime\":\"1692085559000000000\",\"endTime\":\"1692085578600000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2021,\"errorMsg\":\"clock sync success\"},{\"name\":\"DISK_CHECK\",\"startTime\":\"1692085560000000000\",\"endTime\":\"1692085560110000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2023,\"errorMsg\":\"xavier disk check passed\"},{\"name\":\"MOUNT_NFS\",\"startTime\":\"1692085560000000000\",\"endTime\":\"1692085560000000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2025,\"errorMsg\":\"mount nfs success\"},{\"name\":\"ROVER\",\"startTime\":\"1692085592000000000\",\"endTime\":\"1692085594080000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2018,\"errorMsg\":\"rover5 start finish!\"},{\"name\":\"PREPARE_ENGINE\",\"startTime\":\"1692085578000000000\",\"endTime\":\"1692085592490000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2017,\"errorMsg\":\"generate perception engine finish\"}]}}]}}}";
//    public static final String MESSAGE = "{\"requestHeader\":{\"messageTypeRequest\":[{\"messageType\":\"BOOT\",\"needResponse\":true,\"retry\":true}],\"requestId\":\"1692944602249000218\",\"vehicleName\":\"JDZ0038\",\"requestTime\":\"1692944602249566075\"},\"reportStatisticsData\":{\"runtimeUuid\":\"1692944231919518266\",\"vehicleBoot\":{\"bootId\":\"1\",\"startTime\":\"1692954118228575873\",\"allBootStatus\":\"STARTING\",\"nodeBoot\":[{\"nodeName\":\"Compute\",\"ip\":\"***********\",\"startTime\":\"1692944118228575873\",\"endTime\":\"1692944302048336427\",\"bootStatus\":\"START_FAILED\",\"moduleBoot\":[{\"bootCount\":1,\"moduleBootDetail\":[{\"name\":\"DRIVERS_LIDAR_TOP\",\"startTime\":\"1692944244684684604\",\"endTime\":\"1692944245721684604\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_LIDAR_FRONT\",\"startTime\":\"1692944245723125727\",\"endTime\":\"1692944246729125727\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_LIDAR_RIGHT\",\"startTime\":\"1692944246729711401\",\"endTime\":\"1692944251736711401\",\"bootStatus\":\"START_FAILED\",\"errorCode\":-1309,\"errorMsg\":\"Driver[lidar-right] wait data timeout.\"},{\"name\":\"DRIVERS_LIDAR_LEFT\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_IMU\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_GPS_RAC\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_CAMERA360_FRONT\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_CAMERA360_LEFT\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_CAMERA360_RIGHT\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_CAMERA360_REAR\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_LIDAR_REAR\",\"startTime\":\"9223372036854775807\"},{\"name\":\"MAP_INITIALIZATION\",\"startTime\":\"9223372036854775807\"},{\"name\":\"LOCALIZATION_IMU\",\"startTime\":\"9223372036854775807\"},{\"name\":\"LOCALIZATION_INIT\",\"startTime\":\"9223372036854775807\"},{\"name\":\"LOCALIZATION_LIDAR\",\"startTime\":\"9223372036854775807\"},{\"name\":\"LOCALIZATION_EKF\",\"startTime\":\"9223372036854775807\"},{\"name\":\"LOCALIZATION_TRANSFORM\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_CLOUD\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_GEO\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_IMAGE_F\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_IMAGE_B\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_SOD\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_FUSION\",\"startTime\":\"9223372036854775807\"},{\"name\":\"UNIFORM\",\"startTime\":\"9223372036854775807\"},{\"name\":\"RECORDER_LITTLE_BAG\",\"startTime\":\"9223372036854775807\"}]},{\"bootCount\":2,\"moduleBootDetail\":[{\"name\":\"DRIVERS_LIDAR_TOP\",\"startTime\":\"1692944254825562907\",\"endTime\":\"1692944255830562907\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_LIDAR_FRONT\",\"startTime\":\"1692944255831844690\",\"endTime\":\"1692944256835844690\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_LIDAR_RIGHT\",\"startTime\":\"1692944256837654928\",\"endTime\":\"1692944261842654928\",\"bootStatus\":\"START_FAILED\",\"errorCode\":-1309,\"errorMsg\":\"Driver[lidar-right] wait data timeout.\"},{\"name\":\"DRIVERS_LIDAR_LEFT\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_IMU\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_GPS_RAC\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_CAMERA360_FRONT\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_CAMERA360_LEFT\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_CAMERA360_RIGHT\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_CAMERA360_REAR\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_LIDAR_REAR\",\"startTime\":\"9223372036854775807\"},{\"name\":\"MAP_INITIALIZATION\",\"startTime\":\"9223372036854775807\"},{\"name\":\"LOCALIZATION_IMU\",\"startTime\":\"9223372036854775807\"},{\"name\":\"LOCALIZATION_INIT\",\"startTime\":\"9223372036854775807\"},{\"name\":\"LOCALIZATION_LIDAR\",\"startTime\":\"9223372036854775807\"},{\"name\":\"LOCALIZATION_EKF\",\"startTime\":\"9223372036854775807\"},{\"name\":\"LOCALIZATION_TRANSFORM\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_CLOUD\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_GEO\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_IMAGE_F\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_IMAGE_B\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_SOD\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_FUSION\",\"startTime\":\"9223372036854775807\"},{\"name\":\"UNIFORM\",\"startTime\":\"9223372036854775807\"},{\"name\":\"RECORDER_LITTLE_BAG\",\"startTime\":\"9223372036854775807\"}]},{\"bootCount\":3,\"moduleBootDetail\":[{\"name\":\"DRIVERS_LIDAR_TOP\",\"startTime\":\"1692944274926910876\",\"endTime\":\"1692944275930910876\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_LIDAR_FRONT\",\"startTime\":\"1692944275932455512\",\"endTime\":\"1692944276936455512\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_LIDAR_RIGHT\",\"startTime\":\"1692944276937786939\",\"endTime\":\"1692944281941786939\",\"bootStatus\":\"START_FAILED\",\"errorCode\":-1309,\"errorMsg\":\"Driver[lidar-right] wait data timeout.\"},{\"name\":\"DRIVERS_LIDAR_LEFT\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_IMU\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_GPS_RAC\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_CAMERA360_FRONT\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_CAMERA360_LEFT\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_CAMERA360_RIGHT\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_CAMERA360_REAR\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_LIDAR_REAR\",\"startTime\":\"9223372036854775807\"},{\"name\":\"MAP_INITIALIZATION\",\"startTime\":\"9223372036854775807\"},{\"name\":\"LOCALIZATION_IMU\",\"startTime\":\"9223372036854775807\"},{\"name\":\"LOCALIZATION_INIT\",\"startTime\":\"9223372036854775807\"},{\"name\":\"LOCALIZATION_LIDAR\",\"startTime\":\"9223372036854775807\"},{\"name\":\"LOCALIZATION_EKF\",\"startTime\":\"9223372036854775807\"},{\"name\":\"LOCALIZATION_TRANSFORM\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_CLOUD\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_GEO\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_IMAGE_F\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_IMAGE_B\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_SOD\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_FUSION\",\"startTime\":\"9223372036854775807\"},{\"name\":\"UNIFORM\",\"startTime\":\"9223372036854775807\"},{\"name\":\"RECORDER_LITTLE_BAG\",\"startTime\":\"9223372036854775807\"}]},{\"bootCount\":4,\"moduleBootDetail\":[{\"name\":\"DRIVERS_LIDAR_TOP\",\"startTime\":\"1692944295030795787\",\"endTime\":\"1692944296034795787\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_LIDAR_FRONT\",\"startTime\":\"1692944296036723088\",\"endTime\":\"1692944297040723088\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_LIDAR_RIGHT\",\"startTime\":\"1692944297042879213\",\"endTime\":\"1692944302047879213\",\"bootStatus\":\"START_FAILED\",\"errorCode\":-1309,\"errorMsg\":\"Driver[lidar-right] wait data timeout.\"},{\"name\":\"DRIVERS_LIDAR_LEFT\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_IMU\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_GPS_RAC\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_CAMERA360_FRONT\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_CAMERA360_LEFT\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_CAMERA360_RIGHT\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_CAMERA360_REAR\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_LIDAR_REAR\",\"startTime\":\"9223372036854775807\"},{\"name\":\"MAP_INITIALIZATION\",\"startTime\":\"9223372036854775807\"},{\"name\":\"LOCALIZATION_IMU\",\"startTime\":\"9223372036854775807\"},{\"name\":\"LOCALIZATION_INIT\",\"startTime\":\"9223372036854775807\"},{\"name\":\"LOCALIZATION_LIDAR\",\"startTime\":\"9223372036854775807\"},{\"name\":\"LOCALIZATION_EKF\",\"startTime\":\"9223372036854775807\"},{\"name\":\"LOCALIZATION_TRANSFORM\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_CLOUD\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_GEO\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_IMAGE_F\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_IMAGE_B\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_SOD\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_FUSION\",\"startTime\":\"9223372036854775807\"},{\"name\":\"UNIFORM\",\"startTime\":\"9223372036854775807\"},{\"name\":\"RECORDER_LITTLE_BAG\",\"startTime\":\"9223372036854775807\"}]}],\"hardwareModule\":{\"bootCount\":1,\"moduleBootDetail\":[{\"name\":\"SELFCHECK_CONFIG\",\"startTime\":\"1692944183000000000\",\"endTime\":\"1692944183310000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":1013,\"errorMsg\":\"CODE_HARDWARE_CONFIG_SELECT_FINISH\"},{\"name\":\"MCU_RESPONSE\",\"startTime\":\"1692944183000000000\",\"endTime\":\"1692944183050000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":1004,\"errorMsg\":\"CODE_HARDWARE_GET_MCU_RESPONSE\"},{\"name\":\"MCU_UPDATE\",\"startTime\":\"1692944183000000000\",\"endTime\":\"1692944183270000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":1005,\"errorMsg\":\"CODE_HARDWARE_MCU_FIRMWARE_UPDATE_SUCCESS\"},{\"name\":\"NETWORK_CHECK\",\"startTime\":\"1692944183000000000\",\"endTime\":\"1692944183000000000\",\"bootStatus\":\"START_FAILED\",\"errorCode\":1211,\"errorMsg\":\"CODE_HARDWARE_NETWORK_CHECK\"},{\"name\":\"USB_CHECK\",\"startTime\":\"1692944183000000000\",\"endTime\":\"1692944183270000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":1305,\"errorMsg\":\"CODE_HARDWARE_USB_CHIP_NUM_CHECK_OK\"},{\"name\":\"PING_CHECK\",\"startTime\":\"1692944184000000000\",\"endTime\":\"1692944184200000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2019,\"errorMsg\":\"network check passed\"},{\"name\":\"CLOCK_SYNC\",\"startTime\":\"1692944201000000000\",\"endTime\":\"1692944217830000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2021,\"errorMsg\":\"clock sync success\"},{\"name\":\"DISK_CHECK\",\"startTime\":\"1692944201000000000\",\"endTime\":\"1692944201110000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2023,\"errorMsg\":\"xavier disk check passed\"},{\"name\":\"MOUNT_NFS\",\"startTime\":\"1692944201000000000\",\"endTime\":\"1692944201000000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2025,\"errorMsg\":\"mount nfs success\"},{\"name\":\"IMAGE_UPGRADE\",\"startTime\":\"1692944208000000000\",\"endTime\":\"1692944214100000000\",\"bootStatus\":\"START_FAILED\",\"errorCode\":2026,\"errorMsg\":\"image upgrade success\"},{\"name\":\"ROVER\",\"startTime\":\"1692944240000000000\",\"endTime\":\"1692944242080000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2018,\"errorMsg\":\"rover5 start finish!\"},{\"name\":\"PREPARE_ENGINE\",\"startTime\":\"1692944226000000000\",\"endTime\":\"1692944240480000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2017,\"errorMsg\":\"generate perception engine finish\"}]}},{\"nodeName\":\"Control\",\"ip\":\"***********\",\"startTime\":\"1692944118228575873\",\"endTime\":\"1692944601438123416\",\"bootStatus\":\"STARTING\",\"moduleBoot\":[{\"bootCount\":1,\"moduleBootDetail\":[{\"name\":\"DRIVERS_CHASSIS_RX\",\"startTime\":\"1692944244129119620\",\"endTime\":\"1692944245150119620\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_CHASSIS_TX\",\"startTime\":\"1692944245151499768\",\"endTime\":\"1692944245166499768\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_CAMERA_TRAFFIC_LEFT\",\"startTime\":\"1692944245167460327\",\"endTime\":\"1692944246200460327\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_CAMERA_TRAFFIC_RIGHT\",\"startTime\":\"1692944246201000495\",\"endTime\":\"1692944247220000495\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_JOYSTICK\",\"startTime\":\"1692944247220995885\",\"endTime\":\"1692944247226995885\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"MAP_INITIALIZATION\",\"startTime\":\"1692944247228092866\",\"endTime\":\"1692944254239092866\",\"bootStatus\":\"SHUT_DOWN_SUCCESS\",\"errorCode\":-2000,\"errorMsg\":\"Map initialization is not ready.\"},{\"name\":\"LOCALIZATION_TRANSFORM\",\"startTime\":\"9223372036854775807\"},{\"name\":\"MAP_UPDATER\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_TRAFFIC_LIGHT\",\"startTime\":\"9223372036854775807\"},{\"name\":\"ROUTING\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PREDICTION\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PLANNING\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PNC_STATUS\",\"startTime\":\"9223372036854775807\"},{\"name\":\"CONTROL\",\"startTime\":\"9223372036854775807\"},{\"name\":\"ARTIFICIAL_OBSTACLE\",\"startTime\":\"9223372036854775807\"}]},{\"bootCount\":2,\"moduleBootDetail\":[{\"name\":\"DRIVERS_CHASSIS_RX\",\"startTime\":\"1692944258260720726\",\"endTime\":\"1692944259265720726\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_CHASSIS_TX\",\"startTime\":\"1692944259267130597\",\"endTime\":\"1692944259273130597\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_CAMERA_TRAFFIC_LEFT\",\"startTime\":\"1692944259274314810\",\"endTime\":\"1692944260277314810\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_CAMERA_TRAFFIC_RIGHT\",\"startTime\":\"1692944260278354278\",\"endTime\":\"1692944261281354278\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_JOYSTICK\",\"startTime\":\"1692944261282843105\",\"endTime\":\"1692944261285843105\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"MAP_INITIALIZATION\",\"startTime\":\"1692944261286634316\",\"endTime\":\"1692944274293634316\",\"bootStatus\":\"SHUT_DOWN_SUCCESS\",\"errorCode\":-2000,\"errorMsg\":\"Map initialization is not ready.\"},{\"name\":\"LOCALIZATION_TRANSFORM\",\"startTime\":\"9223372036854775807\"},{\"name\":\"MAP_UPDATER\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_TRAFFIC_LIGHT\",\"startTime\":\"9223372036854775807\"},{\"name\":\"ROUTING\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PREDICTION\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PLANNING\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PNC_STATUS\",\"startTime\":\"9223372036854775807\"},{\"name\":\"CONTROL\",\"startTime\":\"9223372036854775807\"},{\"name\":\"ARTIFICIAL_OBSTACLE\",\"startTime\":\"9223372036854775807\"}]},{\"bootCount\":3,\"moduleBootDetail\":[{\"name\":\"DRIVERS_CHASSIS_RX\",\"startTime\":\"1692944279308032191\",\"endTime\":\"1692944280313032191\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_CHASSIS_TX\",\"startTime\":\"1692944280314371841\",\"endTime\":\"1692944280320371841\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_CAMERA_TRAFFIC_LEFT\",\"startTime\":\"1692944280321388087\",\"endTime\":\"1692944281323388087\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_CAMERA_TRAFFIC_RIGHT\",\"startTime\":\"1692944281325034241\",\"endTime\":\"1692944282328034241\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_JOYSTICK\",\"startTime\":\"1692944282329598600\",\"endTime\":\"1692944282333598600\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"MAP_INITIALIZATION\",\"startTime\":\"1692944282334677846\",\"endTime\":\"1692944294341677846\",\"bootStatus\":\"SHUT_DOWN_SUCCESS\",\"errorCode\":-2000,\"errorMsg\":\"Map initialization is not ready.\"},{\"name\":\"LOCALIZATION_TRANSFORM\",\"startTime\":\"9223372036854775807\"},{\"name\":\"MAP_UPDATER\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_TRAFFIC_LIGHT\",\"startTime\":\"9223372036854775807\"},{\"name\":\"ROUTING\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PREDICTION\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PLANNING\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PNC_STATUS\",\"startTime\":\"9223372036854775807\"},{\"name\":\"CONTROL\",\"startTime\":\"9223372036854775807\"},{\"name\":\"ARTIFICIAL_OBSTACLE\",\"startTime\":\"9223372036854775807\"}]},{\"bootCount\":4,\"moduleBootDetail\":[{\"name\":\"DRIVERS_CHASSIS_RX\",\"startTime\":\"1692944299359886074\",\"endTime\":\"1692944300364886074\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_CHASSIS_TX\",\"startTime\":\"1692944300366349191\",\"endTime\":\"1692944300372349191\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_CAMERA_TRAFFIC_LEFT\",\"startTime\":\"1692944300373384539\",\"endTime\":\"1692944301375384539\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_CAMERA_TRAFFIC_RIGHT\",\"startTime\":\"1692944301376441084\",\"endTime\":\"1692944302379441084\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_JOYSTICK\",\"startTime\":\"1692944302380810371\",\"endTime\":\"1692944302383810371\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"MAP_INITIALIZATION\",\"startTime\":\"1692944302384700591\",\"endTime\":\"1692944601437700591\",\"bootStatus\":\"START_FAILED\",\"errorCode\":-2000,\"errorMsg\":\"Map initialization is not ready.\"},{\"name\":\"LOCALIZATION_TRANSFORM\",\"startTime\":\"9223372036854775807\"},{\"name\":\"MAP_UPDATER\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_TRAFFIC_LIGHT\",\"startTime\":\"9223372036854775807\"},{\"name\":\"ROUTING\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PREDICTION\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PLANNING\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PNC_STATUS\",\"startTime\":\"9223372036854775807\"},{\"name\":\"CONTROL\",\"startTime\":\"9223372036854775807\"},{\"name\":\"ARTIFICIAL_OBSTACLE\",\"startTime\":\"9223372036854775807\"}]}],\"hardwareModule\":{\"bootCount\":1,\"moduleBootDetail\":[{\"name\":\"SELFCHECK_CONFIG\",\"startTime\":\"1692944152000000000\",\"endTime\":\"1692944152030000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":1013,\"errorMsg\":\"CODE_HARDWARE_CONFIG_SELECT_FINISH\"},{\"name\":\"NETWORK_CHECK\",\"startTime\":\"1692944152000000000\",\"endTime\":\"1692944165790000000\",\"bootStatus\":\"START_FAILED\",\"errorCode\":1205,\"errorMsg\":\"CODE_HARDWARE_NET_ANOTHER_XAVIER_4G_NET_ERR\"},{\"name\":\"USB_CHECK\",\"startTime\":\"1692944152000000000\",\"endTime\":\"1692944152260000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":1305,\"errorMsg\":\"CODE_HARDWARE_USB_CHIP_NUM_CHECK_OK\"},{\"name\":\"PING_CHECK\",\"startTime\":\"1692944153000000000\",\"endTime\":\"1692944153190000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2019,\"errorMsg\":\"network check passed\"},{\"name\":\"CLOCK_SYNC\",\"startTime\":\"1692944201000000000\",\"endTime\":\"1692944248970000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2021,\"errorMsg\":\"clock sync success\"},{\"name\":\"DISK_CHECK\",\"startTime\":\"1692944201000000000\",\"endTime\":\"1692944201110000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2023,\"errorMsg\":\"xavier disk check passed\"},{\"name\":\"MOUNT_NFS\",\"startTime\":\"1692944201000000000\",\"endTime\":\"1692944201030000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2025,\"errorMsg\":\"mount nfs success\"},{\"name\":\"IMAGE_UPGRADE\",\"startTime\":\"1692944208000000000\",\"endTime\":\"1692944214430000000\",\"bootStatus\":\"START_FAILED\",\"errorCode\":2026,\"errorMsg\":\"image upgrade success\"},{\"name\":\"ROVER\",\"startTime\":\"1692944240000000000\",\"endTime\":\"1692944242080000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2018,\"errorMsg\":\"rover5 start finish!\"},{\"name\":\"PREPARE_ENGINE\",\"startTime\":\"1692944225000000000\",\"endTime\":\"1692944239480000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2017,\"errorMsg\":\"generate perception engine finish\"}]}}]}}}";

    // 启动成功
//    public static final String MESSAGE = "{\"requestHeader\":{\"messageTypeRequest\":[{\"messageType\":\"BOOT\",\"needResponse\":true,\"retry\":true}],\"requestId\":\"1692090630709000759\",\"vehicleName\":\"JDZ0002\",\"requestTime\":\"1692090630709045425\"},\"reportStatisticsData\":{\"runtimeUuid\":\"1692059052472112150\",\"vehicleBoot\":{\"bootId\":\"2\",\"startTime\":\"1692090492357904879\",\"endTime\":\"1692090628942697275\",\"allBootStatus\":\"START_SUCCESS\",\"nodeBoot\":[{\"nodeName\":\"Control\",\"ip\":\"***********\",\"startTime\":\"1692090492808697275\",\"endTime\":\"1692090628942697275\",\"bootStatus\":\"START_SUCCESS\",\"moduleBoot\":[{\"bootCount\":2,\"moduleBootDetail\":[{\"name\":\"DRIVERS_CHASSIS_RX\",\"startTime\":\"1692090492809276824\",\"endTime\":\"1692090493817276824\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_CHASSIS_TX\",\"startTime\":\"1692090493818108575\",\"endTime\":\"1692090493825108575\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"MAP_INITIALIZATION\",\"startTime\":\"1692090493826137617\",\"endTime\":\"1692090525835137617\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"LOCALIZATION_TRANSFORM\",\"startTime\":\"1692090525836444429\",\"endTime\":\"1692090525842444429\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"MAP_UPDATER\",\"startTime\":\"1692090525843722079\",\"endTime\":\"1692090595858722079\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_CAMERA_TRAFFIC_LEFT\",\"startTime\":\"1692090595859553705\",\"endTime\":\"1692090596863553705\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_CAMERA_TRAFFIC_RIGHT\",\"startTime\":\"1692090596864625685\",\"endTime\":\"1692090597869625685\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"PERC2_TRAFFIC_LIGHT\",\"startTime\":\"1692090597870541179\",\"endTime\":\"1692090598999541179\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"ROUTING\",\"startTime\":\"1692090599000275261\",\"endTime\":\"1692090627015275261\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"PREDICTION\",\"startTime\":\"1692090627016335147\",\"endTime\":\"1692090627027335147\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"PLANNING\",\"startTime\":\"1692090627028543967\",\"endTime\":\"1692090627872543967\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"PNC_STATUS\",\"startTime\":\"1692090627874010984\",\"endTime\":\"1692090627882010984\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"CONTROL\",\"startTime\":\"1692090627882682482\",\"endTime\":\"1692090627917682482\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"ARTIFICIAL_OBSTACLE\",\"startTime\":\"1692090627918492469\",\"endTime\":\"1692090628925492469\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_JOYSTICK\",\"startTime\":\"1692090628926459632\",\"endTime\":\"1692090628934459632\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_V2X_TX\",\"startTime\":\"1692090628935401815\",\"endTime\":\"1692090628938401815\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_V2X_RX\",\"startTime\":\"1692090628939292958\",\"endTime\":\"1692090628942292958\",\"bootStatus\":\"START_SUCCESS\"}]}],\"hardwareModule\":{\"bootCount\":1,\"moduleBootDetail\":[{\"name\":\"SELFCHECK_CONFIG\",\"startTime\":\"1692059010000000000\",\"endTime\":\"1692059010030000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":1013,\"errorMsg\":\"CODE_HARDWARE_CONFIG_SELECT_FINISH\"},{\"name\":\"MCU_RESPONSE\",\"startTime\":\"1692059010000000000\",\"endTime\":\"1692059010040000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":1004,\"errorMsg\":\"CODE_HARDWARE_GET_MCU_RESPONSE\"},{\"name\":\"MCU_UPDATE\",\"startTime\":\"1692059010000000000\",\"endTime\":\"1692059010230000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":1005,\"errorMsg\":\"CODE_HARDWARE_MCU_FIRMWARE_UPDATE_SUCCESS\"},{\"name\":\"NETWORK_CHECK\",\"startTime\":\"1692059010000000000\",\"endTime\":\"1692059010000000000\",\"bootStatus\":\"START_FAILED\",\"errorCode\":1211,\"errorMsg\":\"CODE_HARDWARE_NETWORK_CHECK\"},{\"name\":\"USB_CHECK\",\"startTime\":\"1692059010000000000\",\"endTime\":\"1692059010270000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":1305,\"errorMsg\":\"CODE_HARDWARE_USB_CHIP_NUM_CHECK_OK\"},{\"name\":\"PING_CHECK\",\"startTime\":\"1692059010000000000\",\"endTime\":\"1692059010230000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2019,\"errorMsg\":\"network check passed\"},{\"name\":\"CLOCK_SYNC\",\"startTime\":\"1692059027000000000\",\"endTime\":\"1692059043820000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2021,\"errorMsg\":\"clock sync success\"},{\"name\":\"DISK_CHECK\",\"startTime\":\"1692059028000000000\",\"endTime\":\"1692059028110000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2023,\"errorMsg\":\"xavier disk check passed\"},{\"name\":\"MOUNT_NFS\",\"startTime\":\"1692059028000000000\",\"endTime\":\"1692059028040000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2025,\"errorMsg\":\"mount nfs success\"},{\"name\":\"ROVER\",\"startTime\":\"1692059060000000000\",\"endTime\":\"1692059062080000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2018,\"errorMsg\":\"rover5 start finish!\"},{\"name\":\"PREPARE_ENGINE\",\"startTime\":\"1692059046000000000\",\"endTime\":\"1692059060480000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2017,\"errorMsg\":\"generate perception engine finish\"}]}},{\"nodeName\":\"Compute\",\"ip\":\"***********\",\"startTime\":\"1692090492357904879\",\"endTime\":\"1692090605065904879\",\"bootStatus\":\"START_SUCCESS\",\"moduleBoot\":[{\"bootCount\":2,\"moduleBootDetail\":[{\"name\":\"DRIVERS_LIDAR_TOP\",\"startTime\":\"1692090492358537615\",\"endTime\":\"1692090493363537615\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_LIDAR_FRONT\",\"startTime\":\"1692090493365588676\",\"endTime\":\"1692090494371588676\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_LIDAR_RIGHT\",\"startTime\":\"1692090494373700082\",\"endTime\":\"1692090495379700082\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_LIDAR_LEFT\",\"startTime\":\"1692090495381160247\",\"endTime\":\"1692090496388160247\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_IMU\",\"startTime\":\"1692090496389216816\",\"endTime\":\"1692090497393216816\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_GPS_RAC\",\"startTime\":\"1692090497395373410\",\"endTime\":\"1692090498402373410\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"MAP_INITIALIZATION\",\"startTime\":\"1692090498404074457\",\"endTime\":\"1692090532416074457\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"LOCALIZATION_IMU\",\"startTime\":\"1692090532418113541\",\"endTime\":\"1692090533429113541\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"LOCALIZATION_INIT\",\"startTime\":\"1692090533430867377\",\"endTime\":\"1692090591825867377\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"LOCALIZATION_LIDAR\",\"startTime\":\"1692090591827428224\",\"endTime\":\"1692090594837428224\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"LOCALIZATION_EKF\",\"startTime\":\"1692090594839037660\",\"endTime\":\"1692090594847037660\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"LOCALIZATION_TRANSFORM\",\"startTime\":\"1692090594848325980\",\"endTime\":\"1692090594851325980\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_CAMERA360_FRONT\",\"startTime\":\"1692090594851968508\",\"endTime\":\"1692090595856968508\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_CAMERA360_REAR\",\"startTime\":\"1692090595857668082\",\"endTime\":\"1692090596860668082\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_LIDAR_REAR\",\"startTime\":\"1692090596861999170\",\"endTime\":\"1692090597866999170\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"PERC2_CLOUD\",\"startTime\":\"1692090597868535776\",\"endTime\":\"1692090599884535776\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"PERC2_GEO\",\"startTime\":\"1692090599885527934\",\"endTime\":\"1692090599895527934\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"PERC2_IMAGE_F\",\"startTime\":\"1692090599896649885\",\"endTime\":\"1692090603247649885\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"PERC2_IMAGE_B\",\"startTime\":\"1692090603249020515\",\"endTime\":\"1692090604017020515\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"PERC2_SOD\",\"startTime\":\"1692090604018920490\",\"endTime\":\"1692090605036920490\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"PERC2_FUSION\",\"startTime\":\"1692090605038433481\",\"endTime\":\"1692090605054433481\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"UNIFORM\",\"startTime\":\"1692090605056069385\",\"endTime\":\"1692090605066069385\",\"bootStatus\":\"START_SUCCESS\"}]}],\"hardwareModule\":{\"bootCount\":1,\"moduleBootDetail\":[{\"name\":\"SELFCHECK_CONFIG\",\"startTime\":\"1626893525000000000\",\"endTime\":\"1626893525020000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":1013,\"errorMsg\":\"CODE_HARDWARE_CONFIG_SELECT_FINISH\"},{\"name\":\"MCU_RESPONSE\",\"startTime\":\"1692059006000000000\",\"endTime\":\"1692059006030000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":1004,\"errorMsg\":\"CODE_HARDWARE_GET_MCU_RESPONSE\"},{\"name\":\"MCU_UPDATE\",\"startTime\":\"1692059011000000000\",\"endTime\":\"1692059011220000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":1005,\"errorMsg\":\"CODE_HARDWARE_MCU_FIRMWARE_UPDATE_SUCCESS\"},{\"name\":\"NETWORK_CHECK\",\"startTime\":\"1692059011000000000\",\"endTime\":\"1692059011020000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":1201,\"errorMsg\":\"CODE_HARDWARE_CLOSE_NETWORK_CHECK\"},{\"name\":\"USB_CHECK\",\"startTime\":\"1692059011000000000\",\"endTime\":\"1692059016590000000\",\"bootStatus\":\"START_FAILED\",\"errorCode\":1302,\"errorMsg\":\"CODE_HARDWARE_CAM_NUM_ERR\"},{\"name\":\"PING_CHECK\",\"startTime\":\"1692059011000000000\",\"endTime\":\"1692059011220000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2019,\"errorMsg\":\"network check passed\"},{\"name\":\"CLOCK_SYNC\",\"startTime\":\"1692059027000000000\",\"endTime\":\"1692059043070000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2021,\"errorMsg\":\"clock sync success\"},{\"name\":\"DISK_CHECK\",\"startTime\":\"1692059028000000000\",\"endTime\":\"1692059028110000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2023,\"errorMsg\":\"xavier disk check passed\"},{\"name\":\"MOUNT_NFS\",\"startTime\":\"1692059028000000000\",\"endTime\":\"1692059028000000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2025,\"errorMsg\":\"mount nfs success\"},{\"name\":\"ROVER\",\"startTime\":\"1692059061000000000\",\"endTime\":\"1692059063090000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2018,\"errorMsg\":\"rover5 start finish!\"},{\"name\":\"PREPARE_ENGINE\",\"startTime\":\"1692059046000000000\",\"endTime\":\"1692059060490000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2017,\"errorMsg\":\"generate perception engine finish\"}]}}]}}}";
//    public static final String MESSAGE = "{\"requestHeader\":{\"messageTypeRequest\":[{\"messageType\":\"BOOT\",\"needResponse\":true,\"retry\":true}],\"requestId\":\"1693884277508000225\",\"vehicleName\":\"JDZ0038\",\"requestTime\":\"1693884277508555856\"},\"reportStatisticsData\":{\"runtimeUuid\":\"1693884057178211604\",\"vehicleBoot\":{\"bootId\":\"1\",\"startTime\":\"1693883967459906273\",\"endTime\":\"1693884276168416742\",\"allBootStatus\":\"START_SUCCESS\",\"nodeBoot\":[{\"nodeName\":\"Control\",\"ip\":\"***********\",\"startTime\":\"1693883967459906273\",\"endTime\":\"1693884276168416742\",\"bootStatus\":\"START_SUCCESS\",\"moduleBoot\":[{\"bootCount\":1,\"moduleBootDetail\":[{\"name\":\"DRIVERS_CHASSIS_RX\",\"startTime\":\"1693884069688855368\",\"endTime\":\"1693884070710855368\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_CHASSIS_TX\",\"startTime\":\"1693884070712167987\",\"endTime\":\"1693884070726167987\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_CAMERA_TRAFFIC_LEFT\",\"startTime\":\"1693884070727257761\",\"endTime\":\"1693884071774257761\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_CAMERA_TRAFFIC_RIGHT\",\"startTime\":\"1693884071774992084\",\"endTime\":\"1693884072793992084\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_JOYSTICK\",\"startTime\":\"1693884072794725287\",\"endTime\":\"1693884072801725287\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"MAP_INITIALIZATION\",\"startTime\":\"1693884072802275646\",\"endTime\":\"1693884117819275646\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"LOCALIZATION_TRANSFORM\",\"startTime\":\"1693884117820708660\",\"endTime\":\"1693884117832708660\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"MAP_UPDATER\",\"startTime\":\"1693884117833825597\",\"endTime\":\"1693884230854825597\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"PERC2_TRAFFIC_LIGHT\",\"startTime\":\"1693884230855903747\",\"endTime\":\"1693884236990903747\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"ROUTING\",\"startTime\":\"1693884236991569033\",\"endTime\":\"1693884274020569033\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"PREDICTION\",\"startTime\":\"1693884274021328200\",\"endTime\":\"1693884274040328200\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"PLANNING\",\"startTime\":\"1693884274041192575\",\"endTime\":\"1693884275073192575\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"PNC_STATUS\",\"startTime\":\"1693884275074013917\",\"endTime\":\"1693884275089013917\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"CONTROL\",\"startTime\":\"1693884275089414983\",\"endTime\":\"1693884275151414983\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"ARTIFICIAL_OBSTACLE\",\"startTime\":\"1693884275152155987\",\"endTime\":\"1693884276168155987\",\"bootStatus\":\"START_SUCCESS\"}]}],\"hardwareModule\":{\"bootCount\":1,\"moduleBootDetail\":[{\"name\":\"SELFCHECK_CONFIG\",\"startTime\":\"1693884001000000000\",\"endTime\":\"1693884001030000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":1013,\"errorMsg\":\"CODE_HARDWARE_CONFIG_SELECT_FINISH\"},{\"name\":\"NETWORK_CHECK\",\"startTime\":\"1693884002000000000\",\"endTime\":\"1693884017410000000\",\"bootStatus\":\"START_FAILED\",\"errorCode\":1205,\"errorMsg\":\"CODE_HARDWARE_NET_ANOTHER_XAVIER_4G_NET_ERR\"},{\"name\":\"USB_CHECK\",\"startTime\":\"1693884002000000000\",\"endTime\":\"1693884002250000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":1305,\"errorMsg\":\"CODE_HARDWARE_USB_CHIP_NUM_CHECK_OK\"},{\"name\":\"PING_CHECK\",\"startTime\":\"1693884002000000000\",\"endTime\":\"1693884002200000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2019,\"errorMsg\":\"network check passed\"},{\"name\":\"CLOCK_SYNC\",\"startTime\":\"1693884031000000000\",\"endTime\":\"1693884060270000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2021,\"errorMsg\":\"clock sync success\"},{\"name\":\"DISK_CHECK\",\"startTime\":\"1693884031000000000\",\"endTime\":\"1693884031100000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2023,\"errorMsg\":\"xavier disk check passed\"},{\"name\":\"MOUNT_NFS\",\"startTime\":\"1693884032000000000\",\"endTime\":\"1693884032040000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2025,\"errorMsg\":\"mount nfs success\"},{\"name\":\"ROVER\",\"startTime\":\"1693884065000000000\",\"endTime\":\"1693884067090000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2018,\"errorMsg\":\"rover5 start finish!\"},{\"name\":\"PREPARE_ENGINE\",\"startTime\":\"1693884051000000000\",\"endTime\":\"1693884065470000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2017,\"errorMsg\":\"generate perception engine finish\"}]}},{\"nodeName\":\"Compute\",\"ip\":\"***********\",\"startTime\":\"1693883967459906273\",\"endTime\":\"1693884238019795736\",\"bootStatus\":\"START_SUCCESS\",\"moduleBoot\":[{\"bootCount\":1,\"moduleBootDetail\":[{\"name\":\"DRIVERS_LIDAR_TOP\",\"startTime\":\"1693884069727339448\",\"endTime\":\"1693884070764339448\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_LIDAR_FRONT\",\"startTime\":\"1693884070765160666\",\"endTime\":\"1693884071772160666\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_LIDAR_RIGHT\",\"startTime\":\"1693884071773123828\",\"endTime\":\"1693884072778123828\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_LIDAR_LEFT\",\"startTime\":\"1693884072779452854\",\"endTime\":\"1693884073784452854\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_IMU\",\"startTime\":\"1693884073786148708\",\"endTime\":\"1693884074804148708\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_GPS_RAC\",\"startTime\":\"1693884074805035642\",\"endTime\":\"1693884076821035642\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_CAMERA360_FRONT\",\"startTime\":\"1693884076822307659\",\"endTime\":\"1693884077873307659\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_CAMERA360_LEFT\",\"startTime\":\"1693884077874408484\",\"endTime\":\"1693884078899408484\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_CAMERA360_RIGHT\",\"startTime\":\"1693884078900325356\",\"endTime\":\"1693884079928325356\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_CAMERA360_REAR\",\"startTime\":\"1693884079929412613\",\"endTime\":\"1693884080950412613\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_LIDAR_REAR\",\"startTime\":\"1693884080951342367\",\"endTime\":\"1693884081960342367\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"MAP_INITIALIZATION\",\"startTime\":\"1693884081961393739\",\"endTime\":\"1693884112977393739\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"LOCALIZATION_IMU\",\"startTime\":\"1693884112978374837\",\"endTime\":\"1693884113988374837\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"LOCALIZATION_INIT\",\"startTime\":\"1693884113989919472\",\"endTime\":\"1693884224608919472\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"LOCALIZATION_LIDAR\",\"startTime\":\"1693884224610106688\",\"endTime\":\"1693884230631106688\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"LOCALIZATION_EKF\",\"startTime\":\"1693884230632052748\",\"endTime\":\"1693884230641052748\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"LOCALIZATION_TRANSFORM\",\"startTime\":\"1693884230641877836\",\"endTime\":\"1693884230647877836\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"PERC2_CLOUD\",\"startTime\":\"1693884230648502860\",\"endTime\":\"1693884232674502860\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"PERC2_GEO\",\"startTime\":\"1693884232675325582\",\"endTime\":\"1693884232696325582\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"PERC2_IMAGE_F\",\"startTime\":\"1693884232697767533\",\"endTime\":\"1693884236033767533\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"PERC2_IMAGE_B\",\"startTime\":\"1693884236034406532\",\"endTime\":\"1693884236921406532\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"PERC2_SOD\",\"startTime\":\"1693884236922084237\",\"endTime\":\"1693884237903084237\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"PERC2_FUSION\",\"startTime\":\"1693884237904150547\",\"endTime\":\"1693884237954150547\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"UNIFORM\",\"startTime\":\"1693884237955283345\",\"endTime\":\"1693884237970283345\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"RECORDER_LITTLE_BAG\",\"startTime\":\"1693884237971348657\",\"endTime\":\"1693884238019348657\",\"bootStatus\":\"START_SUCCESS\"}]}],\"hardwareModule\":{\"bootCount\":1,\"moduleBootDetail\":[{\"name\":\"SELFCHECK_CONFIG\",\"startTime\":\"1693884014000000000\",\"endTime\":\"1693884014320000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":1013,\"errorMsg\":\"CODE_HARDWARE_CONFIG_SELECT_FINISH\"},{\"name\":\"MCU_RESPONSE\",\"startTime\":\"1693884014000000000\",\"endTime\":\"1693884014040000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":1004,\"errorMsg\":\"CODE_HARDWARE_GET_MCU_RESPONSE\"},{\"name\":\"MCU_UPDATE\",\"startTime\":\"1693884014000000000\",\"endTime\":\"1693884014250000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":1005,\"errorMsg\":\"CODE_HARDWARE_MCU_FIRMWARE_UPDATE_SUCCESS\"},{\"name\":\"NETWORK_CHECK\",\"startTime\":\"1693884014000000000\",\"endTime\":\"1693884014000000000\",\"bootStatus\":\"START_FAILED\",\"errorCode\":1211,\"errorMsg\":\"CODE_HARDWARE_NETWORK_CHECK\"},{\"name\":\"USB_CHECK\",\"startTime\":\"1693884014000000000\",\"endTime\":\"1693884014260000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":1305,\"errorMsg\":\"CODE_HARDWARE_USB_CHIP_NUM_CHECK_OK\"},{\"name\":\"PING_CHECK\",\"startTime\":\"1693884014000000000\",\"endTime\":\"1693884014200000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2019,\"errorMsg\":\"network check passed\"},{\"name\":\"CLOCK_SYNC\",\"startTime\":\"1693884032000000000\",\"endTime\":\"1693884049190000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2021,\"errorMsg\":\"clock sync success\"},{\"name\":\"DISK_CHECK\",\"startTime\":\"1693884032000000000\",\"endTime\":\"1693884032110000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2023,\"errorMsg\":\"xavier disk check passed\"},{\"name\":\"MOUNT_NFS\",\"startTime\":\"1693884032000000000\",\"endTime\":\"1693884032000000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2025,\"errorMsg\":\"mount nfs success\"},{\"name\":\"ROVER\",\"startTime\":\"1693884065000000000\",\"endTime\":\"1693884067080000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2018,\"errorMsg\":\"rover5 start finish!\"},{\"name\":\"PREPARE_ENGINE\",\"startTime\":\"1693884051000000000\",\"endTime\":\"1693884065490000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2017,\"errorMsg\":\"generate perception engine finish\"}]}}]}}}";
//    public static final String MESSAGE = "{\"requestHeader\":{\"messageTypeRequest\":[{\"messageType\":\"BOOT\",\"needResponse\":true,\"retry\":true},{\"messageType\":\"ABNORMAL\",\"needResponse\":true,\"retry\":true}],\"requestId\":\"1724287354449000078\",\"vehicleName\":\"JDK8163\",\"requestTime\":\"1724287354449850229\"},\"reportStatisticsData\":{\"runtimeUuid\":\"1724287282486111516\",\"vehicleBoot\":{\"bootId\":\"1\",\"startTime\":\"1724287190737356809\",\"endTime\":\"1724287353088919171\",\"allBootStatus\":\"START_SUCCESS\",\"nodeBoot\":[{\"nodeName\":\"Compute\",\"ip\":\"***********\",\"startTime\":\"1724287190737356809\",\"endTime\":\"1724287338969923168\",\"bootStatus\":\"START_SUCCESS\",\"moduleBoot\":[{\"bootCount\":1,\"moduleBootDetail\":[{\"name\":\"DRIVERS_LIDAR_TOP\",\"startTime\":\"1724287296357228928\",\"endTime\":\"1724287297405228928\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_LIDAR_FRONT\",\"startTime\":\"1724287297407147413\",\"endTime\":\"1724287298415147413\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_LIDAR_RIGHT\",\"startTime\":\"1724287298416882711\",\"endTime\":\"1724287299424882711\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_LIDAR_LEFT\",\"startTime\":\"1724287299426615093\",\"endTime\":\"1724287300432615093\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_IMU\",\"startTime\":\"1724287300435804876\",\"endTime\":\"1724287301458804876\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_GPS_RAC\",\"startTime\":\"1724287301461165803\",\"endTime\":\"1724287303482165803\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_CAMERA360_FRONT\",\"startTime\":\"1724287303485005961\",\"endTime\":\"1724287304530005961\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_CAMERA360_LEFT\",\"startTime\":\"1724287304532541591\",\"endTime\":\"1724287305554541591\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_CAMERA360_RIGHT\",\"startTime\":\"1724287305557441173\",\"endTime\":\"1724287306578441173\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_CAMERA360_REAR\",\"startTime\":\"1724287306581028969\",\"endTime\":\"1724287307600028969\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_LIDAR_REAR\",\"startTime\":\"1724287307602199635\",\"endTime\":\"1724287308608199635\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"MAP_INITIALIZATION\",\"startTime\":\"1724287308611092146\",\"endTime\":\"1724287319630092146\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"LOCALIZATION_IMU\",\"startTime\":\"1724287319632418623\",\"endTime\":\"1724287320643418623\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"LOCALIZATION_INIT\",\"startTime\":\"1724287320645464604\",\"endTime\":\"1724287333396464604\",\"bootStatus\":\"START_SUCCESS\",\"errorMsg\":\"loc_method : History\"},{\"name\":\"LOCALIZATION_LIDAR\",\"startTime\":\"1724287333398909460\",\"endTime\":\"1724287335422909460\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"LOCALIZATION_EKF\",\"startTime\":\"1724287335425579773\",\"endTime\":\"1724287335438579773\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"LOCALIZATION_TRANSFORM\",\"startTime\":\"1724287335440762781\",\"endTime\":\"1724287335446762781\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"GPS_SIGNAL\",\"startTime\":\"1724287335451144733\",\"endTime\":\"1724287336469144733\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"PERC2_CLOUD\",\"startTime\":\"1724287336471959079\",\"endTime\":\"1724287337494959079\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"PERC2_GEO\",\"startTime\":\"1724287337497941874\",\"endTime\":\"1724287337518941874\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"PERC2_IMAGE_F\",\"startTime\":\"1724287337521313715\",\"endTime\":\"1724287337898313715\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"PERC2_SOD\",\"startTime\":\"1724287337901337478\",\"endTime\":\"1724287338889337478\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"PERC2_FUSION\",\"startTime\":\"1724287338892377308\",\"endTime\":\"1724287338951377308\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"UNIFORM\",\"startTime\":\"1724287338954196002\",\"endTime\":\"1724287338970196002\",\"bootStatus\":\"START_SUCCESS\"}]}],\"hardwareModule\":{\"bootCount\":1,\"moduleBootDetail\":[{\"name\":\"MCU_RESPONSE\",\"startTime\":\"1724287237000000000\",\"endTime\":\"1724287237040000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":1004,\"errorMsg\":\"CODE_HARDWARE_GET_MCU_RESPONSE\"},{\"name\":\"MCU_UPDATE\",\"startTime\":\"1724287237000000000\",\"endTime\":\"1724287237260000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":1005,\"errorMsg\":\"CODE_HARDWARE_MCU_FIRMWARE_UPDATE_SUCCESS\"},{\"name\":\"NETWORK_CHECK\",\"startTime\":\"1724287237000000000\",\"endTime\":\"1724287237000000000\",\"bootStatus\":\"START_FAILED\",\"errorCode\":1211,\"errorMsg\":\"CODE_HARDWARE_NETWORK_CHECK\"},{\"name\":\"USB_CHECK\",\"startTime\":\"1724287237000000000\",\"endTime\":\"1724287237260000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":1305,\"errorMsg\":\"CODE_HARDWARE_USB_CHIP_NUM_CHECK_OK\"},{\"name\":\"PING_CHECK\",\"startTime\":\"1724287237000000000\",\"endTime\":\"1724287237220000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2019,\"errorMsg\":\"network check passed\"},{\"name\":\"CLOCK_SYNC\",\"startTime\":\"1724287255000000000\",\"endTime\":\"1724287270620000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2021,\"errorMsg\":\"clock sync success\"},{\"name\":\"DISK_CHECK\",\"startTime\":\"1724287255000000000\",\"endTime\":\"1724287255110000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2023,\"errorMsg\":\"xavier disk check passed\"},{\"name\":\"MOUNT_NFS\",\"startTime\":\"1724287256000000000\",\"endTime\":\"1724287256000000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2025,\"errorMsg\":\"mount nfs success\"},{\"name\":\"ROVER\",\"startTime\":\"1724287296000000000\",\"endTime\":\"1724287298080000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2018,\"errorMsg\":\"rover5 start finish!\"},{\"name\":\"PREPARE_ENGINE\",\"startTime\":\"1724287275000000000\",\"endTime\":\"1724287290530000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2017,\"errorMsg\":\"generate perception engine finish\"}]}},{\"nodeName\":\"Control\",\"ip\":\"***********\",\"startTime\":\"1724287190737356809\",\"endTime\":\"1724287353088919171\",\"bootStatus\":\"START_SUCCESS\",\"moduleBoot\":[{\"bootCount\":1,\"moduleBootDetail\":[{\"name\":\"DRIVERS_CHASSIS_RX\",\"startTime\":\"1724287295558876108\",\"endTime\":\"1724287296588876108\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_CHASSIS_TX\",\"startTime\":\"1724287296590234184\",\"endTime\":\"1724287296604234184\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_CAMERA_TRAFFIC_LEFT\",\"startTime\":\"1724287296608002702\",\"endTime\":\"1724287297645002702\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_CAMERA_TRAFFIC_RIGHT\",\"startTime\":\"1724287297646389354\",\"endTime\":\"1724287298664389354\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_JOYSTICK\",\"startTime\":\"1724287298666423828\",\"endTime\":\"1724287298672423828\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"MAP_INITIALIZATION\",\"startTime\":\"1724287298673851066\",\"endTime\":\"1724287305689851066\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"LOCALIZATION_TRANSFORM\",\"startTime\":\"1724287305691532357\",\"endTime\":\"1724287305702532357\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"MAP_UPDATER\",\"startTime\":\"1724287305704229114\",\"endTime\":\"1724287338719229114\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"PERC2_TRAFFIC_LIGHT\",\"startTime\":\"1724287338721062634\",\"endTime\":\"1724287344484062634\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"ROUTING\",\"startTime\":\"1724287344485546931\",\"endTime\":\"1724287348514546931\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"PREDICTION\",\"startTime\":\"1724287348515892123\",\"endTime\":\"1724287349564892123\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"PLANNING\",\"startTime\":\"1724287349567003885\",\"endTime\":\"1724287351941003885\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"PNC_STATUS\",\"startTime\":\"1724287351943264704\",\"endTime\":\"1724287351958264704\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"CONTROL\",\"startTime\":\"1724287351959695606\",\"endTime\":\"1724287352073695606\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"ARTIFICIAL_OBSTACLE\",\"startTime\":\"1724287352075558452\",\"endTime\":\"1724287353088558452\",\"bootStatus\":\"START_SUCCESS\"}]}],\"hardwareModule\":{\"bootCount\":1,\"moduleBootDetail\":[{\"name\":\"SELFCHECK_CONFIG\",\"startTime\":\"1724287221000000000\",\"endTime\":\"1724287221030000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":1013,\"errorMsg\":\"CODE_HARDWARE_CONFIG_SELECT_FINISH\"},{\"name\":\"NETWORK_CHECK\",\"startTime\":\"1724287221000000000\",\"endTime\":\"1724287234070000000\",\"bootStatus\":\"START_FAILED\",\"errorCode\":1205,\"errorMsg\":\"CODE_HARDWARE_NET_ANOTHER_XAVIER_4G_NET_ERR\"},{\"name\":\"USB_CHECK\",\"startTime\":\"1724287221000000000\",\"endTime\":\"1724287221260000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":1305,\"errorMsg\":\"CODE_HARDWARE_USB_CHIP_NUM_CHECK_OK\"},{\"name\":\"PING_CHECK\",\"startTime\":\"1724287222000000000\",\"endTime\":\"1724287222180000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2019,\"errorMsg\":\"network check passed\"},{\"name\":\"CLOCK_SYNC\",\"startTime\":\"1724287253000000000\",\"endTime\":\"1724287284370000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2021,\"errorMsg\":\"clock sync success\"},{\"name\":\"DISK_CHECK\",\"startTime\":\"1724287253000000000\",\"endTime\":\"1724287253110000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2023,\"errorMsg\":\"xavier disk check passed\"},{\"name\":\"MOUNT_NFS\",\"startTime\":\"1724287253000000000\",\"endTime\":\"1724287253040000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2025,\"errorMsg\":\"mount nfs success\"},{\"name\":\"ROVER\",\"startTime\":\"1724287294000000000\",\"endTime\":\"1724287296100000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2018,\"errorMsg\":\"rover5 start finish!\"},{\"name\":\"PREPARE_ENGINE\",\"startTime\":\"1724287276000000000\",\"endTime\":\"1724287291530000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2017,\"errorMsg\":\"generate perception engine finish\"}]}}]},\"abnormalData\":[{\"moduleName\":\"ComponentFrequence\",\"errorCode\":-13501,\"errorLevel\":\"ERROR\",\"errorMsg\":\"ErrorCode: ERROR_GUARDIAN_COMPONENT_HZ_NOT_MATCH, Detail: [PLANNING CONTROL hz warn,].\",\"startTime\":\"1724287353536325382\",\"endTime\":\"1724287354446922126\"},{\"moduleName\":\"DRIVERS_CHASSIS_RX\",\"errorCode\":-1029,\"errorLevel\":\"ERROR\",\"errorMsg\":\"ErrorCode: ERROR_DRIVER_CHASSIS_WHEEL_ALARM, Message: [WHEEL_POSITION_FRONT_LEFT: TIRE_PRESSURE_UNDER_LIMIT].\",\"startTime\":\"1724287296328666357\",\"endTime\":\"1724287354446922126\"},{\"moduleName\":\"DRIVERS_CHASSIS_RX\",\"errorCode\":-1029,\"errorLevel\":\"ERROR\",\"errorMsg\":\"ErrorCode: ERROR_DRIVER_CHASSIS_WHEEL_ALARM, Message: [WHEEL_POSITION_FRONT_LEFT: TIRE_PRESSURE_UNDER_LIMIT].\",\"startTime\":\"1724287352368628239\"},{\"moduleName\":\"ComponentFrequence\",\"errorCode\":-13501,\"errorLevel\":\"ERROR\",\"errorMsg\":\"ErrorCode: ERROR_GUARDIAN_COMPONENT_HZ_NOT_MATCH, Detail: [PLANNING CONTROL hz warn,].\",\"startTime\":\"1724287353536325382\"}]}}";
    // 启动失败
//    public static final String MESSAGE = "{\"requestHeader\":{\"messageTypeRequest\":[{\"messageType\":\"BOOT\",\"needResponse\":true,\"retry\":true}],\"requestId\":\"1692067295323000375\",\"vehicleName\":\"JD0017\",\"requestTime\":\"1692067295323554204\"},\"reportStatisticsData\":{\"runtimeUuid\":\"1692066895482869152\",\"vehicleBoot\":{\"bootId\":\"1\",\"startTime\":\"1692066907835563502\",\"endTime\":\"1692067265157084212\",\"allBootStatus\":\"START_FAILED\",\"nodeBoot\":[{\"nodeName\":\"Compute\",\"ip\":\"***********\",\"startTime\":\"1692066909141778487\",\"endTime\":\"1692066965540121472\",\"bootStatus\":\"START_FAILED\",\"moduleBoot\":[{\"bootCount\":1,\"moduleBootDetail\":[{\"name\":\"DRIVERS_LIDAR_TOP\",\"startTime\":\"1692066909142470423\",\"endTime\":\"1692066910184470423\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_LIDAR_FRONT\",\"startTime\":\"1692066910186086902\",\"endTime\":\"1692066911193086902\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_LIDAR_RIGHT\",\"startTime\":\"1692066911194167315\",\"endTime\":\"1692066916202167315\",\"bootStatus\":\"START_FAILED\",\"errorCode\":-1304,\"errorMsg\":\"Driver[lidar-right] data type is not PointCloud.\"},{\"name\":\"DRIVERS_LIDAR_LEFT\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_IMU\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_GPS_RAC\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_CAMERA360_FRONT\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_CAMERA360_LEFT\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_CAMERA360_RIGHT\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_CAMERA360_REAR\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_LIDAR_REAR\",\"startTime\":\"9223372036854775807\"},{\"name\":\"MAP_INITIALIZATION\",\"startTime\":\"9223372036854775807\"},{\"name\":\"LOCALIZATION_IMU\",\"startTime\":\"9223372036854775807\"},{\"name\":\"LOCALIZATION_INIT\",\"startTime\":\"9223372036854775807\"},{\"name\":\"LOCALIZATION_LIDAR\",\"startTime\":\"9223372036854775807\"},{\"name\":\"LOCALIZATION_EKF\",\"startTime\":\"9223372036854775807\"},{\"name\":\"LOCALIZATION_TRANSFORM\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_CLOUD\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_GEO\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_IMAGE_F\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_IMAGE_B\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_SOD\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_FUSION\",\"startTime\":\"9223372036854775807\"},{\"name\":\"UNIFORM\",\"startTime\":\"9223372036854775807\"},{\"name\":\"RECORDER_LITTLE_BAG\",\"startTime\":\"9223372036854775807\"}]},{\"bootCount\":2,\"moduleBootDetail\":[{\"name\":\"DRIVERS_LIDAR_TOP\",\"startTime\":\"1692066918425071033\",\"endTime\":\"1692066919429071033\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_LIDAR_FRONT\",\"startTime\":\"1692066919430510018\",\"endTime\":\"1692066920435510018\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_LIDAR_RIGHT\",\"startTime\":\"1692066920436464106\",\"endTime\":\"1692066925442464106\",\"bootStatus\":\"START_FAILED\",\"errorCode\":-1304,\"errorMsg\":\"Driver[lidar-right] data type is not PointCloud.\"},{\"name\":\"DRIVERS_LIDAR_LEFT\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_IMU\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_GPS_RAC\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_CAMERA360_FRONT\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_CAMERA360_LEFT\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_CAMERA360_RIGHT\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_CAMERA360_REAR\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_LIDAR_REAR\",\"startTime\":\"9223372036854775807\"},{\"name\":\"MAP_INITIALIZATION\",\"startTime\":\"9223372036854775807\"},{\"name\":\"LOCALIZATION_IMU\",\"startTime\":\"9223372036854775807\"},{\"name\":\"LOCALIZATION_INIT\",\"startTime\":\"9223372036854775807\"},{\"name\":\"LOCALIZATION_LIDAR\",\"startTime\":\"9223372036854775807\"},{\"name\":\"LOCALIZATION_EKF\",\"startTime\":\"9223372036854775807\"},{\"name\":\"LOCALIZATION_TRANSFORM\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_CLOUD\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_GEO\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_IMAGE_F\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_IMAGE_B\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_SOD\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_FUSION\",\"startTime\":\"9223372036854775807\"},{\"name\":\"UNIFORM\",\"startTime\":\"9223372036854775807\"},{\"name\":\"RECORDER_LITTLE_BAG\",\"startTime\":\"9223372036854775807\"}]},{\"bootCount\":3,\"moduleBootDetail\":[{\"name\":\"DRIVERS_LIDAR_TOP\",\"startTime\":\"1692066938417852859\",\"endTime\":\"1692066939422852859\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_LIDAR_FRONT\",\"startTime\":\"1692066939424235766\",\"endTime\":\"1692066940428235766\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_LIDAR_RIGHT\",\"startTime\":\"1692066940430222224\",\"endTime\":\"1692066945435222224\",\"bootStatus\":\"START_FAILED\",\"errorCode\":-1304,\"errorMsg\":\"Driver[lidar-right] data type is not PointCloud.\"},{\"name\":\"DRIVERS_LIDAR_LEFT\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_IMU\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_GPS_RAC\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_CAMERA360_FRONT\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_CAMERA360_LEFT\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_CAMERA360_RIGHT\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_CAMERA360_REAR\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_LIDAR_REAR\",\"startTime\":\"9223372036854775807\"},{\"name\":\"MAP_INITIALIZATION\",\"startTime\":\"9223372036854775807\"},{\"name\":\"LOCALIZATION_IMU\",\"startTime\":\"9223372036854775807\"},{\"name\":\"LOCALIZATION_INIT\",\"startTime\":\"9223372036854775807\"},{\"name\":\"LOCALIZATION_LIDAR\",\"startTime\":\"9223372036854775807\"},{\"name\":\"LOCALIZATION_EKF\",\"startTime\":\"9223372036854775807\"},{\"name\":\"LOCALIZATION_TRANSFORM\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_CLOUD\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_GEO\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_IMAGE_F\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_IMAGE_B\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_SOD\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_FUSION\",\"startTime\":\"9223372036854775807\"},{\"name\":\"UNIFORM\",\"startTime\":\"9223372036854775807\"},{\"name\":\"RECORDER_LITTLE_BAG\",\"startTime\":\"9223372036854775807\"}]},{\"bootCount\":4,\"moduleBootDetail\":[{\"name\":\"DRIVERS_LIDAR_TOP\",\"startTime\":\"1692066958524217472\",\"endTime\":\"1692066959528217472\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_LIDAR_FRONT\",\"startTime\":\"1692066959529449757\",\"endTime\":\"1692066960534449757\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_LIDAR_RIGHT\",\"startTime\":\"1692066960535585698\",\"endTime\":\"1692066965540585698\",\"bootStatus\":\"START_FAILED\",\"errorCode\":-1304,\"errorMsg\":\"Driver[lidar-right] data type is not PointCloud.\"},{\"name\":\"DRIVERS_LIDAR_LEFT\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_IMU\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_GPS_RAC\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_CAMERA360_FRONT\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_CAMERA360_LEFT\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_CAMERA360_RIGHT\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_CAMERA360_REAR\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_LIDAR_REAR\",\"startTime\":\"9223372036854775807\"},{\"name\":\"MAP_INITIALIZATION\",\"startTime\":\"9223372036854775807\"},{\"name\":\"LOCALIZATION_IMU\",\"startTime\":\"9223372036854775807\"},{\"name\":\"LOCALIZATION_INIT\",\"startTime\":\"9223372036854775807\"},{\"name\":\"LOCALIZATION_LIDAR\",\"startTime\":\"9223372036854775807\"},{\"name\":\"LOCALIZATION_EKF\",\"startTime\":\"9223372036854775807\"},{\"name\":\"LOCALIZATION_TRANSFORM\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_CLOUD\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_GEO\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_IMAGE_F\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_IMAGE_B\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_SOD\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_FUSION\",\"startTime\":\"9223372036854775807\"},{\"name\":\"UNIFORM\",\"startTime\":\"9223372036854775807\"},{\"name\":\"RECORDER_LITTLE_BAG\",\"startTime\":\"9223372036854775807\"}]}],\"hardwareModule\":{\"bootCount\":1,\"moduleBootDetail\":[{\"name\":\"SELFCHECK_CONFIG\",\"startTime\":\"1692066850000000000\",\"endTime\":\"1692066850310000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":1013,\"errorMsg\":\"CODE_HARDWARE_CONFIG_SELECT_FINISH\"},{\"name\":\"MCU_RESPONSE\",\"startTime\":\"1692066850000000000\",\"endTime\":\"1692066850040000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":1004,\"errorMsg\":\"CODE_HARDWARE_GET_MCU_RESPONSE\"},{\"name\":\"MCU_UPDATE\",\"startTime\":\"1692066850000000000\",\"endTime\":\"1692066850250000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":1005,\"errorMsg\":\"CODE_HARDWARE_MCU_FIRMWARE_UPDATE_SUCCESS\"},{\"name\":\"NETWORK_CHECK\",\"startTime\":\"1692066850000000000\",\"endTime\":\"1692066850000000000\",\"bootStatus\":\"START_FAILED\",\"errorCode\":1211,\"errorMsg\":\"CODE_HARDWARE_NETWORK_CHECK\"},{\"name\":\"USB_CHECK\",\"startTime\":\"1692066850000000000\",\"endTime\":\"1692066850270000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":1305,\"errorMsg\":\"CODE_HARDWARE_USB_CHIP_NUM_CHECK_OK\"},{\"name\":\"PING_CHECK\",\"startTime\":\"1692066850000000000\",\"endTime\":\"1692066850120000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2019,\"errorMsg\":\"network check passed\"},{\"name\":\"CLOCK_SYNC\",\"startTime\":\"1692066871000000000\",\"endTime\":\"1692066891390000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2021,\"errorMsg\":\"clock sync success\"},{\"name\":\"DISK_CHECK\",\"startTime\":\"1692066871000000000\",\"endTime\":\"1692066871110000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2023,\"errorMsg\":\"xavier disk check passed\"},{\"name\":\"MOUNT_NFS\",\"startTime\":\"1692066871000000000\",\"endTime\":\"1692066871000000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2025,\"errorMsg\":\"mount nfs success\"},{\"name\":\"ROVER\",\"startTime\":\"1692066904000000000\",\"endTime\":\"1692066906080000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2018,\"errorMsg\":\"rover5 start finish!\"},{\"name\":\"PREPARE_ENGINE\",\"startTime\":\"1692066889000000000\",\"endTime\":\"1692066903480000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2017,\"errorMsg\":\"generate perception engine finish\"}]}},{\"nodeName\":\"Control\",\"ip\":\"***********\",\"startTime\":\"1692066907835563502\",\"endTime\":\"1692067265157084212\",\"bootStatus\":\"START_FAILED\",\"moduleBoot\":[{\"bootCount\":1,\"moduleBootDetail\":[{\"name\":\"DRIVERS_CHASSIS_RX\",\"startTime\":\"1692066907836039630\",\"endTime\":\"1692066908859039630\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_CHASSIS_TX\",\"startTime\":\"1692066908859910356\",\"endTime\":\"1692066908874910356\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_CAMERA_TRAFFIC_LEFT\",\"startTime\":\"1692066908875902313\",\"endTime\":\"1692066909913902313\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_CAMERA_TRAFFIC_RIGHT\",\"startTime\":\"1692066909914839890\",\"endTime\":\"1692066910932839890\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_JOYSTICK\",\"startTime\":\"1692066910934004168\",\"endTime\":\"1692066910942004168\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"MAP_INITIALIZATION\",\"startTime\":\"1692066910943059682\",\"endTime\":\"1692066917955059682\",\"bootStatus\":\"SHUT_DOWN_SUCCESS\",\"errorCode\":-2000,\"errorMsg\":\"Map initialization is not ready.\"},{\"name\":\"LOCALIZATION_TRANSFORM\",\"startTime\":\"9223372036854775807\"},{\"name\":\"MAP_UPDATER\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_TRAFFIC_LIGHT\",\"startTime\":\"9223372036854775807\"},{\"name\":\"ROUTING\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PREDICTION\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PLANNING\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PNC_STATUS\",\"startTime\":\"9223372036854775807\"},{\"name\":\"CONTROL\",\"startTime\":\"9223372036854775807\"},{\"name\":\"ARTIFICIAL_OBSTACLE\",\"startTime\":\"9223372036854775807\"}]},{\"bootCount\":2,\"moduleBootDetail\":[{\"name\":\"DRIVERS_CHASSIS_RX\",\"startTime\":\"1692066921975551876\",\"endTime\":\"1692066922980551876\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_CHASSIS_TX\",\"startTime\":\"1692066922981850536\",\"endTime\":\"1692066922988850536\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_CAMERA_TRAFFIC_LEFT\",\"startTime\":\"1692066922989233602\",\"endTime\":\"1692066923992233602\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_CAMERA_TRAFFIC_RIGHT\",\"startTime\":\"1692066923993296173\",\"endTime\":\"1692066924996296173\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_JOYSTICK\",\"startTime\":\"1692066924997101849\",\"endTime\":\"1692066925001101849\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"MAP_INITIALIZATION\",\"startTime\":\"1692066925001603478\",\"endTime\":\"1692066938007603478\",\"bootStatus\":\"SHUT_DOWN_SUCCESS\",\"errorCode\":-2000,\"errorMsg\":\"Map initialization is not ready.\"},{\"name\":\"LOCALIZATION_TRANSFORM\",\"startTime\":\"9223372036854775807\"},{\"name\":\"MAP_UPDATER\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_TRAFFIC_LIGHT\",\"startTime\":\"9223372036854775807\"},{\"name\":\"ROUTING\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PREDICTION\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PLANNING\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PNC_STATUS\",\"startTime\":\"9223372036854775807\"},{\"name\":\"CONTROL\",\"startTime\":\"9223372036854775807\"},{\"name\":\"ARTIFICIAL_OBSTACLE\",\"startTime\":\"9223372036854775807\"}]},{\"bootCount\":3,\"moduleBootDetail\":[{\"name\":\"DRIVERS_CHASSIS_RX\",\"startTime\":\"1692066943024429411\",\"endTime\":\"1692066944029429411\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_CHASSIS_TX\",\"startTime\":\"1692066944030620637\",\"endTime\":\"1692066944036620637\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_CAMERA_TRAFFIC_LEFT\",\"startTime\":\"1692066944037155001\",\"endTime\":\"1692066945039155001\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_CAMERA_TRAFFIC_RIGHT\",\"startTime\":\"1692066945040506870\",\"endTime\":\"1692066946043506870\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_JOYSTICK\",\"startTime\":\"1692066946045020479\",\"endTime\":\"1692066946048020479\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"MAP_INITIALIZATION\",\"startTime\":\"1692066946049154813\",\"endTime\":\"1692066958055154813\",\"bootStatus\":\"SHUT_DOWN_SUCCESS\",\"errorCode\":-2000,\"errorMsg\":\"Map initialization is not ready.\"},{\"name\":\"LOCALIZATION_TRANSFORM\",\"startTime\":\"9223372036854775807\"},{\"name\":\"MAP_UPDATER\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_TRAFFIC_LIGHT\",\"startTime\":\"9223372036854775807\"},{\"name\":\"ROUTING\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PREDICTION\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PLANNING\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PNC_STATUS\",\"startTime\":\"9223372036854775807\"},{\"name\":\"CONTROL\",\"startTime\":\"9223372036854775807\"},{\"name\":\"ARTIFICIAL_OBSTACLE\",\"startTime\":\"9223372036854775807\"}]},{\"bootCount\":4,\"moduleBootDetail\":[{\"name\":\"DRIVERS_CHASSIS_RX\",\"startTime\":\"1692066963075878004\",\"endTime\":\"1692066964080878004\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_CHASSIS_TX\",\"startTime\":\"1692066964082842501\",\"endTime\":\"1692066964088842501\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_CAMERA_TRAFFIC_LEFT\",\"startTime\":\"1692066964090194113\",\"endTime\":\"1692066965092194113\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_CAMERA_TRAFFIC_RIGHT\",\"startTime\":\"1692066965094181960\",\"endTime\":\"1692066966098181960\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_JOYSTICK\",\"startTime\":\"1692066966099403857\",\"endTime\":\"1692066966102403857\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"MAP_INITIALIZATION\",\"startTime\":\"1692066966103761295\",\"endTime\":\"1692067265157761295\",\"bootStatus\":\"START_FAILED\",\"errorCode\":-2000,\"errorMsg\":\"Map initialization is not ready.\"},{\"name\":\"LOCALIZATION_TRANSFORM\",\"startTime\":\"9223372036854775807\"},{\"name\":\"MAP_UPDATER\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_TRAFFIC_LIGHT\",\"startTime\":\"9223372036854775807\"},{\"name\":\"ROUTING\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PREDICTION\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PLANNING\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PNC_STATUS\",\"startTime\":\"9223372036854775807\"},{\"name\":\"CONTROL\",\"startTime\":\"9223372036854775807\"},{\"name\":\"ARTIFICIAL_OBSTACLE\",\"startTime\":\"9223372036854775807\"}]}],\"hardwareModule\":{\"bootCount\":1,\"moduleBootDetail\":[{\"name\":\"SELFCHECK_CONFIG\",\"startTime\":\"1692066831000000000\",\"endTime\":\"1692066831020000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":1013,\"errorMsg\":\"CODE_HARDWARE_CONFIG_SELECT_FINISH\"},{\"name\":\"NETWORK_CHECK\",\"startTime\":\"1692066831000000000\",\"endTime\":\"1692066844240000000\",\"bootStatus\":\"START_FAILED\",\"errorCode\":1205,\"errorMsg\":\"CODE_HARDWARE_NET_ANOTHER_XAVIER_4G_NET_ERR\"},{\"name\":\"USB_CHECK\",\"startTime\":\"1692066831000000000\",\"endTime\":\"1692066831290000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":1305,\"errorMsg\":\"CODE_HARDWARE_USB_CHIP_NUM_CHECK_OK\"},{\"name\":\"PING_CHECK\",\"startTime\":\"1692066836000000000\",\"endTime\":\"1692066840170000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2019,\"errorMsg\":\"network check passed\"},{\"name\":\"CLOCK_SYNC\",\"startTime\":\"1692066871000000000\",\"endTime\":\"1692066906500000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2021,\"errorMsg\":\"clock sync success\"},{\"name\":\"DISK_CHECK\",\"startTime\":\"1692066871000000000\",\"endTime\":\"1692066871100000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2023,\"errorMsg\":\"xavier disk check passed\"},{\"name\":\"MOUNT_NFS\",\"startTime\":\"1692066871000000000\",\"endTime\":\"1692066871040000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2025,\"errorMsg\":\"mount nfs success\"},{\"name\":\"ROVER\",\"startTime\":\"1692066903000000000\",\"endTime\":\"1692066905080000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2018,\"errorMsg\":\"rover5 start finish!\"},{\"name\":\"PREPARE_ENGINE\",\"startTime\":\"1692066889000000000\",\"endTime\":\"1692066903470000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2017,\"errorMsg\":\"generate perception engine finish\"}]}}]}}}";
//    public static final String MESSAGE = "{\"requestHeader\":{\"messageTypeRequest\":[{\"messageType\":\"BOOT\",\"needResponse\":true,\"retry\":true}],\"requestId\":\"1692944631496000219\",\"vehicleName\":\"JDZ0038\",\"requestTime\":\"1692944631496023127\"},\"reportStatisticsData\":{\"runtimeUuid\":\"1692944231919518260\",\"vehicleBoot\":{\"bootId\":\"1\",\"startTime\":\"1692944118228575873\",\"endTime\":\"1692944601438123416\",\"allBootStatus\":\"START_FAILED\",\"nodeBoot\":[{\"nodeName\":\"Compute\",\"ip\":\"***********\",\"startTime\":\"1692944118228575873\",\"endTime\":\"1692944302048336427\",\"bootStatus\":\"START_FAILED\",\"moduleBoot\":[{\"bootCount\":1,\"moduleBootDetail\":[{\"name\":\"DRIVERS_LIDAR_TOP\",\"startTime\":\"1692944244684684604\",\"endTime\":\"1692944245721684604\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_LIDAR_FRONT\",\"startTime\":\"1692944245723125727\",\"endTime\":\"1692944246729125727\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_LIDAR_RIGHT\",\"startTime\":\"1692944246729711401\",\"endTime\":\"1692944251736711401\",\"bootStatus\":\"START_FAILED\",\"errorCode\":-1309,\"errorMsg\":\"Driver[lidar-right] wait data timeout.\"},{\"name\":\"DRIVERS_LIDAR_LEFT\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_IMU\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_GPS_RAC\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_CAMERA360_FRONT\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_CAMERA360_LEFT\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_CAMERA360_RIGHT\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_CAMERA360_REAR\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_LIDAR_REAR\",\"startTime\":\"9223372036854775807\"},{\"name\":\"MAP_INITIALIZATION\",\"startTime\":\"9223372036854775807\"},{\"name\":\"LOCALIZATION_IMU\",\"startTime\":\"9223372036854775807\"},{\"name\":\"LOCALIZATION_INIT\",\"startTime\":\"9223372036854775807\"},{\"name\":\"LOCALIZATION_LIDAR\",\"startTime\":\"9223372036854775807\"},{\"name\":\"LOCALIZATION_EKF\",\"startTime\":\"9223372036854775807\"},{\"name\":\"LOCALIZATION_TRANSFORM\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_CLOUD\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_GEO\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_IMAGE_F\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_IMAGE_B\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_SOD\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_FUSION\",\"startTime\":\"9223372036854775807\"},{\"name\":\"UNIFORM\",\"startTime\":\"9223372036854775807\"},{\"name\":\"RECORDER_LITTLE_BAG\",\"startTime\":\"9223372036854775807\"}]},{\"bootCount\":2,\"moduleBootDetail\":[{\"name\":\"DRIVERS_LIDAR_TOP\",\"startTime\":\"1692944254825562907\",\"endTime\":\"1692944255830562907\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_LIDAR_FRONT\",\"startTime\":\"1692944255831844690\",\"endTime\":\"1692944256835844690\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_LIDAR_RIGHT\",\"startTime\":\"1692944256837654928\",\"endTime\":\"1692944261842654928\",\"bootStatus\":\"START_FAILED\",\"errorCode\":-1309,\"errorMsg\":\"Driver[lidar-right] wait data timeout.\"},{\"name\":\"DRIVERS_LIDAR_LEFT\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_IMU\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_GPS_RAC\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_CAMERA360_FRONT\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_CAMERA360_LEFT\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_CAMERA360_RIGHT\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_CAMERA360_REAR\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_LIDAR_REAR\",\"startTime\":\"9223372036854775807\"},{\"name\":\"MAP_INITIALIZATION\",\"startTime\":\"9223372036854775807\"},{\"name\":\"LOCALIZATION_IMU\",\"startTime\":\"9223372036854775807\"},{\"name\":\"LOCALIZATION_INIT\",\"startTime\":\"9223372036854775807\"},{\"name\":\"LOCALIZATION_LIDAR\",\"startTime\":\"9223372036854775807\"},{\"name\":\"LOCALIZATION_EKF\",\"startTime\":\"9223372036854775807\"},{\"name\":\"LOCALIZATION_TRANSFORM\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_CLOUD\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_GEO\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_IMAGE_F\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_IMAGE_B\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_SOD\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_FUSION\",\"startTime\":\"9223372036854775807\"},{\"name\":\"UNIFORM\",\"startTime\":\"9223372036854775807\"},{\"name\":\"RECORDER_LITTLE_BAG\",\"startTime\":\"9223372036854775807\"}]},{\"bootCount\":3,\"moduleBootDetail\":[{\"name\":\"DRIVERS_LIDAR_TOP\",\"startTime\":\"1692944274926910876\",\"endTime\":\"1692944275930910876\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_LIDAR_FRONT\",\"startTime\":\"1692944275932455512\",\"endTime\":\"1692944276936455512\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_LIDAR_RIGHT\",\"startTime\":\"1692944276937786939\",\"endTime\":\"1692944281941786939\",\"bootStatus\":\"START_FAILED\",\"errorCode\":-1309,\"errorMsg\":\"Driver[lidar-right] wait data timeout.\"},{\"name\":\"DRIVERS_LIDAR_LEFT\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_IMU\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_GPS_RAC\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_CAMERA360_FRONT\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_CAMERA360_LEFT\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_CAMERA360_RIGHT\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_CAMERA360_REAR\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_LIDAR_REAR\",\"startTime\":\"9223372036854775807\"},{\"name\":\"MAP_INITIALIZATION\",\"startTime\":\"9223372036854775807\"},{\"name\":\"LOCALIZATION_IMU\",\"startTime\":\"9223372036854775807\"},{\"name\":\"LOCALIZATION_INIT\",\"startTime\":\"9223372036854775807\"},{\"name\":\"LOCALIZATION_LIDAR\",\"startTime\":\"9223372036854775807\"},{\"name\":\"LOCALIZATION_EKF\",\"startTime\":\"9223372036854775807\"},{\"name\":\"LOCALIZATION_TRANSFORM\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_CLOUD\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_GEO\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_IMAGE_F\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_IMAGE_B\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_SOD\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_FUSION\",\"startTime\":\"9223372036854775807\"},{\"name\":\"UNIFORM\",\"startTime\":\"9223372036854775807\"},{\"name\":\"RECORDER_LITTLE_BAG\",\"startTime\":\"9223372036854775807\"}]},{\"bootCount\":4,\"moduleBootDetail\":[{\"name\":\"DRIVERS_LIDAR_TOP\",\"startTime\":\"1692944295030795787\",\"endTime\":\"1692944296034795787\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_LIDAR_FRONT\",\"startTime\":\"1692944296036723088\",\"endTime\":\"1692944297040723088\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_LIDAR_RIGHT\",\"startTime\":\"1692944297042879213\",\"endTime\":\"1692944302047879213\",\"bootStatus\":\"START_FAILED\",\"errorCode\":-1309,\"errorMsg\":\"Driver[lidar-right] wait data timeout.\"},{\"name\":\"DRIVERS_LIDAR_LEFT\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_IMU\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_GPS_RAC\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_CAMERA360_FRONT\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_CAMERA360_LEFT\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_CAMERA360_RIGHT\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_CAMERA360_REAR\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_LIDAR_REAR\",\"startTime\":\"9223372036854775807\"},{\"name\":\"MAP_INITIALIZATION\",\"startTime\":\"9223372036854775807\"},{\"name\":\"LOCALIZATION_IMU\",\"startTime\":\"9223372036854775807\"},{\"name\":\"LOCALIZATION_INIT\",\"startTime\":\"9223372036854775807\"},{\"name\":\"LOCALIZATION_LIDAR\",\"startTime\":\"9223372036854775807\"},{\"name\":\"LOCALIZATION_EKF\",\"startTime\":\"9223372036854775807\"},{\"name\":\"LOCALIZATION_TRANSFORM\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_CLOUD\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_GEO\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_IMAGE_F\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_IMAGE_B\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_SOD\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_FUSION\",\"startTime\":\"9223372036854775807\"},{\"name\":\"UNIFORM\",\"startTime\":\"9223372036854775807\"},{\"name\":\"RECORDER_LITTLE_BAG\",\"startTime\":\"9223372036854775807\"}]}],\"hardwareModule\":{\"bootCount\":1,\"moduleBootDetail\":[{\"name\":\"SELFCHECK_CONFIG\",\"startTime\":\"1692944183000000000\",\"endTime\":\"1692944183310000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":1013,\"errorMsg\":\"CODE_HARDWARE_CONFIG_SELECT_FINISH\"},{\"name\":\"MCU_RESPONSE\",\"startTime\":\"1692944183000000000\",\"endTime\":\"1692944183050000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":1004,\"errorMsg\":\"CODE_HARDWARE_GET_MCU_RESPONSE\"},{\"name\":\"MCU_UPDATE\",\"startTime\":\"1692944183000000000\",\"endTime\":\"1692944183270000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":1005,\"errorMsg\":\"CODE_HARDWARE_MCU_FIRMWARE_UPDATE_SUCCESS\"},{\"name\":\"NETWORK_CHECK\",\"startTime\":\"1692944183000000000\",\"endTime\":\"1692944183000000000\",\"bootStatus\":\"START_FAILED\",\"errorCode\":1211,\"errorMsg\":\"CODE_HARDWARE_NETWORK_CHECK\"},{\"name\":\"USB_CHECK\",\"startTime\":\"1692944183000000000\",\"endTime\":\"1692944183270000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":1305,\"errorMsg\":\"CODE_HARDWARE_USB_CHIP_NUM_CHECK_OK\"},{\"name\":\"PING_CHECK\",\"startTime\":\"1692944184000000000\",\"endTime\":\"1692944184200000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2019,\"errorMsg\":\"network check passed\"},{\"name\":\"CLOCK_SYNC\",\"startTime\":\"1692944201000000000\",\"endTime\":\"1692944217830000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2021,\"errorMsg\":\"clock sync success\"},{\"name\":\"DISK_CHECK\",\"startTime\":\"1692944201000000000\",\"endTime\":\"1692944201110000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2023,\"errorMsg\":\"xavier disk check passed\"},{\"name\":\"MOUNT_NFS\",\"startTime\":\"1692944201000000000\",\"endTime\":\"1692944201000000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2025,\"errorMsg\":\"mount nfs success\"},{\"name\":\"IMAGE_UPGRADE\",\"startTime\":\"1692944208000000000\",\"endTime\":\"1692944214100000000\",\"bootStatus\":\"START_FAILED\",\"errorCode\":2026,\"errorMsg\":\"image upgrade success\"},{\"name\":\"ROVER\",\"startTime\":\"1692944240000000000\",\"endTime\":\"1692944242080000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2018,\"errorMsg\":\"rover5 start finish!\"},{\"name\":\"PREPARE_ENGINE\",\"startTime\":\"1692944226000000000\",\"endTime\":\"1692944240480000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2017,\"errorMsg\":\"generate perception engine finish\"}]}},{\"nodeName\":\"Control\",\"ip\":\"***********\",\"startTime\":\"1692944118228575873\",\"endTime\":\"1692944601438123416\",\"bootStatus\":\"START_FAILED\",\"moduleBoot\":[{\"bootCount\":1,\"moduleBootDetail\":[{\"name\":\"DRIVERS_CHASSIS_RX\",\"startTime\":\"1692944244129119620\",\"endTime\":\"1692944245150119620\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_CHASSIS_TX\",\"startTime\":\"1692944245151499768\",\"endTime\":\"1692944245166499768\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_CAMERA_TRAFFIC_LEFT\",\"startTime\":\"1692944245167460327\",\"endTime\":\"1692944246200460327\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_CAMERA_TRAFFIC_RIGHT\",\"startTime\":\"1692944246201000495\",\"endTime\":\"1692944247220000495\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_JOYSTICK\",\"startTime\":\"1692944247220995885\",\"endTime\":\"1692944247226995885\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"MAP_INITIALIZATION\",\"startTime\":\"1692944247228092866\",\"endTime\":\"1692944254239092866\",\"bootStatus\":\"SHUT_DOWN_SUCCESS\",\"errorCode\":-2000,\"errorMsg\":\"Map initialization is not ready.\"},{\"name\":\"LOCALIZATION_TRANSFORM\",\"startTime\":\"9223372036854775807\"},{\"name\":\"MAP_UPDATER\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_TRAFFIC_LIGHT\",\"startTime\":\"9223372036854775807\"},{\"name\":\"ROUTING\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PREDICTION\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PLANNING\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PNC_STATUS\",\"startTime\":\"9223372036854775807\"},{\"name\":\"CONTROL\",\"startTime\":\"9223372036854775807\"},{\"name\":\"ARTIFICIAL_OBSTACLE\",\"startTime\":\"9223372036854775807\"}]},{\"bootCount\":2,\"moduleBootDetail\":[{\"name\":\"DRIVERS_CHASSIS_RX\",\"startTime\":\"1692944258260720726\",\"endTime\":\"1692944259265720726\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_CHASSIS_TX\",\"startTime\":\"1692944259267130597\",\"endTime\":\"1692944259273130597\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_CAMERA_TRAFFIC_LEFT\",\"startTime\":\"1692944259274314810\",\"endTime\":\"1692944260277314810\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_CAMERA_TRAFFIC_RIGHT\",\"startTime\":\"1692944260278354278\",\"endTime\":\"1692944261281354278\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_JOYSTICK\",\"startTime\":\"1692944261282843105\",\"endTime\":\"1692944261285843105\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"MAP_INITIALIZATION\",\"startTime\":\"1692944261286634316\",\"endTime\":\"1692944274293634316\",\"bootStatus\":\"SHUT_DOWN_SUCCESS\",\"errorCode\":-2000,\"errorMsg\":\"Map initialization is not ready.\"},{\"name\":\"LOCALIZATION_TRANSFORM\",\"startTime\":\"9223372036854775807\"},{\"name\":\"MAP_UPDATER\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_TRAFFIC_LIGHT\",\"startTime\":\"9223372036854775807\"},{\"name\":\"ROUTING\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PREDICTION\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PLANNING\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PNC_STATUS\",\"startTime\":\"9223372036854775807\"},{\"name\":\"CONTROL\",\"startTime\":\"9223372036854775807\"},{\"name\":\"ARTIFICIAL_OBSTACLE\",\"startTime\":\"9223372036854775807\"}]},{\"bootCount\":3,\"moduleBootDetail\":[{\"name\":\"DRIVERS_CHASSIS_RX\",\"startTime\":\"1692944279308032191\",\"endTime\":\"1692944280313032191\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_CHASSIS_TX\",\"startTime\":\"1692944280314371841\",\"endTime\":\"1692944280320371841\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_CAMERA_TRAFFIC_LEFT\",\"startTime\":\"1692944280321388087\",\"endTime\":\"1692944281323388087\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_CAMERA_TRAFFIC_RIGHT\",\"startTime\":\"1692944281325034241\",\"endTime\":\"1692944282328034241\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_JOYSTICK\",\"startTime\":\"1692944282329598600\",\"endTime\":\"1692944282333598600\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"MAP_INITIALIZATION\",\"startTime\":\"1692944282334677846\",\"endTime\":\"1692944294341677846\",\"bootStatus\":\"SHUT_DOWN_SUCCESS\",\"errorCode\":-2000,\"errorMsg\":\"Map initialization is not ready.\"},{\"name\":\"LOCALIZATION_TRANSFORM\",\"startTime\":\"9223372036854775807\"},{\"name\":\"MAP_UPDATER\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_TRAFFIC_LIGHT\",\"startTime\":\"9223372036854775807\"},{\"name\":\"ROUTING\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PREDICTION\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PLANNING\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PNC_STATUS\",\"startTime\":\"9223372036854775807\"},{\"name\":\"CONTROL\",\"startTime\":\"9223372036854775807\"},{\"name\":\"ARTIFICIAL_OBSTACLE\",\"startTime\":\"9223372036854775807\"}]},{\"bootCount\":4,\"moduleBootDetail\":[{\"name\":\"DRIVERS_CHASSIS_RX\",\"startTime\":\"1692944299359886074\",\"endTime\":\"1692944300364886074\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_CHASSIS_TX\",\"startTime\":\"1692944300366349191\",\"endTime\":\"1692944300372349191\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_CAMERA_TRAFFIC_LEFT\",\"startTime\":\"1692944300373384539\",\"endTime\":\"1692944301375384539\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_CAMERA_TRAFFIC_RIGHT\",\"startTime\":\"1692944301376441084\",\"endTime\":\"1692944302379441084\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_JOYSTICK\",\"startTime\":\"1692944302380810371\",\"endTime\":\"1692944302383810371\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"MAP_INITIALIZATION\",\"startTime\":\"1692944302384700591\",\"endTime\":\"1692944601437700591\",\"bootStatus\":\"START_FAILED\",\"errorCode\":-2000,\"errorMsg\":\"Map initialization is not ready.\"},{\"name\":\"LOCALIZATION_TRANSFORM\",\"startTime\":\"9223372036854775807\"},{\"name\":\"MAP_UPDATER\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_TRAFFIC_LIGHT\",\"startTime\":\"9223372036854775807\"},{\"name\":\"ROUTING\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PREDICTION\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PLANNING\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PNC_STATUS\",\"startTime\":\"9223372036854775807\"},{\"name\":\"CONTROL\",\"startTime\":\"9223372036854775807\"},{\"name\":\"ARTIFICIAL_OBSTACLE\",\"startTime\":\"9223372036854775807\"}]}],\"hardwareModule\":{\"bootCount\":1,\"moduleBootDetail\":[{\"name\":\"SELFCHECK_CONFIG\",\"startTime\":\"1692944152000000000\",\"endTime\":\"1692944152030000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":1013,\"errorMsg\":\"CODE_HARDWARE_CONFIG_SELECT_FINISH\"},{\"name\":\"NETWORK_CHECK\",\"startTime\":\"1692944152000000000\",\"endTime\":\"1692944165790000000\",\"bootStatus\":\"START_FAILED\",\"errorCode\":1205,\"errorMsg\":\"CODE_HARDWARE_NET_ANOTHER_XAVIER_4G_NET_ERR\"},{\"name\":\"USB_CHECK\",\"startTime\":\"1692944152000000000\",\"endTime\":\"1692944152260000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":1305,\"errorMsg\":\"CODE_HARDWARE_USB_CHIP_NUM_CHECK_OK\"},{\"name\":\"PING_CHECK\",\"startTime\":\"1692944153000000000\",\"endTime\":\"1692944153190000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2019,\"errorMsg\":\"network check passed\"},{\"name\":\"CLOCK_SYNC\",\"startTime\":\"1692944201000000000\",\"endTime\":\"1692944248970000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2021,\"errorMsg\":\"clock sync success\"},{\"name\":\"DISK_CHECK\",\"startTime\":\"1692944201000000000\",\"endTime\":\"1692944201110000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2023,\"errorMsg\":\"xavier disk check passed\"},{\"name\":\"MOUNT_NFS\",\"startTime\":\"1692944201000000000\",\"endTime\":\"1692944201030000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2025,\"errorMsg\":\"mount nfs success\"},{\"name\":\"IMAGE_UPGRADE\",\"startTime\":\"1692944208000000000\",\"endTime\":\"1692944214430000000\",\"bootStatus\":\"START_FAILED\",\"errorCode\":2026,\"errorMsg\":\"image upgrade success\"},{\"name\":\"ROVER\",\"startTime\":\"1692944240000000000\",\"endTime\":\"1692944242080000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2018,\"errorMsg\":\"rover5 start finish!\"},{\"name\":\"PREPARE_ENGINE\",\"startTime\":\"1692944225000000000\",\"endTime\":\"1692944239480000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2017,\"errorMsg\":\"generate perception engine finish\"}]}}]}}}";
    // 异常启动 bootId > 1
//    public static final String MESSAGE = "{\"requestHeader\":{\"messageTypeRequest\":[{\"messageType\":\"BOOT\",\"needResponse\":true,\"retry\":true}],\"requestId\":\"1692944631496000219\",\"vehicleName\":\"JDZ0038\",\"requestTime\":\"1692944631496023127\"},\"reportStatisticsData\":{\"runtimeUuid\":\"1692944231919518260\",\"vehicleBoot\":{\"bootId\":\"1\",\"startTime\":\"1692944118228575873\",\"endTime\":\"1692944601438123416\",\"allBootStatus\":\"STARTING\",\"nodeBoot\":[{\"nodeName\":\"Compute\",\"ip\":\"***********\",\"startTime\":\"1692944118228575873\",\"endTime\":\"1692944302048336427\",\"bootStatus\":\"STARTING\",\"moduleBoot\":[{\"bootCount\":1,\"moduleBootDetail\":[{\"name\":\"DRIVERS_LIDAR_TOP\",\"startTime\":\"1692944244684684604\",\"endTime\":\"1692944245721684604\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_LIDAR_FRONT\",\"startTime\":\"1692944245723125727\",\"endTime\":\"1692944246729125727\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_LIDAR_RIGHT\",\"startTime\":\"1692944246729711401\",\"endTime\":\"1692944251736711401\",\"bootStatus\":\"STARTING\",\"errorCode\":-1309,\"errorMsg\":\"Driver[lidar-right] wait data timeout.\"},{\"name\":\"DRIVERS_LIDAR_LEFT\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_IMU\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_GPS_RAC\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_CAMERA360_FRONT\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_CAMERA360_LEFT\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_CAMERA360_RIGHT\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_CAMERA360_REAR\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_LIDAR_REAR\",\"startTime\":\"9223372036854775807\"},{\"name\":\"MAP_INITIALIZATION\",\"startTime\":\"9223372036854775807\"},{\"name\":\"LOCALIZATION_IMU\",\"startTime\":\"9223372036854775807\"},{\"name\":\"LOCALIZATION_INIT\",\"startTime\":\"9223372036854775807\"},{\"name\":\"LOCALIZATION_LIDAR\",\"startTime\":\"9223372036854775807\"},{\"name\":\"LOCALIZATION_EKF\",\"startTime\":\"9223372036854775807\"},{\"name\":\"LOCALIZATION_TRANSFORM\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_CLOUD\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_GEO\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_IMAGE_F\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_IMAGE_B\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_SOD\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_FUSION\",\"startTime\":\"9223372036854775807\"},{\"name\":\"UNIFORM\",\"startTime\":\"9223372036854775807\"},{\"name\":\"RECORDER_LITTLE_BAG\",\"startTime\":\"9223372036854775807\"}]},{\"bootCount\":2,\"moduleBootDetail\":[{\"name\":\"DRIVERS_LIDAR_TOP\",\"startTime\":\"1692944254825562907\",\"endTime\":\"1692944255830562907\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_LIDAR_FRONT\",\"startTime\":\"1692944255831844690\",\"endTime\":\"1692944256835844690\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_LIDAR_RIGHT\",\"startTime\":\"1692944256837654928\",\"endTime\":\"1692944261842654928\",\"bootStatus\":\"START_FAILED\",\"errorCode\":-1309,\"errorMsg\":\"Driver[lidar-right] wait data timeout.\"},{\"name\":\"DRIVERS_LIDAR_LEFT\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_IMU\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_GPS_RAC\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_CAMERA360_FRONT\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_CAMERA360_LEFT\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_CAMERA360_RIGHT\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_CAMERA360_REAR\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_LIDAR_REAR\",\"startTime\":\"9223372036854775807\"},{\"name\":\"MAP_INITIALIZATION\",\"startTime\":\"9223372036854775807\"},{\"name\":\"LOCALIZATION_IMU\",\"startTime\":\"9223372036854775807\"},{\"name\":\"LOCALIZATION_INIT\",\"startTime\":\"9223372036854775807\"},{\"name\":\"LOCALIZATION_LIDAR\",\"startTime\":\"9223372036854775807\"},{\"name\":\"LOCALIZATION_EKF\",\"startTime\":\"9223372036854775807\"},{\"name\":\"LOCALIZATION_TRANSFORM\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_CLOUD\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_GEO\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_IMAGE_F\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_IMAGE_B\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_SOD\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_FUSION\",\"startTime\":\"9223372036854775807\"},{\"name\":\"UNIFORM\",\"startTime\":\"9223372036854775807\"},{\"name\":\"RECORDER_LITTLE_BAG\",\"startTime\":\"9223372036854775807\"}]},{\"bootCount\":3,\"moduleBootDetail\":[{\"name\":\"DRIVERS_LIDAR_TOP\",\"startTime\":\"1692944274926910876\",\"endTime\":\"1692944275930910876\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_LIDAR_FRONT\",\"startTime\":\"1692944275932455512\",\"endTime\":\"1692944276936455512\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_LIDAR_RIGHT\",\"startTime\":\"1692944276937786939\",\"endTime\":\"1692944281941786939\",\"bootStatus\":\"START_FAILED\",\"errorCode\":-1309,\"errorMsg\":\"Driver[lidar-right] wait data timeout.\"},{\"name\":\"DRIVERS_LIDAR_LEFT\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_IMU\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_GPS_RAC\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_CAMERA360_FRONT\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_CAMERA360_LEFT\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_CAMERA360_RIGHT\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_CAMERA360_REAR\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_LIDAR_REAR\",\"startTime\":\"9223372036854775807\"},{\"name\":\"MAP_INITIALIZATION\",\"startTime\":\"9223372036854775807\"},{\"name\":\"LOCALIZATION_IMU\",\"startTime\":\"9223372036854775807\"},{\"name\":\"LOCALIZATION_INIT\",\"startTime\":\"9223372036854775807\"},{\"name\":\"LOCALIZATION_LIDAR\",\"startTime\":\"9223372036854775807\"},{\"name\":\"LOCALIZATION_EKF\",\"startTime\":\"9223372036854775807\"},{\"name\":\"LOCALIZATION_TRANSFORM\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_CLOUD\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_GEO\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_IMAGE_F\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_IMAGE_B\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_SOD\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_FUSION\",\"startTime\":\"9223372036854775807\"},{\"name\":\"UNIFORM\",\"startTime\":\"9223372036854775807\"},{\"name\":\"RECORDER_LITTLE_BAG\",\"startTime\":\"9223372036854775807\"}]},{\"bootCount\":4,\"moduleBootDetail\":[{\"name\":\"DRIVERS_LIDAR_TOP\",\"startTime\":\"1692944295030795787\",\"endTime\":\"1692944296034795787\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_LIDAR_FRONT\",\"startTime\":\"1692944296036723088\",\"endTime\":\"1692944297040723088\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_LIDAR_RIGHT\",\"startTime\":\"1692944297042879213\",\"endTime\":\"1692944302047879213\",\"bootStatus\":\"START_FAILED\",\"errorCode\":-1309,\"errorMsg\":\"Driver[lidar-right] wait data timeout.\"},{\"name\":\"DRIVERS_LIDAR_LEFT\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_IMU\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_GPS_RAC\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_CAMERA360_FRONT\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_CAMERA360_LEFT\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_CAMERA360_RIGHT\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_CAMERA360_REAR\",\"startTime\":\"9223372036854775807\"},{\"name\":\"DRIVERS_LIDAR_REAR\",\"startTime\":\"9223372036854775807\"},{\"name\":\"MAP_INITIALIZATION\",\"startTime\":\"9223372036854775807\"},{\"name\":\"LOCALIZATION_IMU\",\"startTime\":\"9223372036854775807\"},{\"name\":\"LOCALIZATION_INIT\",\"startTime\":\"9223372036854775807\"},{\"name\":\"LOCALIZATION_LIDAR\",\"startTime\":\"9223372036854775807\"},{\"name\":\"LOCALIZATION_EKF\",\"startTime\":\"9223372036854775807\"},{\"name\":\"LOCALIZATION_TRANSFORM\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_CLOUD\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_GEO\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_IMAGE_F\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_IMAGE_B\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_SOD\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_FUSION\",\"startTime\":\"9223372036854775807\"},{\"name\":\"UNIFORM\",\"startTime\":\"9223372036854775807\"},{\"name\":\"RECORDER_LITTLE_BAG\",\"startTime\":\"9223372036854775807\"}]}],\"hardwareModule\":{\"bootCount\":1,\"moduleBootDetail\":[{\"name\":\"SELFCHECK_CONFIG\",\"startTime\":\"1692944183000000000\",\"endTime\":\"1692944183310000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":1013,\"errorMsg\":\"CODE_HARDWARE_CONFIG_SELECT_FINISH\"},{\"name\":\"MCU_RESPONSE\",\"startTime\":\"1692944183000000000\",\"endTime\":\"1692944183050000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":1004,\"errorMsg\":\"CODE_HARDWARE_GET_MCU_RESPONSE\"},{\"name\":\"MCU_UPDATE\",\"startTime\":\"1692944183000000000\",\"endTime\":\"1692944183270000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":1005,\"errorMsg\":\"CODE_HARDWARE_MCU_FIRMWARE_UPDATE_SUCCESS\"},{\"name\":\"NETWORK_CHECK\",\"startTime\":\"1692944183000000000\",\"endTime\":\"1692944183000000000\",\"bootStatus\":\"START_FAILED\",\"errorCode\":1211,\"errorMsg\":\"CODE_HARDWARE_NETWORK_CHECK\"},{\"name\":\"USB_CHECK\",\"startTime\":\"1692944183000000000\",\"endTime\":\"1692944183270000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":1305,\"errorMsg\":\"CODE_HARDWARE_USB_CHIP_NUM_CHECK_OK\"},{\"name\":\"PING_CHECK\",\"startTime\":\"1692944184000000000\",\"endTime\":\"1692944184200000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2019,\"errorMsg\":\"network check passed\"},{\"name\":\"CLOCK_SYNC\",\"startTime\":\"1692944201000000000\",\"endTime\":\"1692944217830000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2021,\"errorMsg\":\"clock sync success\"},{\"name\":\"DISK_CHECK\",\"startTime\":\"1692944201000000000\",\"endTime\":\"1692944201110000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2023,\"errorMsg\":\"xavier disk check passed\"},{\"name\":\"MOUNT_NFS\",\"startTime\":\"1692944201000000000\",\"endTime\":\"1692944201000000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2025,\"errorMsg\":\"mount nfs success\"},{\"name\":\"IMAGE_UPGRADE\",\"startTime\":\"1692944208000000000\",\"endTime\":\"1692944214100000000\",\"bootStatus\":\"START_FAILED\",\"errorCode\":2026,\"errorMsg\":\"image upgrade success\"},{\"name\":\"ROVER\",\"startTime\":\"1692944240000000000\",\"endTime\":\"1692944242080000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2018,\"errorMsg\":\"rover5 start finish!\"},{\"name\":\"PREPARE_ENGINE\",\"startTime\":\"1692944226000000000\",\"endTime\":\"1692944240480000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2017,\"errorMsg\":\"generate perception engine finish\"}]}},{\"nodeName\":\"Control\",\"ip\":\"***********\",\"startTime\":\"1692944118228575873\",\"endTime\":\"1692944601438123416\",\"bootStatus\":\"STARTING\",\"moduleBoot\":[{\"bootCount\":1,\"moduleBootDetail\":[{\"name\":\"DRIVERS_CHASSIS_RX\",\"startTime\":\"1692944244129119620\",\"endTime\":\"1692944245150119620\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_CHASSIS_TX\",\"startTime\":\"1692944245151499768\",\"endTime\":\"1692944245166499768\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_CAMERA_TRAFFIC_LEFT\",\"startTime\":\"1692944245167460327\",\"endTime\":\"1692944246200460327\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_CAMERA_TRAFFIC_RIGHT\",\"startTime\":\"1692944246201000495\",\"endTime\":\"1692944247220000495\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_JOYSTICK\",\"startTime\":\"1692944247220995885\",\"endTime\":\"1692944247226995885\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"MAP_INITIALIZATION\",\"startTime\":\"1692944247228092866\",\"endTime\":\"1692944254239092866\",\"bootStatus\":\"SHUT_DOWN_SUCCESS\",\"errorCode\":-2000,\"errorMsg\":\"Map initialization is not ready.\"},{\"name\":\"LOCALIZATION_TRANSFORM\",\"startTime\":\"9223372036854775807\"},{\"name\":\"MAP_UPDATER\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_TRAFFIC_LIGHT\",\"startTime\":\"9223372036854775807\"},{\"name\":\"ROUTING\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PREDICTION\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PLANNING\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PNC_STATUS\",\"startTime\":\"9223372036854775807\"},{\"name\":\"CONTROL\",\"startTime\":\"9223372036854775807\"},{\"name\":\"ARTIFICIAL_OBSTACLE\",\"startTime\":\"9223372036854775807\"}]},{\"bootCount\":2,\"moduleBootDetail\":[{\"name\":\"DRIVERS_CHASSIS_RX\",\"startTime\":\"1692944258260720726\",\"endTime\":\"1692944259265720726\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_CHASSIS_TX\",\"startTime\":\"1692944259267130597\",\"endTime\":\"1692944259273130597\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_CAMERA_TRAFFIC_LEFT\",\"startTime\":\"1692944259274314810\",\"endTime\":\"1692944260277314810\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_CAMERA_TRAFFIC_RIGHT\",\"startTime\":\"1692944260278354278\",\"endTime\":\"1692944261281354278\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_JOYSTICK\",\"startTime\":\"1692944261282843105\",\"endTime\":\"1692944261285843105\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"MAP_INITIALIZATION\",\"startTime\":\"1692944261286634316\",\"endTime\":\"1692944274293634316\",\"bootStatus\":\"SHUT_DOWN_SUCCESS\",\"errorCode\":-2000,\"errorMsg\":\"Map initialization is not ready.\"},{\"name\":\"LOCALIZATION_TRANSFORM\",\"startTime\":\"9223372036854775807\"},{\"name\":\"MAP_UPDATER\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_TRAFFIC_LIGHT\",\"startTime\":\"9223372036854775807\"},{\"name\":\"ROUTING\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PREDICTION\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PLANNING\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PNC_STATUS\",\"startTime\":\"9223372036854775807\"},{\"name\":\"CONTROL\",\"startTime\":\"9223372036854775807\"},{\"name\":\"ARTIFICIAL_OBSTACLE\",\"startTime\":\"9223372036854775807\"}]},{\"bootCount\":3,\"moduleBootDetail\":[{\"name\":\"DRIVERS_CHASSIS_RX\",\"startTime\":\"1692944279308032191\",\"endTime\":\"1692944280313032191\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_CHASSIS_TX\",\"startTime\":\"1692944280314371841\",\"endTime\":\"1692944280320371841\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_CAMERA_TRAFFIC_LEFT\",\"startTime\":\"1692944280321388087\",\"endTime\":\"1692944281323388087\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_CAMERA_TRAFFIC_RIGHT\",\"startTime\":\"1692944281325034241\",\"endTime\":\"1692944282328034241\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_JOYSTICK\",\"startTime\":\"1692944282329598600\",\"endTime\":\"1692944282333598600\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"MAP_INITIALIZATION\",\"startTime\":\"1692944282334677846\",\"endTime\":\"1692944294341677846\",\"bootStatus\":\"SHUT_DOWN_SUCCESS\",\"errorCode\":-2000,\"errorMsg\":\"Map initialization is not ready.\"},{\"name\":\"LOCALIZATION_TRANSFORM\",\"startTime\":\"9223372036854775807\"},{\"name\":\"MAP_UPDATER\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_TRAFFIC_LIGHT\",\"startTime\":\"9223372036854775807\"},{\"name\":\"ROUTING\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PREDICTION\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PLANNING\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PNC_STATUS\",\"startTime\":\"9223372036854775807\"},{\"name\":\"CONTROL\",\"startTime\":\"9223372036854775807\"},{\"name\":\"ARTIFICIAL_OBSTACLE\",\"startTime\":\"9223372036854775807\"}]},{\"bootCount\":4,\"moduleBootDetail\":[{\"name\":\"DRIVERS_CHASSIS_RX\",\"startTime\":\"1692944299359886074\",\"endTime\":\"1692944300364886074\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_CHASSIS_TX\",\"startTime\":\"1692944300366349191\",\"endTime\":\"1692944300372349191\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_CAMERA_TRAFFIC_LEFT\",\"startTime\":\"1692944300373384539\",\"endTime\":\"1692944301375384539\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_CAMERA_TRAFFIC_RIGHT\",\"startTime\":\"1692944301376441084\",\"endTime\":\"1692944302379441084\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_JOYSTICK\",\"startTime\":\"1692944302380810371\",\"endTime\":\"1692944302383810371\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"MAP_INITIALIZATION\",\"startTime\":\"1692944302384700591\",\"endTime\":\"1692944601437700591\",\"bootStatus\":\"START_FAILED\",\"errorCode\":-2000,\"errorMsg\":\"Map initialization is not ready.\"},{\"name\":\"LOCALIZATION_TRANSFORM\",\"startTime\":\"9223372036854775807\"},{\"name\":\"MAP_UPDATER\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PERC2_TRAFFIC_LIGHT\",\"startTime\":\"9223372036854775807\"},{\"name\":\"ROUTING\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PREDICTION\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PLANNING\",\"startTime\":\"9223372036854775807\"},{\"name\":\"PNC_STATUS\",\"startTime\":\"9223372036854775807\"},{\"name\":\"CONTROL\",\"startTime\":\"9223372036854775807\"},{\"name\":\"ARTIFICIAL_OBSTACLE\",\"startTime\":\"9223372036854775807\"}]}],\"hardwareModule\":{\"bootCount\":1,\"moduleBootDetail\":[{\"name\":\"SELFCHECK_CONFIG\",\"startTime\":\"1692944152000000000\",\"endTime\":\"1692944152030000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":1013,\"errorMsg\":\"CODE_HARDWARE_CONFIG_SELECT_FINISH\"},{\"name\":\"NETWORK_CHECK\",\"startTime\":\"1692944152000000000\",\"endTime\":\"1692944165790000000\",\"bootStatus\":\"START_FAILED\",\"errorCode\":1205,\"errorMsg\":\"CODE_HARDWARE_NET_ANOTHER_XAVIER_4G_NET_ERR\"},{\"name\":\"USB_CHECK\",\"startTime\":\"1692944152000000000\",\"endTime\":\"1692944152260000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":1305,\"errorMsg\":\"CODE_HARDWARE_USB_CHIP_NUM_CHECK_OK\"},{\"name\":\"PING_CHECK\",\"startTime\":\"1692944153000000000\",\"endTime\":\"1692944153190000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2019,\"errorMsg\":\"network check passed\"},{\"name\":\"CLOCK_SYNC\",\"startTime\":\"1692944201000000000\",\"endTime\":\"1692944248970000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2021,\"errorMsg\":\"clock sync success\"},{\"name\":\"DISK_CHECK\",\"startTime\":\"1692944201000000000\",\"endTime\":\"1692944201110000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2023,\"errorMsg\":\"xavier disk check passed\"},{\"name\":\"MOUNT_NFS\",\"startTime\":\"1692944201000000000\",\"endTime\":\"1692944201030000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2025,\"errorMsg\":\"mount nfs success\"},{\"name\":\"IMAGE_UPGRADE\",\"startTime\":\"1692944208000000000\",\"endTime\":\"1692944214430000000\",\"bootStatus\":\"START_FAILED\",\"errorCode\":2026,\"errorMsg\":\"image upgrade success\"},{\"name\":\"ROVER\",\"startTime\":\"1692944240000000000\",\"endTime\":\"1692944242080000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2018,\"errorMsg\":\"rover5 start finish!\"},{\"name\":\"PREPARE_ENGINE\",\"startTime\":\"1692944225000000000\",\"endTime\":\"1692944239480000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2017,\"errorMsg\":\"generate perception engine finish\"}]}}]}}}";

    // 正常驾驶模式
//    public static final String MESSAGE = "{\"vehicleId\":\"JDXT0002\",\"timestamp\":\"1692777264886524696\",\"sequenceNum\":22521,\"drivableDirection\":{\"enableFront\":true,\"enableBack\":true,\"enableLeft\":true,\"enableRight\":true},\"chassisStatus\":{\"batteryCapacity\":74.588241********,\"heading\":-0.****************,\"lat\":39.065964,\"lon\":117.02705,\"drivingMode\":\"DRIVEMODE_ENGAGED_INIT\",\"chargeState\":\"DISCHARGING\"},\"launchStatus\":[{\"state\":\"START_SUCCESS\",\"currentStartingModuleName\":\"RECORDER_LITTLE_BAG\",\"ip\":\"***********\"},{\"state\":\"START_SUCCESS\",\"currentStartingModuleName\":\"ARTIFICIAL_OBSTACLE\",\"ip\":\"***********\"}],\"solutionStatus\":{},\"planningStatus\":{\"resultCode\":{\"type\":\"ACCOMPLISHED\"},\"passNoSignalIntersectionStatus\":{}},\"routingStatus\":{\"resultCode\":{\"type\":\"OK\"},\"routingId\":1000,\"globalMileage\":6306.************,\"stopResult\":[{\"stopId\":2655,\"routingMileage\":3022.*************},{\"stopId\":592,\"routingMileage\":3283.*************}],\"currentStopFinishedMileage\":3022.*************,\"naviRefreshId\":1,\"naviSequenceNum\":3,\"missionStatus\":\"AS_ARRIVED\"},\"wheelEntity\":[{\"position\":\"WHEEL_POSITION_FRONT_LEFT\",\"pressure\":256,\"pressureStatus\":\"TIRE_PRESSURE_NORMAL\"},{\"position\":\"WHEEL_POSITION_FRONT_RIGHT\",\"pressure\":264,\"pressureStatus\":\"TIRE_PRESSURE_NORMAL\"},{\"position\":\"WHEEL_POSITION_BACK_LEFT\",\"pressure\":248,\"pressureStatus\":\"TIRE_PRESSURE_NORMAL\"},{\"position\":\"WHEEL_POSITION_BACK_RIGHT\",\"pressure\":260,\"pressureStatus\":\"TIRE_PRESSURE_NORMAL\"}],\"otaUpdaterStatus\":{\"versions\":[{\"name\":\"rover\",\"curVersion\":\"1.20.4.230731_release_JP46_engine\",\"otaVersion\":\"rover[2-1.20.4.230731_release_JP46_engine]\",\"localVersions\":[\"rover[2-1.17.2.1.230413_release_JP46_engine]\",\"rover[2-1.17.3.230427_release_JP46_engine]\",\"rover[2-1.18.0.1.230518_release_JP46_engine]\",\"rover[2-1.19.3.230629_release_JP46_engine]\",\"rover[2-1.20.4.230731_release_JP46_engine]\"]},{\"name\":\"rover-conf\",\"curVersion\":\"conf202307141143270002\",\"otaVersion\":\"rover-conf[2-conf202307141143270002]\",\"localVersions\":[\"rover-conf[2-conf202302270924430007]\",\"rover-conf[2-conf202303212029160003]\",\"rover-conf[2-conf202304061348490002]\",\"rover-conf[2-conf202304071041060002]\",\"rover-conf[2-conf202307141143270002]\"]},{\"name\":\"jdmap\",\"curVersion\":\"161-210433\",\"localVersions\":[\"[2-161-210432]\",\"[2-161-210433]\"]}],\"updating\":{\"processStatus\":{\"loadProgress\":{}}}},\"localizationConfidence\":{\"confidenceValue\":5.0,\"matchingIterations\":2,\"ndtTransLikelihood\":3.6201317024248536}}";

    // 按钮急停
//    public static final String MESSAGE = "{\"vehicleId\":\"JDXT0002\",\"timestamp\":\"1692777264730849348\",\"sequenceNum\":29701,\"drivableDirection\":{\"enableFront\":true,\"enableBack\":true,\"enableLeft\":true,\"enableRight\":true},\"abnormal\":[{\"moduleName\":\"FeatureCollector\",\"errorCode\":-13053,\"errorLevel\":\"ERROR\",\"errorMsg\":\"ErrorCode: ERROR_GUARDIAN_CONTROL_BRAKE_RESPONSE, Detail: [Brake response is abnormal. Mean response percentage: 22.400000].\",\"errorCodeString\":\"ERROR_GUARDIAN_CONTROL_BRAKE_RESPONSE\",\"timestamp\":\"1692749370352258777\"}],\"chassisStatus\":{\"batteryCapacity\":66.**************,\"heading\":2.***************,\"lat\":39.77272,\"lon\":116.51994,\"drivingMode\":\"DRIVEMODE_TAKEOVER_EMERGENCY_STOP\",\"chargeState\":\"DISCHARGING\"},\"launchStatus\":[{\"state\":\"START_SUCCESS\",\"currentStartingModuleName\":\"UNIFORM\",\"ip\":\"***********\"},{\"state\":\"START_SUCCESS\",\"currentStartingModuleName\":\"DRIVERS_JOYSTICK\",\"ip\":\"***********\"}],\"solutionStatus\":{},\"planningStatus\":{\"resultCode\":{\"type\":\"ACCOMPLISHED\"},\"passNoSignalIntersectionStatus\":{}},\"routingStatus\":{\"resultCode\":{\"type\":\"OK\"},\"routingId\":1002,\"globalMileage\":4013.*************,\"stopResult\":[{\"stopId\":546,\"routingMileage\":1624.*************},{\"stopId\":544,\"routingMileage\":2389.*************}],\"mileageToNextStop\":1624.*************,\"naviRefreshId\":3,\"naviSequenceNum\":9,\"missionStatus\":\"AS_ARRIVED\"},\"wheelEntity\":[{\"position\":\"WHEEL_POSITION_FRONT_RIGHT\",\"pressure\":220,\"pressureStatus\":\"TIRE_PRESSURE_NORMAL\"},{\"position\":\"WHEEL_POSITION_BACK_LEFT\",\"pressure\":224,\"pressureStatus\":\"TIRE_PRESSURE_NORMAL\"},{\"position\":\"WHEEL_POSITION_BACK_RIGHT\",\"pressure\":236,\"pressureStatus\":\"TIRE_PRESSURE_NORMAL\"},{\"position\":\"WHEEL_POSITION_FRONT_LEFT\",\"pressure\":240,\"pressureStatus\":\"TIRE_PRESSURE_NORMAL\"}],\"otaUpdaterStatus\":{\"versions\":[{\"name\":\"rover\",\"curVersion\":\"1.19.3.230629_release_JP46_engine\",\"otaVersion\":\"rover[2-1.20.4.230731_release_JP46_engine]\",\"localVersions\":[\"rover[2-1.17.3_ukf_cyclist_release_JP46_engine]\",\"rover[2-1.18.0.1.230518_release_JP46_engine]\",\"rover[2-1.18.0.1_fixcore_release_JP46_engine]\",\"rover[2-1.19.3.230629_release_JP46_engine]\",\"rover[2-1.20.4.230731_release_JP46_engine]\"]},{\"name\":\"rover-conf\",\"curVersion\":\"conf202307141141390002\",\"otaVersion\":\"rover-conf[2-conf202307141141390002]\",\"localVersions\":[\"rover-conf[2-conf202303181252450003]\",\"rover-conf[2-conf202304201037320002]\",\"rover-conf[2-conf202304261538240006]\",\"rover-conf[2-conf202305092054500002]\",\"rover-conf[2-conf202307141141390002]\"]},{\"name\":\"jdmap\",\"curVersion\":\"149-201451\",\"localVersions\":[\"[2-149-201452]\",\"[2-149-201451]\",\"[2-149-201449]\"]}],\"updating\":{\"processStatus\":{\"loadProgress\":{}}}},\"localizationConfidence\":{}}";

    // 异常
//    public static final String MESSAGE = "{\"requestHeader\":{\"messageTypeRequest\":[{\"messageType\":\"ABNORMAL\",\"needResponse\":true,\"retry\":true}],\"requestId\":\"1692946802521000221\",\"vehicleName\":\"JDZ0038\",\"requestTime\":\"1692946802521943793\"},\"reportStatisticsData\":{\"runtimeUuid\":\"1692944231919518260\",\"abnormalData\":[{\"moduleName\":\"DRIVERS_LIDAR_TOP\",\"errorCode\":-1304,\"errorLevel\":\"ERROR\",\"errorMsg\":\"ErrorCode: ERROR_DRIVER_CLASS_DATA_TYPE_ERROR, Message: Driver[lidar-top] data type is not PointCloud.\",\"startTime\":\"1692946800005609282\"},{\"moduleName\":\"DRIVERS_LIDAR_RIGHT\",\"errorCode\":-1304,\"errorLevel\":\"FATAL\",\"errorMsg\":\"ErrorCode: ERROR_DRIVER_CLASS_DATA_TYPE_ERROR, Message: Driver[lidar-right] data type is not PointCloud.\",\"startTime\":\"1692944257342992796\",\"endTime\":\"1692946802521456079\"}]}}";
    // 产生异常
//        public static final String MESSAGE = "{\"requestHeader\":{\"messageTypeRequest\":[{\"messageType\":\"ABNORMAL\",\"needResponse\":true,\"retry\":true}],\"requestId\":\"1693893461867000428\",\"vehicleName\":\"JDZ0038\",\"requestTime\":\"1693893461867688874\"},\"reportStatisticsData\":{\"runtimeUuid\":\"1693884057178211604\",\"abnormalData\":[{\"moduleName\":\"ComponentFrequence\",\"errorCode\":-13501,\"errorLevel\":\"ERROR\",\"errorMsg\":\"ErrorCode: ERROR_GUARDIAN_COMPONENT_HZ_NOT_MATCH, Detail: [PLANNING hz warn,].\",\"startTime\":\"1693893461725292273\"}]}}";
//    public static final String MESSAGE = "{\"requestHeader\":{\"messageTypeRequest\":[{\"messageType\":\"ABNORMAL\",\"needResponse\":true,\"retry\":true}],\"requestId\":\"1693899400758001247\",\"vehicleName\":\"JDZ0038\",\"requestTime\":\"1693899400758478217\"},\"reportStatisticsData\":{\"runtimeUuid\":\"1693884057178211604\",\"abnormalData\":[{\"moduleName\":\"DRIVERS_LIDAR_REAR\",\"errorCode\":-1142,\"errorLevel\":\"FATAL\",\"errorMsg\":\"ErrorCode: ERROR_DRIVER_LIDAR_DATA_CONTINUED_ALL_NAN_POINT, Message: [Driver:lidar-rear] Failed to process data, lidar point nan percent[92.8%] too high.\",\"startTime\":\"1693899399659986054\"},{\"moduleName\":\"ComponentFrequence\",\"errorCode\":-13501,\"errorLevel\":\"ERROR\",\"errorMsg\":\"ErrorCode: ERROR_GUARDIAN_COMPONENT_HZ_NOT_MATCH, Detail: [DRIVERS_LIDAR_REAR hz error,].\",\"startTime\":\"1693899399725948525\",\"endTime\":\"1693899400758088840\"}]}}";
//        public static final String MESSAGE = "{\"requestHeader\":{\"messageTypeRequest\":[{\"messageType\":\"ABNORMAL\",\"needResponse\":true,\"retry\":true}],\"requestId\":\"1693899403772001249\",\"vehicleName\":\"JDZ0038\",\"requestTime\":\"1693899403772111552\"},\"reportStatisticsData\":{\"runtimeUuid\":\"1693884057178211604\",\"abnormalData\":[{\"moduleName\":\"DRIVERS_LIDAR_REAR\",\"errorCode\":-1304,\"errorLevel\":\"ERROR\",\"errorMsg\":\"ErrorCode: ERROR_DRIVER_CLASS_DATA_TYPE_ERROR, Message: Driver[lidar-rear] data type is not PointCloud.\",\"startTime\":\"1693899402663956373\"}]}}";
//        public static final String MESSAGE = "{\"requestHeader\":{\"messageTypeRequest\":[{\"messageType\":\"ABNORMAL\",\"needResponse\":true,\"retry\":true}],\"requestId\":\"1724291890381000273\",\"vehicleName\":\"JDK8163\",\"requestTime\":\"1724291890381156312\"},\"reportStatisticsData\":{\"runtimeUuid\":\"1724287282486111516\",\"abnormalData\":[{\"moduleName\":\"ComponentFrequence\",\"errorCode\":-13501,\"errorLevel\":\"FATAL\",\"errorMsg\":\"ErrorCode: ERROR_GUARDIAN_COMPONENT_HZ_NOT_MATCH, Detail: [ROUTING hz warn,].\",\"startTime\":\"1724291889534384967\"}]}}";
    // 结束异常
//    public static final String MESSAGE = "{\"requestHeader\":{\"messageTypeRequest\":[{\"messageType\":\"ABNORMAL\",\"needResponse\":true,\"retry\":true}],\"requestId\":\"1693893461867000428\",\"vehicleName\":\"JDZ0038\",\"requestTime\":\"1693893461867688874\"},\"reportStatisticsData\":{\"runtimeUuid\":\"1693884057178211604\",\"abnormalData\":[{\"moduleName\":\"ComponentFrequence\",\"errorCode\":-13501,\"errorLevel\":\"ERROR\",\"errorMsg\":\"ErrorCode: ERROR_GUARDIAN_COMPONENT_HZ_NOT_MATCH, Detail: [PLANNING hz warn,].\",\"startTime\":\"1693893461725292273\",\"endTime\":\"1692946802521456079\"}]}}";
//    public static final String MESSAGE = "{\"requestHeader\":{\"messageTypeRequest\":[{\"messageType\":\"ABNORMAL\",\"needResponse\":true,\"retry\":true}],\"requestId\":\"1693899400758001247\",\"vehicleName\":\"JDZ0038\",\"requestTime\":\"1693899400758478217\"},\"reportStatisticsData\":{\"runtimeUuid\":\"1693884057178211604\",\"abnormalData\":[{\"moduleName\":\"DRIVERS_LIDAR_REAR\",\"errorCode\":-1142,\"errorLevel\":\"FATAL\",\"errorMsg\":\"ErrorCode: ERROR_DRIVER_LIDAR_DATA_CONTINUED_ALL_NAN_POINT, Message: [Driver:lidar-rear] Failed to process data, lidar point nan percent[92.8%] too high.\",\"startTime\":\"1693899399659986054\",\"endTime\":\"1693899399659986054\"},{\"moduleName\":\"ComponentFrequence\",\"errorCode\":-13501,\"errorLevel\":\"ERROR\",\"errorMsg\":\"ErrorCode: ERROR_GUARDIAN_COMPONENT_HZ_NOT_MATCH, Detail: [DRIVERS_LIDAR_REAR hz error,].\",\"startTime\":\"1693899399725948525\",\"endTime\":\"1693899400758088840\"}]}}";
//    public static final String MESSAGE = "{\"requestHeader\":{\"messageTypeRequest\":[{\"messageType\":\"ABNORMAL\",\"needResponse\":true,\"retry\":true}],\"requestId\":\"1693899403772001249\",\"vehicleName\":\"JDZ0038\",\"requestTime\":\"1693899403772111552\"},\"reportStatisticsData\":{\"runtimeUuid\":\"1693884057178211604\",\"abnormalData\":[{\"moduleName\":\"DRIVERS_LIDAR_REAR\",\"errorCode\":-1304,\"errorLevel\":\"ERROR\",\"errorMsg\":\"ErrorCode: ERROR_DRIVER_CLASS_DATA_TYPE_ERROR, Message: Driver[lidar-rear] data type is not PointCloud.\",\"startTime\":\"1693899402663956373\",\"endTime\":\"1693899402663956373\"}]}}";
    // CPU温度过高
    public static final String MESSAGE = "{\"requestHeader\":{\"messageTypeRequest\":[{\"messageType\":\"ABNORMAL\",\"needResponse\":true,\"retry\":true}],\"requestId\":\"1726713048886001110\",\"vehicleName\":\"JDXT002\",\"requestTime\":\"1726713048886725201\"},\"reportStatisticsData\":{\"runtimeUuid\":\"1726706524832586687\",\"abnormalData\":[{\"moduleName\":\"SystemCPUTempature\",\"errorCode\":-13708,\"errorLevel\":\"ERROR\",\"errorMsg\":\"ErrorCode: ERROR_GUARDIAN_SYSTEM_CPU_TEMPATURE_TOO_HIGH, Detail: [*********** cpu tempature error, value: 90.000000].\",\"startTime\":\"1726713033784897456\",\"endTime\":\"1726713048885938766\"},{\"moduleName\":\"SystemCPUTempature\",\"errorCode\":-13708,\"errorLevel\":\"FATAL\",\"errorMsg\":\"ErrorCode: ERROR_GUARDIAN_SYSTEM_CPU_TEMPATURE_TOO_HIGH, Detail: [*********** cpu tempature fatal, value: 90.375000].\",\"startTime\":\"1726713047789278321\"}]}}";
    // Routing规划失败
//    public static final String MESSAGE = "{\"requestHeader\":{\"messageTypeRequest\":[{\"messageType\":\"ABNORMAL\",\"needResponse\":true,\"retry\":true}],\"requestId\":\"1726714127498000223\",\"vehicleName\":\"JDXT002\",\"requestTime\":\"1726714127498320584\"},\"reportStatisticsData\":{\"runtimeUuid\":\"1726713714586869374\",\"abnormalData\":[{\"moduleName\":\"ROUTING\",\"errorCode\":-7006,\"errorLevel\":\"FATAL\",\"errorMsg\":\"ErrorCode: ERROR_ROUTING_FIND_NO_LINK, Message: No map connection from the start point to the end point of navigation. start index: -1 end index: 0, Detail: [From: [{module_name: LOCALIZATION_EKF_3, sequence_num: 30219}{module_name: LOCALIZATION_EKF_3, sequence_num: 30212}{module_name: ARTIFICIAL_OBSTACLE_2, sequence_num: 2599}{module_name: BUSINESS_REQUEST_FORWARDER, sequence_num: 0}]].\",\"startTime\":\"1726714080415047098\",\"endTime\":\"1726714127497951435\"}]}}";
    // 遇阻停车开始
//    public static final String MESSAGE = "{\"requestHeader\":{\"messageTypeRequest\":[{\"messageType\":\"ABNORMAL\",\"needResponse\":true,\"retry\":true}],\"requestId\":\"1693902364877001391\",\"vehicleName\":\"JDZ0038\",\"requestTime\":\"1693902364877848454\"},\"reportStatisticsData\":{\"runtimeUuid\":\"1693884057178211604\",\"abnormalData\":[{\"moduleName\":\"StuckAlarm\",\"errorCode\":-13200,\"errorLevel\":\"ERROR\",\"errorMsg\":\"ErrorCode: ERROR_GUARDIAN_VEHICLE_STUCK, Detail: [The vehicle is stuck!].\",\"startTime\":\"1693902364745874841\"}]}}";
    // 遇阻停车结束
//    public static final String MESSAGE = "{\"requestHeader\":{\"messageTypeRequest\":[{\"messageType\":\"ABNORMAL\",\"needResponse\":true,\"retry\":true}],\"requestId\":\"1693902364877001391\",\"vehicleName\":\"JDZ0038\",\"requestTime\":\"1693902364877848454\"},\"reportStatisticsData\":{\"runtimeUuid\":\"1693884057178211604\",\"abnormalData\":[{\"moduleName\":\"StuckAlarm\",\"errorCode\":-13200,\"errorLevel\":\"ERROR\",\"errorMsg\":\"ErrorCode: ERROR_GUARDIAN_VEHICLE_STUCK, Detail: [The vehicle is stuck!].\",\"startTime\":\"1693902364745874841\",\"endTime\":\"1693902364745874841\"}]}}";
//    public static final String MESSAGE = "{\"requestHeader\":{\"messageTypeRequest\":[{\"messageType\":\"ABNORMAL\",\"needResponse\":true,\"retry\":true}],\"requestId\":\"1694154250625000163\",\"vehicleName\":\"JDK0896\",\"requestTime\":\"1694154250625438655\"},\"reportStatisticsData\":{\"runtimeUuid\":\"1694148392713246264\",\"abnormalData\":[{\"moduleName\":\"FeatureCollector\",\"errorCode\":-13053,\"errorLevel\":\"ERROR\",\"errorMsg\":\"ErrorCode: ERROR_GUARDIAN_CONTROL_BRAKE_RESPONSE, Detail: [Brake response is abnormal. Mean response percentage: 4.800000].\",\"startTime\":\"1694154250594378995\"}]}}";
    //public static final String MESSAGE = "{\"requestHeader\":{\"messageTypeRequest\":[{\"messageType\":\"ABNORMAL\",\"needResponse\":true,\"retry\":true}],\"requestId\":\"1694154251631000165\",\"vehicleName\":\"JDK0896\",\"requestTime\":\"1694154251631630830\"},\"reportStatisticsData\":{\"runtimeUuid\":\"1694148392713246264\",\"abnormalData\":[{\"moduleName\":\"LOCALIZATION_LIDAR\",\"errorCode\":-4204,\"errorLevel\":\"FATAL\",\"errorMsg\":\"ErrorCode: ERROR_LOCALIZATION_MATCHING_TIMEOUT, Message: Matching timeout or lidar msg error., Detail: [From: [{module_name: DRIVERS_LIDAR_TOP_0, sequence_num: 57807}{module_name: LOCALIZATION_IMU_2, sequence_num: 570005}{module_name: LOCALIZATION_INIT_3, sequence_num: 57444}{module_name: DRIVERS_GPS_RAC_1, sequence_num: 57719}{module_name: DRIVERS_GPS_RAC_1, sequence_num: 57718}]].\",\"startTime\":\"1694154249449909639\"},{\"moduleName\":\"LOCALIZATION_EKF\",\"errorCode\":-4309,\"errorLevel\":\"FATAL\",\"errorMsg\":\"ErrorCode: ERROR_LOCALIZATION_POSE_FUSION_LOST_LIDARMATCHING, Message: Gets too many same sequences num data from lidar matching., Detail: [From: [{module_name: DRIVERS_IMU_0, sequence_num: 567007}]].\",\"startTime\":\"1694154250333956329\"},{\"moduleName\":\"DrivableDirection\",\"errorCode\":-13600,\"errorLevel\":\"FATAL\",\"errorMsg\":\"ErrorCode: ERROR_GUARDIAN_DRIVABLE_DIRECTION_BLOCKED, Detail: [Emergency stop because no newer drivable area message recived.].\",\"startTime\":\"1694154250867576588\"},{\"moduleName\":\"FeatureCollector\",\"errorCode\":-13053,\"errorLevel\":\"ERROR\",\"errorMsg\":\"ErrorCode: ERROR_GUARDIAN_CONTROL_BRAKE_RESPONSE, Detail: [Brake response is abnormal. Mean response percentage: 4.800000].\",\"startTime\":\"1694154250594378995\",\"endTime\":\"1694154251630931471\"},{\"moduleName\":\"ComponentFrequence\",\"errorCode\":-13501,\"errorLevel\":\"FATAL\",\"errorMsg\":\"ErrorCode: ERROR_GUARDIAN_COMPONENT_HZ_NOT_MATCH, Detail: [LOCALIZATION_LIDAR hz fatal,].\",\"startTime\":\"1694154249416030758\",\"endTime\":\"1694154251630934831\"}]}}";
    //

    @Test
    void sendWsMessage() throws Exception {
//        String host = "127.0.0.1:8011";
        String host = "jdxserver-up-beta.jd.local";
        String url = "ws://" + host + "/upstream/ws/report/" + VEHICLE_NAME;
        try {
            TestWebSocketClient myClient = new TestWebSocketClient(new URI(url));
            myClient.connect();
            while (!myClient.getReadyState().equals(ReadyState.OPEN)) {
                log.info("连接中。。。");
                Thread.sleep(1000);
            }

            // 连接成功往websocket服务端发送数据
            long startTime = System.currentTimeMillis() * NumberConstant.MILLION;
            ReportDto.ReportDTO.Builder infoOrignBuilder1 = buildAbnormal(MESSAGE, startTime, 0L);
            sendMessage(myClient, infoOrignBuilder1);
            log.info("发送report信息1完成!");
            Thread.sleep(120000);
            ReportDto.ReportDTO.Builder infoOrignBuilder2 = buildAbnormal(MESSAGE, startTime, System.currentTimeMillis() * NumberConstant.MILLION);
            sendMessage(myClient, infoOrignBuilder2);
            log.info("发送report信息2完成!");
            Thread.sleep(1000);
            myClient.close();
        } catch (Exception e) {
            e.printStackTrace();
        }

        Thread.sleep(1000);
    }

    /**
     * 发送消息
     */
    private void sendMessage(TestWebSocketClient myClient, ReportDto.ReportDTO.Builder infoOrignBuilder) {
        ReportDto.ReportDTO infoOrign = infoOrignBuilder.build();
        byte[] result = infoOrign.toByteArray();
        myClient.send(result);
    }

    private ReportDto.ReportDTO.Builder buildAbnormal(String msg, Long startTime, Long endTime) {
        ReportDto.ReportDTO.Builder infoOrignBuilder = ReportDto.ReportDTO.newBuilder();
        try {
            // 直接解析json,得到protobuf数据,也可以自己构造,麻烦一些
            JsonFormat.parser().merge(msg, infoOrignBuilder);
        } catch (InvalidProtocolBufferException e) {
            e.printStackTrace();
        }

        // 设置车号
        infoOrignBuilder.getRequestHeaderBuilder().setVehicleName(VEHICLE_NAME);
        // 设置上报时间为当前时间
        infoOrignBuilder.getRequestHeaderBuilder().setRequestTime(System.currentTimeMillis() * NumberConstant.MILLION);
        for (ReportStatistics.AbnormalData.Builder builder : infoOrignBuilder.getReportStatisticsDataBuilder().getAbnormalDataBuilderList()) {
            builder.setStartTime(startTime);
            if(!Objects.isNull(endTime)){
                builder.setEndTime(endTime);
            }
        }
        return infoOrignBuilder;
    }

    private static void setFatalAbnormal(ReportDto.ReportDTO.Builder infoOrignBuilder, Long endTime) {
        {
            ReportStatistics.AbnormalData.Builder abnormalBuilder = ReportStatistics.AbnormalData.newBuilder();
            abnormalBuilder.setStartTime(START_TIME_NANOSECOND);
            if (!Objects.isNull(endTime)) {
                abnormalBuilder.setEndTime(endTime);
            }
            abnormalBuilder.setErrorCode(-13000);
            abnormalBuilder.setErrorLevel("FATAL");
            abnormalBuilder.setErrorMsg("ErrorCode: ERROR_GUARDIAN_NOT_INITIALIZED, Message: Component not initialized.");
            abnormalBuilder.setModuleName("LOCALIZATION_INIT");
            infoOrignBuilder.getReportStatisticsDataBuilder().setAbnormalData(0, abnormalBuilder);
        }
        {
            ReportStatistics.AbnormalData.Builder abnormalBuilder2 = ReportStatistics.AbnormalData.newBuilder();
            abnormalBuilder2.setStartTime(START_TIME_NANOSECOND);
            if (!Objects.isNull(endTime)) {
                abnormalBuilder2.setEndTime(endTime);
            }
            abnormalBuilder2.setErrorCode(-13002);
            abnormalBuilder2.setErrorLevel("FATAL");
            abnormalBuilder2.setErrorMsg("ErrorCode: ERROR_GUARDIAN_NOT_INITIALIZED, Message: Component not initialized.");
            abnormalBuilder2.setModuleName("LOCALIZATION_INIT");
            infoOrignBuilder.getReportStatisticsDataBuilder().setAbnormalData(1, abnormalBuilder2);
        }
    }
}