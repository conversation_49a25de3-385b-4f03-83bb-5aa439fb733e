package com.jdx.rover.server.upstream.web.websocket;

import lombok.extern.slf4j.Slf4j;
import org.java_websocket.client.WebSocketClient;
import org.java_websocket.handshake.ServerHandshake;

import java.net.URI;
import java.util.Map;

/**
 * websocket客户端监听类
 *
 * <AUTHOR>
 */
@Slf4j
public class TestWebSocketClient extends WebSocketClient {
  public TestWebSocketClient(URI serverUri) {
    super(serverUri);
  }

  public TestWebSocketClient(URI serverUri, Map<String, String> httpHeaders) {
    super(serverUri, httpHeaders);
  }

  @Override
  public void onOpen(ServerHandshake serverHandshake) {
    log.info(">>>>>>>>>>>websocket open");
  }

  @Override
  public void onMessage(String s) {
    log.info(">>>>>>>>>> websocket message,{}", s);

  }

  @Override
  public void onClose(int i, String s, boolean b) {
    log.info(">>>>>>>>>>>websocket close");
  }

  @Override
  public void onError(Exception e) {
    log.error(">>>>>>>>>websocket error {}", e);
  }
}