server:
  port: 8011
spring:
  cloud:
    nacos:
      discovery:
        server-addr: http://jdxnacos-beta.jd.local/
        username: nacos
        password: nacos-beta@jdlX2022
  kafka: # 默认的kafka集群,发送String
    bootstrap-servers: broker-kafka-poiybufu79-az1-0.jvessel-open-hb.jdcloud.com:9092,broker-kafka-poiybufu79-az1-1.jvessel-open-hb.jdcloud.com:9092,broker-kafka-poiybufu79-az1-2.jvessel-open-hb.jdcloud.com:9092
  data:
    redis:
      database: 2
      host: redis-hb3hpfz16cbi-proxy-nlb.jvessel-open-hb.jdcloud.com
      password: jdlX2022
project:
  kafka:
    second:
      bootstrap-servers: broker-kafka-poiybufu79-az1-0.jvessel-open-hb.jdcloud.com:9092,broker-kafka-poiybufu79-az1-1.jvessel-open-hb.jdcloud.com:9092,broker-kafka-poiybufu79-az1-2.jvessel-open-hb.jdcloud.com:9092

websocket:
  maxTextMessageBufferSize: 8192
  maxBinaryMessageBufferSize: 81920
  maxSessionIdleTimeout: 600000

jmq:
  address: nameserver.jmq.jd.local:80
  password: f954c10ce13a4570884bc228cf7bca16
  app: serverupstreamtest