spring:
  application:
    name: rover-server-upstream
  profiles:
    active: "@activatedProperties@"
  cloud:
    nacos:
      discovery:
        group: ${spring.profiles.active}_group
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss #jackson日期格式化,用于@RestController返回
  kafka:
    producer:
      retries: 5
  data:
    redis:
      database: 1
      lettuce:
        pool:
          max-active: 500
          max-wait: -1ms
          min-idle: 0
          max-idle: 50
logging:
  config: classpath:log4j2.xml

websocket:
  maxTextMessageBufferSize: 81920
  maxBinaryMessageBufferSize: 1148000
  maxSessionIdleTimeout: 600000

project:
  kafka:
    second:
      bootstrap-servers: *************:9092

jsf:
  enable-provider: false
  enable-consumer: false

jmq:
  address: test-nameserver.jmq.jd.local:50088
  password: 3f9e1de79acd4e3ba7e1e1aec51bf274
  app: roverServer
  topic:
    producer:
      serverBusOrigin: server_bus_origin_${spring.profiles.active}
      serverUpstreamGuardianOrigin: server_upstream_guardian_origin_${spring.profiles.active}
      serverReportOrigin: server_report_origin_${spring.profiles.active}
      serverLocalView: server_local_view_${spring.profiles.active}
      serverGuardianConnect: server_guardian_connect_${spring.profiles.active}