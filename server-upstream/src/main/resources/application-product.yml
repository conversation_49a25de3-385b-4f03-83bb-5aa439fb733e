server:
  port: 8011
spring:
  cloud:
    nacos:
      discovery:
        server-addr: http://jdxnacos.jd.local/
        username: nacos
        password: nacos@jdlX2022
  kafka: # 默认的kafka集群,发送String
    bootstrap-servers: broker-kafka-dzk69k556l-az1-0.jvessel-open-hb.jdcloud.com:9092,broker-kafka-dzk69k556l-az1-1.jvessel-open-hb.jdcloud.com:9092,broker-kafka-dzk69k556l-az1-2.jvessel-open-hb.jdcloud.com:9092
  data:
    redis:
      database: 2
      host: redis-ozj2fnhyspho-proxy-nlb.jvessel-open-hb.jdcloud.com
      password: jdlX2022
project:
  kafka:
    second:
      bootstrap-servers: kafka-vukl3p8rk4-kafka-bootstrap.kafka-vukl3p8rk4-hb.jvessel2.jdcloud.com:9092

jmq:
  address: nameserver.jmq.jd.local:80
  password: 2e433bdb74b1485e994e65afcb74fe42
  app: roverserverupstream
  topic:
    producer:
      serverBusOrigin: server_bus_origin
      serverUpstreamGuardianOrigin: server_upstream_guardian_origin
      serverReportOrigin: server_report_origin
      serverLocalView: server_local_view
      serverGuardianConnect: server_guardian_connect