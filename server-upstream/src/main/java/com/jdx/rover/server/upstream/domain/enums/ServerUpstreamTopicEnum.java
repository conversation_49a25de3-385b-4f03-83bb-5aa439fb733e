/*
 * Copyright (c) 2021 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.server.upstream.domain.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * <p>
 * kafka主题枚举
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum ServerUpstreamTopicEnum {
  SERVER_UPSTREAM_GUARDIAN_ORIGIN("server_upstream_guardian_origin", "guardian原始信息"),
  SERVER_GUARDIAN_ORIGIN("server_guardian_origin", "guardian原始信息"),
  SERVER_UPSTREAM_BUS_ORIGIN("server_upstream_bus_origin", "bus原始信息"),

  SERVER_BUS_ORIGIN("server_bus_origin", "bus原始信息"),
  SERVER_REPORT_ORIGIN("server_report_origin", "report原始信息"),
  ;

  /**
   * 值
   */
  private String value;

  /**
   * 显示标题
   */
  private String title;
}
