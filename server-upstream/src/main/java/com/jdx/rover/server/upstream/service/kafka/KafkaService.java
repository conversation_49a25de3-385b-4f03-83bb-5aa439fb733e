package com.jdx.rover.server.upstream.service.kafka;

import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.serialization.ByteArraySerializer;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.core.ProducerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class KafkaService {

  @Value("${spring.kafka.bootstrap-servers}")
  private String bootstrapServers;


  private KafkaTemplate<String, byte[]> byteTemplate;

  @PostConstruct
  void init() {
    this.byteTemplate = createByteTemplate();
  }

  public KafkaTemplate<String, byte[]> getByteTemplate() {
    return byteTemplate;
  }

  /**
   * 创建发送byte[]数组kafka模板
   */
  private KafkaTemplate<String, byte[]> createByteTemplate() {
    Map<String, Object> props = new HashMap<>();
    props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
    props.put(ProducerConfig.RETRIES_CONFIG, 0);
    props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
    props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, ByteArraySerializer.class);
    ProducerFactory<String, byte[]> pf = new DefaultKafkaProducerFactory(props);
    KafkaTemplate<String, byte[]> template = new KafkaTemplate<>(pf);
    return template;
  }
}
