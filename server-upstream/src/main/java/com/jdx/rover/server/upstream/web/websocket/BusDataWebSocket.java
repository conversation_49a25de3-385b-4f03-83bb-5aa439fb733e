package com.jdx.rover.server.upstream.web.websocket;

import com.jdx.rover.server.upstream.config.ServletAwareConfigurator;
import jdx.rover.bus.data.dto.BusDataDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

import jakarta.websocket.CloseReason;
import jakarta.websocket.OnClose;
import jakarta.websocket.OnError;
import jakarta.websocket.OnMessage;
import jakarta.websocket.OnOpen;
import jakarta.websocket.Session;
import jakarta.websocket.server.PathParam;
import jakarta.websocket.server.ServerEndpoint;
import java.io.IOException;
import java.io.InputStream;
import java.nio.ByteBuffer;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <p>
 * 车辆上报总线数据WebSocket连接信息
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@ServerEndpoint(value = "/upstream/ws/bus/{clientName}", configurator = ServletAwareConfigurator.class)
@Component
@Deprecated
public class BusDataWebSocket {

  /**
   * 记录当前在线连接数
   */
  private static final AtomicInteger onlineCount = new AtomicInteger(0);

  /**
   * 连接客户端名称,此处为车辆名称
   */
  private String clientName;

  /**
   * WebSocket是线程安全的,有用户连接时才会创建一个新的端点实例,所以spring依赖注入会失败,改成静态变量就好了
   */
  private static KafkaTemplate<String, String> kafkaTemplate;

  @Autowired
  public void setKafkaTemplate(KafkaTemplate<String, String> kafkaTemplate) {
    BusDataWebSocket.kafkaTemplate = kafkaTemplate;
  }

  /**
   * 连接建立
   */
  @OnOpen
  public void onOpen(Session session, @PathParam("clientName") String clientName) {
    this.clientName = clientName;
    onlineCount.incrementAndGet();
    log.info("新增连接: {}, 连接ID：{}, 当前在线数: {}.", this.clientName, session.getId(), onlineCount.get());
  }

  /**
   * 传输消息
   */
  @OnMessage
  public void onMessage(InputStream message, Session session) {
    try {
      BusDataDto.BusDataDTO busDataDTO = BusDataDto.BusDataDTO.parseFrom(message);
      // 发送心跳
      BusDataDto.RequestHeader requestHeader = busDataDTO.getRequestHeader();
      BusDataDto.ResponseHeader.Builder responseHeader = BusDataDto.ResponseHeader.newBuilder();
      responseHeader.setMessageType(requestHeader.getMessageType());
      responseHeader.setSequenceNum(requestHeader.getSequenceNum());
      responseHeader.setVehicleId(requestHeader.getVehicleId());
      responseHeader.setClientTimestamp(requestHeader.getTimestamp());
      responseHeader.setServerTimestamp(System.currentTimeMillis() * 1000000L);
      BusDataDto.BusDataResponseDTO.Builder builder = BusDataDto.BusDataResponseDTO.newBuilder();
      builder.setResponseHeader(responseHeader);
      ByteBuffer response = ByteBuffer.wrap(builder.build().toByteArray());
      synchronized (session) {
        session.getBasicRemote().sendBinary(response);
      }
    } catch (IOException e) {
      throw new RuntimeException("Failed to read all bytes.", e);
    }
  }

  /**
   * 发生异常
   */
  @OnError
  public void onError(Session session, Throwable throwable) {
    log.error("异常连接: {}, 连接ID：{}.", this.clientName, session.getId(), throwable);
  }

  /**
   * 连接关闭
   */
  @OnClose
  public void onClose(Session session, CloseReason closeReason) {
    onlineCount.decrementAndGet();
    log.info("关闭连接: {}, 连接ID：{}, 当前在线数: {}, 关闭原因: {}.", this.clientName, session.getId(), onlineCount.get(), closeReason.getCloseCode().getCode());
  }

  /**
   * 不打印RoutingPoint
   */
  private void printLog(Session session, String busDataDTOStr) {
    try {
      String str = busDataDTOStr.replaceAll(",\\s*\"routingPoint[\\s\\S]*?]", "");
      log.info("接收消息: {}, 连接ID：{}, 内容: {}.", this.clientName, session.getId(), str);
    } catch (Exception e) {
      log.error("BusData printLog error {}.", this.clientName, e);
    }
  }
}