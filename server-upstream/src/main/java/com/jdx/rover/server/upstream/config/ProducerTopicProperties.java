/*
 * Copyright (c) 2024 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.server.upstream.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 生产主题配置类
 * 读取application.yml中的jmq.topic.producer属性文件
 *
 * <AUTHOR>
 * @date 2024/12/16
 */
@Component
@ConfigurationProperties(prefix = "jmq.topic.producer")
@Data
public class ProducerTopicProperties {
    /**
     * bus源数据
     */
    private String serverBusOrigin;
    /**
     * Guardian源数据
     */
    private String serverUpstreamGuardianOrigin;
    /**
     * report源数据
     */
    private String serverReportOrigin;
    /**
     * LocalView源数据
     */
    private String serverLocalView;
    /**
     * Guardian连接信息
     */
    private String serverGuardianConnect;
}