/*
 * Copyright (c) 2021 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.server.upstream.service;

import com.jdx.rover.common.utils.exception.AppException;
import com.jdx.rover.server.api.domain.constants.KafkaTopicConstant;
import com.jdx.rover.server.domain.entity.Client;
import com.jdx.rover.server.repository.jmq.JmqProducerManager;
import com.jdx.rover.server.upstream.config.ProducerTopicProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.serialization.ByteArraySerializer;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.core.ProducerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

/**
 * 3Dmap的服务类
 *
 * <AUTHOR>
 * @date  2022-01-01
 */
@Service
@Slf4j
public class ThreeDimensionalMapService {

  @Value("${spring.kafka.bootstrap-servers}")
  private String bootstrapServers;

  /**
   * jmq消息发送producer
   */
  @Autowired
  private JmqProducerManager jmqProducerManager;

  /**
   * 生产主题配置类
   */
  @Autowired
  private ProducerTopicProperties producerTopicProperties;

  /**
   * 发送byte[]数组kafka模板
   */
  private KafkaTemplate<String, byte[]> kafkaByteTemplate;

  @PostConstruct
  public void init() {
    kafkaByteTemplate = createByteTemplate();
  }

  /**
   * 发送3D map 二进制数据流到kafka
   *
   * @param client
   * @param message
   * @throws AppException
   */
  public void streamingClientRequest(Client client, byte[] message) throws AppException {
    // 发送jmq消息
    jmqProducerManager.sendByteOrdered(producerTopicProperties.getServerLocalView(), message, client.getName());

    String topic = KafkaTopicConstant.SERVER_LOCAL_VIEW + client.getName();
    kafkaByteTemplate.send(topic, client.getName(), message);
  }

  /**
   * 创建发送byte[]数组kafka模板
   */
  private KafkaTemplate<String, byte[]> createByteTemplate() {
    Map<String, Object> props = new HashMap<>();
    props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
    props.put(ProducerConfig.RETRIES_CONFIG, 0);
    props.put(ProducerConfig.BATCH_SIZE_CONFIG, 16384);
    props.put(ProducerConfig.LINGER_MS_CONFIG, 1);
    props.put(ProducerConfig.BUFFER_MEMORY_CONFIG, 33554432);
    props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
    props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, ByteArraySerializer.class);
    ProducerFactory<String, byte[]> pf = new DefaultKafkaProducerFactory(props);
    KafkaTemplate<String, byte[]> template = new KafkaTemplate<>(pf);
    return template;
  }
}
