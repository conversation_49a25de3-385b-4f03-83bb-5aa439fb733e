/*
 * Copyright (c) 2023 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.server.upstream.web.websocket;

import com.jdx.rover.server.common.utils.host.HostUtils;
import com.jdx.rover.server.common.utils.proto.ProtoUtils;
import com.jdx.rover.server.domain.entity.Client;
import com.jdx.rover.server.domain.enums.ClientTypeEnum;
import com.jdx.rover.server.repository.jmq.JmqProducerManager;
import com.jdx.rover.server.repository.shadow.ShadowEventEnum;
import com.jdx.rover.server.repository.shadow.ShadowEventUtils;
import com.jdx.rover.server.upstream.config.ProducerTopicProperties;
import com.jdx.rover.server.upstream.config.ServletAwareConfigurator;
import com.jdx.rover.server.upstream.domain.enums.ServerUpstreamTopicEnum;
import com.jdx.rover.server.upstream.service.kafka.KafkaService;
import com.jdx.rover.server.upstream.utils.WebsocketUtils;
import com.jdx.rover.shadow.boot.starter.util.EventUtil;
import com.jdx.rover.shadow.boot.starter.vo.EventRecordVO;
import jdx.rover.report.dto.proto.ReportDto;
import jdx.rover.report.dto.proto.ReportHeader;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import jakarta.websocket.CloseReason;
import jakarta.websocket.OnClose;
import jakarta.websocket.OnError;
import jakarta.websocket.OnMessage;
import jakarta.websocket.OnOpen;
import jakarta.websocket.Session;
import jakarta.websocket.server.PathParam;
import jakarta.websocket.server.ServerEndpoint;
import java.io.IOException;
import java.io.InputStream;
import java.nio.ByteBuffer;
import java.util.Date;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 车辆上报数据WebSocket连接信息
 *
 * <AUTHOR>
 * @date  2022-01-01
 */
@Slf4j
@ServerEndpoint(value = "/upstream/ws/report/{clientName}", configurator = ServletAwareConfigurator.class)
@Component
public class ReportWebSocket {
    /**
     * 记录当前在线连接数
     */
    private static final AtomicInteger onlineCount = new AtomicInteger(0);

    /**
     * 连接客户端
     */
    private Client client = new Client();

    private static KafkaService kafkaService;

    /**
     * WebSocket是线程安全的,有用户连接时才会创建一个新的端点实例,所以spring依赖注入会失败,改成静态变量就好了
     */
    private static KafkaTemplate<String, byte[]> secondKafkaTemplate;

    /**
     * jmq消息发送producer
     */
    private static JmqProducerManager jmqProducerManager;
    /**
     * 生产主题配置类
     */
    private static ProducerTopicProperties producerTopicProperties;

    @Autowired
    public void setKafkaService(KafkaService kafkaService) {
        ReportWebSocket.kafkaService = kafkaService;
    }

    @Autowired
    public void setSecondKafkaTemplate(KafkaTemplate<String, byte[]> secondKafkaTemplate) {
        ReportWebSocket.secondKafkaTemplate = secondKafkaTemplate;
    }

    @Autowired
    public void setJmqProducerManager(JmqProducerManager jmqProducerManager) {
        ReportWebSocket.jmqProducerManager = jmqProducerManager;
    }

    @Autowired
    public void setProducerTopicProperties(ProducerTopicProperties producerTopicProperties) {
        ReportWebSocket.producerTopicProperties = producerTopicProperties;
    }

    /**
     * 连接建立
     */
    @OnOpen
    public void onOpen(Session session, @PathParam("clientName") String clientName) {
        onlineCount.incrementAndGet();
        this.client.setName(clientName);
        this.client.setSessionId(session.getId());
        this.client.setHostName(HostUtils.getHostName());
        this.client.setType(ClientTypeEnum.REPORT.getType());
        this.client.setConnectTime(new Date());
        String clientIp = WebsocketUtils.getWebSocketIp(session);
        log.info("新增report连接:{}, 连接ID:{}, 当前在线数为:{}, 连接IP:{}", this.client.getName(), session.getId(), onlineCount.get(), clientIp);

        EventRecordVO eventRecordVO = ShadowEventUtils.buildConnectEvent(this.client.getName()
                , ShadowEventEnum.SERVER_VEHICLE_REPORT_WS_STATE, "成功");
        EventUtil.sendEvent(eventRecordVO);
    }

    /**
     * 传输消息
     */
    @OnMessage
    public void onMessage(InputStream message, Session session) {
        try {
            ReportDto.ReportDTO reportDataDTO = ReportDto.ReportDTO.parseFrom(message);
            ReportHeader.RequestHeader reportHeader = reportDataDTO.getRequestHeader();
            List<ReportHeader.MessageTypeRequest> requestList = reportHeader.getMessageTypeRequestList();
            if (CollectionUtils.isEmpty(requestList)) {
                log.error("report请求头不能为空!{}", this.client.getName());
                return;
            }
            ReportHeader.ResponseHeader.Builder responseHeader = ReportHeader.ResponseHeader.newBuilder();
            for (ReportHeader.MessageTypeRequest messageTypeRequest : requestList) {
                if (!messageTypeRequest.getNeedResponse()) {
                    if (messageTypeRequest.getMessageType().equals(ReportHeader.MessageType.HEARTBEAT)) {
                        // 处理心跳
                        log.error("report心跳请求必须响应!{}", this.client.getName());
                    }
                    continue;
                }
                // 心跳的头,可以直接删除
                ReportHeader.MessageTypeResponse.Builder responseBuilder = ReportHeader.MessageTypeResponse.newBuilder();
                responseBuilder.setMessageType(messageTypeRequest.getMessageType());
                responseBuilder.setMessageState(ReportHeader.MessageState.SUCCESS);
                responseHeader.addMessageTypeResponse(responseBuilder);
            }

            // 发送kafka
            byte[] data = reportDataDTO.toByteArray();

            // 发送jmq消息
            jmqProducerManager.sendByteOrdered(producerTopicProperties.getServerReportOrigin(), data, reportDataDTO.getRequestHeader().getVehicleName());

            secondKafkaTemplate.send(ServerUpstreamTopicEnum.SERVER_REPORT_ORIGIN.getValue()
                    , reportDataDTO.getRequestHeader().getVehicleName(), data);
            // 发送响应
            if (!CollectionUtils.isEmpty(responseHeader.getMessageTypeResponseList())) {
                responseHeader.setRequestId(reportDataDTO.getRequestHeader().getRequestId());
                responseHeader.setRequestTime(reportDataDTO.getRequestHeader().getRequestTime());
                responseHeader.setResponseTime(System.currentTimeMillis());
                responseHeader.setVehicleName(reportDataDTO.getRequestHeader().getVehicleName());

                ReportDto.ReportDTO.Builder responseBuilder = ReportDto.ReportDTO.newBuilder();
                responseBuilder.setResponseHeader(responseHeader);
                byte[] responseArray = responseBuilder.build().toByteArray();
                ByteBuffer response = ByteBuffer.wrap(responseArray);
                synchronized (session) {
                    session.getBasicRemote().sendBinary(response);
                }
                for (ReportHeader.MessageTypeRequest messageTypeRequest : requestList) {
                    // 打印非心跳回复
                    if (!ReportHeader.MessageType.HEARTBEAT.equals(messageTypeRequest.getMessageType())) {
                        log.info("report响应={}", ProtoUtils.protoToJson(responseBuilder));
                        break;
                    }
                }
            }
            // 打印日志放到最后,可以删除
            printfLog(reportDataDTO);
        } catch (IOException e) {
            log.error("读写report信息失败!this.client.getName()={}", this.client.getName(), e);
            throw new RuntimeException("Failed to read all bytes.", e);
        } catch (Exception e) {
            log.error("解析report信息失败!this.client.getName()={}", this.client.getName(), e);
            throw new RuntimeException("Failed to read all bytes.", e);
        }
    }

    /**
     * 发生异常
     */
    @OnError
    public void onError(Session session, Throwable throwable) {
        log.error("异常report连接:{}, 连接ID:{}, 当前在线数为:{}", this.client.getName(), session.getId(), onlineCount.get(), throwable);
    }

    /**
     * 连接关闭
     */
    @OnClose
    public void onClose(Session session, CloseReason closeReason) {
        onlineCount.decrementAndGet();
        String clientIp = WebsocketUtils.getWebSocketIp(session);
        log.error("关闭report连接:{}, 连接ID:{}, 连接IP:{}, 当前在线数为:{}, 关闭原因:{}", this.client.getName(), session.getId()
                , clientIp, onlineCount.get(), closeReason.getCloseCode().getCode());

        EventRecordVO eventRecordVO = ShadowEventUtils.buildConnectEvent(this.client.getName()
                , ShadowEventEnum.SERVER_VEHICLE_REPORT_WS_STATE, "断开");
        EventUtil.sendEvent(eventRecordVO);
    }

    /**
     * 打印日志
     *
     * @param dto
     */
    private void printfLog(ReportDto.ReportDTO dto) {
        String str = ProtoUtils.protoToJson(dto);
        log.info("{}收到report={}", dto.getRequestHeader().getVehicleName(), str);
    }
}