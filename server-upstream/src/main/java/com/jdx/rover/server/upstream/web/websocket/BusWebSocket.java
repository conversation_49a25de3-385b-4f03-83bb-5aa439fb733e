package com.jdx.rover.server.upstream.web.websocket;

import com.jdx.rover.server.common.utils.proto.ProtoUtils;
import com.jdx.rover.server.repository.jmq.JmqProducerManager;
import com.jdx.rover.server.repository.shadow.ShadowEventEnum;
import com.jdx.rover.server.repository.shadow.ShadowEventUtils;
import com.jdx.rover.server.upstream.config.ProducerTopicProperties;
import com.jdx.rover.server.upstream.config.ServletAwareConfigurator;
import com.jdx.rover.server.upstream.domain.enums.ServerUpstreamTopicEnum;
import com.jdx.rover.server.upstream.service.kafka.KafkaService;
import com.jdx.rover.server.upstream.utils.WebsocketUtils;
import com.jdx.rover.shadow.boot.starter.util.EventUtil;
import com.jdx.rover.shadow.boot.starter.vo.EventRecordVO;
import jdx.rover.dto.proto.BusDto;
import jdx.rover.dto.proto.BusHeader;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import jakarta.websocket.CloseReason;
import jakarta.websocket.OnClose;
import jakarta.websocket.OnError;
import jakarta.websocket.OnMessage;
import jakarta.websocket.OnOpen;
import jakarta.websocket.Session;
import jakarta.websocket.server.PathParam;
import jakarta.websocket.server.ServerEndpoint;
import java.io.IOException;
import java.nio.ByteBuffer;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 车辆上报总线数据WebSocket连接信息
 *
 * <AUTHOR>
 * @date  2022-01-01
 */
@Slf4j
@ServerEndpoint(value = "/upstream/ws/bus2/{clientName}", configurator = ServletAwareConfigurator.class)
@Component
public class BusWebSocket {
  /**
   * 记录当前在线连接数
   */
  private static final AtomicInteger onlineCount = new AtomicInteger(0);

  /**
   * 连接客户端名称,此处为车辆名称
   */
  private String clientName;

  private static KafkaService kafkaService;
  /**
   * jmq消息发送producer
   */
  private static JmqProducerManager jmqProducerManager;
  /**
   * 生产主题配置类
   */
  private static ProducerTopicProperties producerTopicProperties;

  @Autowired
  public void setKafkaService(KafkaService kafkaService) {
    BusWebSocket.kafkaService = kafkaService;
  }

  @Autowired
  public void setJmqProducerManager(JmqProducerManager jmqProducerManager) {
    BusWebSocket.jmqProducerManager = jmqProducerManager;
  }

  @Autowired
  public void setProducerTopicProperties(ProducerTopicProperties producerTopicProperties) {
    BusWebSocket.producerTopicProperties = producerTopicProperties;
  }

  /**
   * 连接建立
   */
  @OnOpen
  public void onOpen(Session session, @PathParam("clientName") String clientName) {
    onlineCount.incrementAndGet();
    this.clientName = clientName;
    String clientIp = WebsocketUtils.getWebSocketIp(session);
    log.info("新增bus连接:{},连接ID:{},连接IP:{},当前在线数:{}"
        , this.clientName, session.getId(), clientIp, onlineCount.get());

    EventRecordVO eventRecordVO = ShadowEventUtils.buildConnectEvent(this.clientName
            , ShadowEventEnum.SERVER_VEHICLE_BUS_WS_STATE, "成功");
    EventUtil.sendEvent(eventRecordVO);
  }

  /**
   * 传输消息
   */
  @OnMessage
  public void onMessage(byte[] data, Session session) {
    try {
      BusDto.BusDTO busDataDTO = BusDto.BusDTO.parseFrom(data);
      BusHeader.RequestHeader busHeader = busDataDTO.getRequestHeader();
      List<BusHeader.MessageTypeRequest> requestList = busHeader.getMessageTypeRequestList();
      if (CollectionUtils.isEmpty(requestList)) {
        log.error("bus请求头不能为空!{}", clientName);
        return;
      }
      BusHeader.ResponseHeader.Builder responseHeader = BusHeader.ResponseHeader.newBuilder();
      for (BusHeader.MessageTypeRequest messageTypeRequest : requestList) {
        if (!messageTypeRequest.getNeedResponse()) {
          if (messageTypeRequest.getMessageType().equals(BusHeader.MessageType.HEARTBEAT)) {
            // 处理心跳
            log.error("bus心跳请求必须响应!{}", clientName);
          }
          continue;
        }
        // 心跳的头,可以直接删除
        BusHeader.MessageTypeResponse.Builder responseBuilder = BusHeader.MessageTypeResponse.newBuilder();
        responseBuilder.setMessageType(messageTypeRequest.getMessageType());
        responseBuilder.setMessageState(BusHeader.MessageState.SUCCESS);
        responseHeader.addMessageTypeResponse(responseBuilder);
      }

      // 发送jmq消息
      jmqProducerManager.sendByteOrdered(producerTopicProperties.getServerBusOrigin(), data, busDataDTO.getRequestHeader().getVehicleName());
      // 发送kafka
      kafkaService.getByteTemplate().send(ServerUpstreamTopicEnum.SERVER_BUS_ORIGIN.getValue()
              , busDataDTO.getRequestHeader().getVehicleName(), data);
      // 发送响应
      if (!CollectionUtils.isEmpty(responseHeader.getMessageTypeResponseList())) {
        responseHeader.setRequestId(busDataDTO.getRequestHeader().getRequestId());
        responseHeader.setRequestTime(busDataDTO.getRequestHeader().getRequestTime());
        responseHeader.setResponseTime(System.currentTimeMillis());
        responseHeader.setVehicleName(busDataDTO.getRequestHeader().getVehicleName());

        BusDto.BusDTO.Builder responseBuilder = BusDto.BusDTO.newBuilder();
        responseBuilder.setResponseHeader(responseHeader);
        byte[] responseArray = responseBuilder.build().toByteArray();
        ByteBuffer response = ByteBuffer.wrap(responseArray);
        synchronized (session) {
          session.getBasicRemote().sendBinary(response);
        }
        for (BusHeader.MessageTypeRequest messageTypeRequest : requestList) {
          // 打印非心跳回复
          if (!BusHeader.MessageType.HEARTBEAT.equals(messageTypeRequest.getMessageType())) {
            log.info("bus2响应数据={}", ProtoUtils.protoToJson(responseBuilder));
            break;
          }
        }
      }
    } catch (IOException e) {
      log.error("读写bus信息失败!clientName={}", clientName, e);
      throw new RuntimeException("Failed to read all bytes.", e);
    } catch (Exception e) {
      log.error("解析bus信息失败!clientName={}", clientName, e);
      throw new RuntimeException("Failed to read all bytes.", e);
    }
  }

  /**
   * 发生异常
   */
  @OnError
  public void onError(Session session, Throwable throwable) {
    log.error("异常bus连接:{}, 连接ID:{}", this.clientName, session.getId(), throwable);
  }

  /**
   * 连接关闭
   */
  @OnClose
  public void onClose(Session session, CloseReason closeReason) {
    onlineCount.decrementAndGet();
    String clientIp = WebsocketUtils.getWebSocketIp(session);
    log.info("关闭bus连接:{},连接ID:{},连接IP:{},当前在线数:{},关闭原因:{}"
        , this.clientName, session.getId(), clientIp, onlineCount.get(), closeReason.getCloseCode().getCode());

    EventRecordVO eventRecordVO = ShadowEventUtils.buildConnectEvent(this.clientName
            , ShadowEventEnum.SERVER_VEHICLE_BUS_WS_STATE, "断开");
    EventUtil.sendEvent(eventRecordVO);
  }
}