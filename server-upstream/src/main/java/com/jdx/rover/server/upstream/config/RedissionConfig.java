/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.server.upstream.config;

import java.util.ArrayList;
import java.util.List;
import org.redisson.codec.JsonJacksonCodec;
import org.redisson.spring.starter.RedissonAutoConfigurationCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 配置 redisson 的序列化方式
 *
 * <AUTHOR>
 */
@Configuration
public class RedissionConfig {
  /**
   * <p>
   * 配置 redisson 的序列化方式
   * </p>
   */
  @Bean
  public List<RedissonAutoConfigurationCustomizer> redissonAutoConfigurationCustomizers() {
    List<RedissonAutoConfigurationCustomizer> RedissonAutoConfigurationCustomizer = new ArrayList<>();
    RedissonAutoConfigurationCustomizer.add(configuration -> configuration.setCodec(new JsonJacksonCodec()));
    return RedissonAutoConfigurationCustomizer;
  }
}
