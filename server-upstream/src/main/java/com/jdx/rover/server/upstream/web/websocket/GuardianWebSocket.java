/*
 * Copyright (c) 2021 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.server.upstream.web.websocket;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.jdx.rover.common.constant.NumberConstant;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.server.api.domain.dto.guardian.GuardianConnectDTO;
import com.jdx.rover.server.api.domain.enums.guardian.GuardianConnectStatusEnum;
import com.jdx.rover.server.common.utils.host.HostUtils;
import com.jdx.rover.server.common.utils.proto.ProtoUtils;
import com.jdx.rover.server.domain.entity.Client;
import com.jdx.rover.server.domain.enums.ClientTypeEnum;
import com.jdx.rover.server.domain.enums.RedisCacheEnum;
import com.jdx.rover.server.repository.jmq.JmqProducerManager;
import com.jdx.rover.server.repository.redis.RedissonUtils;
import com.jdx.rover.server.repository.shadow.ShadowEventEnum;
import com.jdx.rover.server.repository.shadow.ShadowEventUtils;
import com.jdx.rover.server.upstream.config.ProducerTopicProperties;
import com.jdx.rover.server.upstream.config.ServletAwareConfigurator;
import com.jdx.rover.server.upstream.domain.enums.ServerUpstreamTopicEnum;
import com.jdx.rover.server.upstream.service.kafka.KafkaService;
import com.jdx.rover.shadow.boot.starter.util.EventUtil;
import com.jdx.rover.shadow.boot.starter.vo.EventRecordVO;
import jdx.rover.guardian.data.dto.GuardianDataDto;
import jdx.rover.guardian.data.dto.GuardianDataDto.GuardianInfoDTO;
import jdx.rover.guardian.data.dto.GuardianDataDto.GuardianInfoResponseDTO;
import jdx.rover.guardian.data.dto.GuardianDataDto.HeartbeatHeader;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.concurrent.BasicThreadFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import jakarta.websocket.CloseReason;
import jakarta.websocket.OnClose;
import jakarta.websocket.OnError;
import jakarta.websocket.OnMessage;
import jakarta.websocket.OnOpen;
import jakarta.websocket.Session;
import jakarta.websocket.server.ServerEndpoint;
import java.io.IOException;
import java.io.InputStream;
import java.nio.ByteBuffer;
import java.util.Date;
import java.util.concurrent.Callable;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * guardian WebSocket连接信息
 *
 * <AUTHOR>
 * @date  2022-01-01
 */
@Slf4j
@ServerEndpoint(value = "/upstream/ws/guardian", configurator = ServletAwareConfigurator.class)
@Component
public class GuardianWebSocket {
  /**
   * 最大异常间隔时间,超过了表示新的异常
   */
  public static final int MAX_MESSAGE_INTERVAL = 10;
  private static ScheduledExecutorService scheduledThreadPool = new ScheduledThreadPoolExecutor(10,
      new BasicThreadFactory.Builder().namingPattern("guardian-schedule-pool-%d").daemon(true).build());

  /**
   * 记录当前在线连接数
   */
  private static AtomicInteger onlineCount = new AtomicInteger(0);

  /**
   * 连接客户端
   */
  private Client client = new Client();

  /**
   * WebSocket是线程安全的,有用户连接时才会创建一个新的端点实例,所以spring依赖注入会失败,改成静态变量就好了
   */
  private static KafkaService kafkaService;
  /**
   * jmq消息发送producer
   */
  private static JmqProducerManager jmqProducerManager;
  /**
   * 生产主题配置类
   */
  private static ProducerTopicProperties producerTopicProperties;

  @Autowired
  public void setKafkaService(KafkaService kafkaService) {
    GuardianWebSocket.kafkaService = kafkaService;
  }

  @Autowired
  public void setJmqProducerManager(JmqProducerManager jmqProducerManager) {
    GuardianWebSocket.jmqProducerManager = jmqProducerManager;
  }

  @Autowired
  public void setProducerTopicProperties(ProducerTopicProperties producerTopicProperties) {
    GuardianWebSocket.producerTopicProperties = producerTopicProperties;
  }

  /**
   * 连接建立成功调用的方法
   */
  @OnOpen
  public void onOpen(Session session) {
    log.info("有新guardian连接加入：{}，当前在线数为：{}", session.getId(), onlineCount.get());
    // 在线数加1
    onlineCount.incrementAndGet();
    if (session.getUserProperties() != null && session.getUserProperties().get("vehicleName") != null) {
      this.client.setName(session.getUserProperties().get("vehicleName").toString());
      this.client.setSessionId(session.getId());
      this.client.setHostName(HostUtils.getHostName());
      this.client.setType(ClientTypeEnum.GUARDIAN_ROVER.getType());
      this.client.setConnectTime(new Date());
      try {
        RedissonUtils.setMapObject(RedisCacheEnum.GUARDIAN_CONNECT_HASH.getValue(), this.client.getName(), this.client);
        sendDelayGuardianConnectStatusMsg(this.client);
      } catch (Exception e) {
        log.error("处理初始化信息失败!{}", this.client, e);
      }
      log.info("车辆guardian连接: {}, 连接ID：{}，当前在线数为：{}", this.client.getName(), session.getId(), onlineCount.get());
    }

    EventRecordVO eventRecordVO = ShadowEventUtils.buildConnectEvent(this.client.getName()
            , ShadowEventEnum.SERVER_VEHICLE_GUARDIAN_WS_STATE, "成功");
    EventUtil.sendEvent(eventRecordVO);
  }


  /**
   * 连接关闭调用的方法
   */
  @OnClose
  public void onClose(Session session, CloseReason closeReason) {
    // 在线数减1
    onlineCount.decrementAndGet();
    this.client.setServerTime(new Date());
    log.info("有一guardian连接关闭：{}，当前在线数为：{}", session.getId(), onlineCount.get());
    if (StringUtils.isNotBlank(this.client.getName())) {
      Client clientRedis = RedissonUtils.getMapObject(RedisCacheEnum.GUARDIAN_CONNECT_HASH.getValue(), this.client.getName());
      if (StringUtils.equals(clientRedis.getHostName(), this.client.getHostName()) && StringUtils.equals(clientRedis.getSessionId(), this.client.getSessionId())) {
        sendDelayGuardianDisconnectStatusMsg(this.client);
      } else {
        log.info("本地guardian=[{}]不等于redis中guardian=[{}],忽略关闭消息", this.client, clientRedis);
      }
      log.info("车辆guardian关闭: {}, 连接ID：{}, 当前在线数: {}, 关闭原因: {}.", this.client, session.getId(), onlineCount.get(), closeReason.getCloseCode().getCode());
    }
    EventRecordVO eventRecordVO = ShadowEventUtils.buildConnectEvent(this.client.getName()
            , ShadowEventEnum.SERVER_VEHICLE_GUARDIAN_WS_STATE, "断开");
    EventUtil.sendEvent(eventRecordVO);
  }

  /**
   * 收到客户端消息后调用的方法
   *
   * @param message 客户端发送过来的消息
   */
  @OnMessage
  public void onMessage(InputStream message, Session session) {
    try {
      this.client.setServerTime(new Date());
      GuardianInfoDTO dto = GuardianInfoDTO.parseFrom(message);
      // 转发放到最前面,保证数据及时下发到下游系统
      jmqProducerManager.sendByteOrdered(producerTopicProperties.getServerUpstreamGuardianOrigin(), dto.toByteArray(), dto.getVehicleId());

      kafkaService.getByteTemplate().send(ServerUpstreamTopicEnum.SERVER_UPSTREAM_GUARDIAN_ORIGIN.getValue(), dto.getVehicleId(), dto.toByteArray());

      HeartbeatHeader.Builder heartbeatBuilder = HeartbeatHeader.newBuilder();
      heartbeatBuilder.setVehicleId(dto.getVehicleId());
      heartbeatBuilder.setSequenceNum(dto.getSequenceNum());
      heartbeatBuilder.setClientTimestamp(dto.getTimestamp());
      heartbeatBuilder.setServerTimestamp(System.currentTimeMillis() * NumberConstant.MILLION);
      GuardianInfoResponseDTO.Builder builder = GuardianInfoResponseDTO.newBuilder();
      builder.setHeartbeatHeader(heartbeatBuilder);

      ByteBuffer response = ByteBuffer.wrap(builder.build().toByteArray());

      synchronized (session) {
        session.getBasicRemote().sendBinary(response);
      }

      this.client.setClientTime(new Date(dto.getTimestamp() / NumberConstant.MILLION));
      long time = DateUtil.between(this.client.getServerTime(), this.client.getClientTime(), DateUnit.SECOND);
      if (time > MAX_MESSAGE_INTERVAL) {
        log.info("当前车辆{}延迟{}秒!", this.client.getName(), time);
      }
      // 打印日志放到最后,可以删除
      printfLog(dto);
    } catch (IOException e) {
      log.error("guardian读取发生异常={}", this.client, e);
      throw new RuntimeException("Failed to read all bytes.", e);
    } catch (Exception e) {
      log.error("guardian处理发生异常={}", this.client, e);
      throw new RuntimeException("Other guardian error.", e);
    }
  }

  /**
   * 不打印RoutingPoint
   *
   * @param dto
   */
  private void printfLog(GuardianInfoDTO dto) {
    GuardianDataDto.GuardianInfoDTO.Builder dtoBuilder = dto.toBuilder();
    dtoBuilder.clearServiceInfo();
    for (GuardianDataDto.StopResult.Builder builder : dtoBuilder.getRoutingStatusBuilder().getStopResultBuilderList()) {
      builder.clearRoutingPoint();
    }
    String str = ProtoUtils.protoToJson(dtoBuilder);
    log.info("{} 收到guardian={}", dto.getVehicleId(), str);
  }

  @OnError
  public void onError(Session session, Throwable error) {
    log.error("发生错误", error);
  }

  /**
   * 延迟10s发送guardian断开的kafka消息
   *
   * @param client 客户端
   */
  private void sendDelayGuardianConnectStatusMsg(Client client) {
    String vehicleName = client.getName();
    scheduledThreadPool.schedule((Callable<Void>) () -> {
      try {
        sendGuardianConnectStatusMsg(vehicleName, GuardianConnectStatusEnum.CONNECT.getValue());
        log.info("延迟3s发送在线消息:{}", vehicleName);
      } catch (Exception e) {
        log.error("延迟3s发送在线消息失败!vehicleName={}", vehicleName, e);
      }
      return null;
    }, 3, TimeUnit.SECONDS);
  }

  /**
   * 延迟10s发送guardian断开的kafka消息
   *
   * @param client 客户端
   */
  private void sendDelayGuardianDisconnectStatusMsg(Client client) {
    String vehicleName = client.getName();
    String hostName = client.getHostName();
    String sessionId = client.getSessionId();
    scheduledThreadPool.schedule((Callable<Void>) () -> {
      try {
        Client clientRedis = RedissonUtils.getMapObject(RedisCacheEnum.GUARDIAN_CONNECT_HASH.getValue(), vehicleName);
        if (StringUtils.equals(clientRedis.getHostName(), hostName) && StringUtils.equals(clientRedis.getSessionId(), sessionId)) {
          sendGuardianConnectStatusMsg(vehicleName, GuardianConnectStatusEnum.DISCONNECT.getValue());
          log.info("延迟10s发送离线消息:{}", vehicleName);
        } else {
          log.info("本地guardian=[{},{},{}]不等于redis中guardian=[{}],忽略延迟关闭消息", vehicleName, hostName, sessionId, clientRedis);
        }
      } catch (Exception e) {
        log.error("延迟10s发送离线消息失败!vehicleName={}", vehicleName, e);
      }
      return null;
    }, 10, TimeUnit.SECONDS);
  }

  /**
   * 发送guardian连接的kafka消息
   *
   * @param vehicleName           车辆名称
   * @param guardianConnectStatus guardian连接状态
   */
  private void sendGuardianConnectStatusMsg(String vehicleName, String guardianConnectStatus) {
    GuardianConnectDTO guardianConnectDTO = new GuardianConnectDTO();
    guardianConnectDTO.setVehicleName(vehicleName);
    guardianConnectDTO.setGuardianConnectStatus(guardianConnectStatus);
    guardianConnectDTO.setRecordTime(new Date());
    String message = JsonUtils.writeValueAsString(guardianConnectDTO);
    jmqProducerManager.sendOrderedAndLog(producerTopicProperties.getServerGuardianConnect(), message, vehicleName);
  }
}
