/*
 * Copyright (c) 2021 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.server.upstream.web.websocket;

import java.util.concurrent.atomic.AtomicInteger;
import jakarta.websocket.OnClose;
import jakarta.websocket.OnError;
import jakarta.websocket.OnMessage;
import jakarta.websocket.OnOpen;
import jakarta.websocket.Session;
import jakarta.websocket.server.ServerEndpoint;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * guardian WebSocket连接信息
 */
@Slf4j
@ServerEndpoint(value = "/upstream/ws/test")
@Component
public class TestWebSocket {
  /**
   * 记录当前在线连接数
   */
  private static AtomicInteger onlineCount = new AtomicInteger(0);

  /**
   * 连接建立成功调用的方法
   */
  @OnOpen
  public void onOpen(Session session) {
    // 在线数加1
    onlineCount.incrementAndGet();
    log.info("有新连接加入：{}，当前在线数为：{}", session.getId(), onlineCount.get());
  }

  /**
   * 连接关闭调用的方法
   */
  @OnClose
  public void onClose(Session session) {
    // 在线数减1
    onlineCount.decrementAndGet();
    log.info("有一连接关闭：{}，当前在线数为：{}", session.getId(), onlineCount.get());
  }

  /**
   * 收到客户端消息后调用的方法
   *
   * @param message 客户端发送过来的消息
   */
  @OnMessage
  public void onMessage(String message, Session session) {
    log.info("服务端收到客户端[{}]的消息:{}", session.getId(), message);
    this.sendMessage("Hello, " + message, session);
  }

  @OnError
  public void onError(Session session, Throwable error) {
    log.error("发生错误", error);
  }

  /**
   * 服务端发送消息给客户端
   */
  private void sendMessage(String message, Session toSession) {
    try {
      log.info("服务端给客户端[{}]发送消息{}", toSession.getId(), message);
      toSession.getAsyncRemote().sendText(message);
    } catch (Exception e) {
      log.error("服务端发送消息给客户端失败：{}", e);
    }
  }
}
