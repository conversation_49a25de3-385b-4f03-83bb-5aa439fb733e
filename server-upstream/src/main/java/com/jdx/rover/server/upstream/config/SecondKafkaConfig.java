package com.jdx.rover.server.upstream.config;

import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.serialization.ByteArraySerializer;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.kafka.KafkaProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.core.ProducerFactory;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * 第二个kafka配置, 只有生产者, 不配置消费者
 *
 * <AUTHOR>
 */
@Configuration
public class SecondKafkaConfig {
    @Autowired
    private KafkaProperties kafkaProperties;

    @Value("${project.kafka.second.bootstrap-servers}")
    private String bootstrapServers;

    @Bean
    public KafkaTemplate<String, byte[]> secondKafkaTemplate() {
        Map<String, Object> producerConfigs = producerConfigs(ByteArraySerializer.class);
        ProducerFactory<String, byte[]> producerFactory = new DefaultKafkaProducerFactory<>(producerConfigs);
        return new KafkaTemplate<>(producerFactory);
    }

    private Map<String, Object> producerConfigs(Class serializerClass) {
        Map<String, Object> props = new HashMap<>();
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, this.bootstrapServers);
        Integer producerRetries = Optional.ofNullable(this.kafkaProperties.getProducer().getRetries()).orElse(5);
        props.put(ProducerConfig.RETRIES_CONFIG, producerRetries);
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, serializerClass);
        return props;
    }
}
