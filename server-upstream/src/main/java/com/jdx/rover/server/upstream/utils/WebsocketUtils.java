package com.jdx.rover.server.upstream.utils;


import jakarta.websocket.Session;

/**
 * <AUTHOR>
 */
public class WebsocketUtils {
  /**
   * IP关键字
   */
  private static final String CLIENT_IP_KEY = "CLIENT_IP";

  /**
   * 获取webSocket ip
   *
   * @param session
   * @return
   */
  public static final String getWebSocketIp(Session session) {
    String clientIp = "N/A";
    if (session == null || session.getUserProperties() == null || session.getUserProperties().get(CLIENT_IP_KEY) == null) {
      return clientIp;
    }
    clientIp = String.valueOf(session.getUserProperties().get(CLIENT_IP_KEY));
    return clientIp;
  }
}
