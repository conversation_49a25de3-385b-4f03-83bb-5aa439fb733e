/*
 * Copyright (c) 2021-2021 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.server.upstream.web.websocket;

import com.jdx.rover.server.domain.entity.Client;
import com.jdx.rover.server.repository.shadow.ShadowEventEnum;
import com.jdx.rover.server.repository.shadow.ShadowEventUtils;
import com.jdx.rover.server.upstream.config.ServletAwareConfigurator;
import com.jdx.rover.server.upstream.service.ThreeDimensionalMapService;
import com.jdx.rover.shadow.boot.starter.util.EventUtil;
import com.jdx.rover.shadow.boot.starter.vo.EventRecordVO;
import jdx.rover.guardian.dto.LocalView;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import jakarta.websocket.CloseReason;
import jakarta.websocket.OnClose;
import jakarta.websocket.OnError;
import jakarta.websocket.OnMessage;
import jakarta.websocket.OnOpen;
import jakarta.websocket.Session;
import jakarta.websocket.server.ServerEndpoint;
import java.io.IOException;
import java.io.InputStream;
import java.nio.ByteBuffer;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * guardian WebSocket连接信息
 *
 * <AUTHOR>
 */
@Slf4j
@ServerEndpoint(value = "/upstream/ws/3dmap", configurator = ServletAwareConfigurator.class)
@Component
public class ThreeDimensionalMapWebSocket {
  /**
   * 记录当前在线连接数
   */
  private static AtomicInteger onlineCount = new AtomicInteger(0);

  /**
   * 连接客户端名称,此处为车辆名称
   */
  private String clientName;

  /**
   * WebSocket是线程安全的,有用户连接时才会创建一个新的端点实例,所以spring依赖注入会失败,改成静态变量就好了
   */
  private static ThreeDimensionalMapService threeDimensionalMapService;

  @Autowired
  public void setThreeDimensionalMapService(ThreeDimensionalMapService threeDimensionalMapService) {
    ThreeDimensionalMapWebSocket.threeDimensionalMapService = threeDimensionalMapService;
  }

  /**
   * 连接建立成功调用的方法
   */
  @OnOpen
  public void onOpen(Session session) {
    log.info("有新3dmap连接加入：{}，当前在线数为：{}", session.getId(), onlineCount.get());
    // 在线数加1
    onlineCount.incrementAndGet();
    if (session.getUserProperties() != null && session.getUserProperties().get("vehicleName") != null) {
      this.clientName = session.getUserProperties().get("vehicleName").toString();
      log.info("车辆3dmap连接: {}, 连接ID：{}，当前在线数为：{}", this.clientName, session.getId(), onlineCount.get());
    }

    EventRecordVO eventRecordVO = ShadowEventUtils.buildConnectEvent(this.clientName
            , ShadowEventEnum.SERVER_VEHICLE_LOCAL_VIEW_WS_STATE, "成功");
    EventUtil.sendEvent(eventRecordVO);
  }

  /**
   * 连接关闭调用的方法
   */
  @OnClose
  public void onClose(Session session, CloseReason closeReason) {
    // 在线数减1
    onlineCount.decrementAndGet();
    log.info("有一3dmap连接关闭：{}，当前在线数为：{}", session.getId(), onlineCount.get());
    if (StringUtils.isNotBlank(this.clientName)) {
      log.info("车辆3dmap关闭: {}, 连接ID：{}，当前在线数为：{}, 关闭原因: {}.", this.clientName, session.getId(), onlineCount.get(), closeReason.getCloseCode().getCode());
    }

    EventRecordVO eventRecordVO = ShadowEventUtils.buildConnectEvent(this.clientName
            , ShadowEventEnum.SERVER_VEHICLE_LOCAL_VIEW_WS_STATE, "断开");
    EventUtil.sendEvent(eventRecordVO);
  }

  /**
   * 收到客户端消息后调用的方法
   *
   * @param message 客户端发送过来的消息
   */
  @OnMessage
  public void onMessage(InputStream message, Session session) {
    try {
      LocalView.LocalViewDTO dto = LocalView.LocalViewDTO.parseFrom(message);
      LocalView.AuthorizeInfo authInfo = dto.getAuthInfo();
      LocalView.ProcessInfo.Builder processInfo = LocalView.ProcessInfo.newBuilder();
      processInfo.setProxyTimestamp(System.currentTimeMillis());
      LocalView.LocalViewDTO.Builder builder =
              dto.toBuilder().setProcessInfo(processInfo);

      if (StringUtils.isNotBlank(authInfo.getVehicleId())) {
        Client client = new Client();
        client.setName(authInfo.getVehicleId());
        client.setSessionId(session.getId());
        threeDimensionalMapService.streamingClientRequest(client, builder.build().toByteArray());
      } else {
        log.info("LocalView消息中缺少车辆名称.{},{}", this.clientName, session.getId());
      }

      LocalView.LocalViewHeartbeatHeader.Builder heartbeatBuilder = LocalView.LocalViewHeartbeatHeader.newBuilder();
      heartbeatBuilder.setVehicleId(authInfo.getVehicleId());
      heartbeatBuilder.setSequenceNum(0);
      long nanoTime = System.currentTimeMillis() * 1000000;
      heartbeatBuilder.setClientTimestamp(nanoTime);
      heartbeatBuilder.setServerTimestamp(nanoTime);
      LocalView.LocalViewResponseDTO.Builder dtoBuilder = LocalView.LocalViewResponseDTO.newBuilder();
      dtoBuilder.setHeartbeatHeader(heartbeatBuilder);

      ByteBuffer response = ByteBuffer.wrap(dtoBuilder.build().toByteArray());
      session.getBasicRemote().sendBinary(response);
      log.info("LocalView={},{}", authInfo.getVehicleId(), dto.getTimestamp());
    } catch (IOException e) {
      throw new RuntimeException("Failed to read all bytes.", e);
    }
  }

  @OnError
  public void onError(Session session, Throwable error) {
    if (StringUtils.indexOf(error.getMessage(), "authInfo#vehicleId") > -1) {
      log.info("LocalView缺少车辆名称{},{}", this.clientName, error.getMessage());
    } else {
      log.error("LocalView发生错误{}", this.clientName, error);
    }
  }
}
