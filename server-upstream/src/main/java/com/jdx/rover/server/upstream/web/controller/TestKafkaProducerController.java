/*
 * Copyright (c) 2021 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.server.upstream.web.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
public class TestKafkaProducerController {

  @Autowired
  private KafkaTemplate<String, String> kafkaTemplate;

  @GetMapping("/test/kafka/producer")
  public String producer() {
    log.info("开始测试发送kafka");
    String topic = "kafka_producer_test";
    String message = "{\"vehicleName\":\"JD40006\",\"drivingMode\":\"auto\",\"reportMileage\":2.2,\"reportTime\":1602691198000}";
    kafkaTemplate.send(topic, message);
    log.info("发送kafka完成");
    return "ok";
  }
}
