<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.5.4</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>

    <groupId>com.jdx.rover</groupId>
    <artifactId>rover-server</artifactId>
    <packaging>pom</packaging>
    <version>1.0.0-SNAPSHOT</version>
    <modules>
        <module>server-upstream</module>
        <module>server-stream</module>
        <module>server-business</module>
        <module>server-domain</module>
        <module>server-common</module>
        <module>server-repository</module>
        <module>server-api</module>
        <module>server-service</module>
        <module>server-mqtt</module>
    </modules>

    <properties>
        <jdk.version>17</jdk.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <maven.compiler.encoding>UTF-8</maven.compiler.encoding>
        <maven.compiler.compilerVersion>17</maven.compiler.compilerVersion>

        <rover-dependencies.version>1.1.26</rover-dependencies.version>
        <spring-cloud-dependencies.version>2025.0.0</spring-cloud-dependencies.version>
        <spring-cloud-alibaba-dependencies.version>2023.0.3.3</spring-cloud-alibaba-dependencies.version>
        <jakartaee-api.version>10.0.1</jakartaee-api.version>
        <commons-collections4.version>4.5.0</commons-collections4.version>
        <guava.version>33.4.8-jre</guava.version>
        <hutool-all.version>5.8.39</hutool-all.version>

        <protobuf-java.version>3.18.0</protobuf-java.version>
        <protobuf-java-util.version>3.19.1</protobuf-java-util.version>
        <rover-server-proto.release.version>1.1.34</rover-server-proto.release.version>
        <rover-server-api.release.version>1.1.27</rover-server-api.release.version>
        <rover-metadata-api.version>1.0.0-SNAPSHOT</rover-metadata-api.version>
        <rover-common.version>1.2.1-SNAPSHOT</rover-common.version>
        <shadow-boot-starter.version>1.1.6-SNAPSHOT</shadow-boot-starter.version>
        <!--redisson版本号配置 -->
        <redisson-spring-boot-starter.version>3.50.0</redisson-spring-boot-starter.version>

        <embedded-redis.version>0.6</embedded-redis.version>
        <Java-WebSocket.version>1.6.0</Java-WebSocket.version>
        <schedule-client-starter.version>2.4.4</schedule-client-starter.version>
        <disruptor.version>4.0.0</disruptor.version>
        <expiringmap.version>0.5.11</expiringmap.version>

        <rover-mqtt-spring-boot-starter.version>1.2.0-SNAPSHOT</rover-mqtt-spring-boot-starter.version>
        <spring-integration-mqtt.version>6.5.1</spring-integration-mqtt.version>
        <jmq.version>2.3.8-HOTFIX-T4</jmq.version>
        <rover-jsf-provider.version>1.1.0-SNAPSHOT</rover-jsf-provider.version>
        <jaxb-runtime.version>2.3.2</jaxb-runtime.version>
        <jaxb-api.version>2.3.1</jaxb-api.version>
        <commons-lang3.version>3.18.0</commons-lang3.version>
        <profiler.version>20240630</profiler.version>
        <rover-local-cache-boot-starter.version>1.2.0-SNAPSHOT</rover-local-cache-boot-starter.version>
    </properties>
    <!-- 打包配置信息 -->
    <profiles>
        <profile>
            <!-- 开发环境 -->
            <id>develop</id>
            <!-- 默认 -->
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <properties>
                <activatedProperties>develop</activatedProperties>
                <rover-server-proto.version>${rover-server-proto.release.version}-SNAPSHOT</rover-server-proto.version>
                <rover-server-api.version>${rover-server-api.release.version}-BETA-SNAPSHOT</rover-server-api.version>
                <rover-metadata-api.version>1.0.0-TEST-SNAPSHOT</rover-metadata-api.version>
            </properties>
        </profile>
        <profile>
            <!-- 测试环境 -->
            <id>test</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <activatedProperties>test</activatedProperties>
                <rover-server-proto.version>${rover-server-proto.release.version}-SNAPSHOT</rover-server-proto.version>
                <rover-server-api.version>${rover-server-api.release.version}-BETA-SNAPSHOT</rover-server-api.version>
                <rover-metadata-api.version>1.0.0-TEST-SNAPSHOT</rover-metadata-api.version>
            </properties>
        </profile>
        <profile>
            <!-- 预发测试环境 -->
            <id>beta</id>
            <properties>
                <activatedProperties>beta</activatedProperties>
                <rover-server-proto.version>${rover-server-proto.release.version}-SNAPSHOT</rover-server-proto.version>
                <rover-server-api.version>${rover-server-api.release.version}-BETA-SNAPSHOT</rover-server-api.version>
                <rover-metadata-api.version>1.0.0-BETA-SNAPSHOT</rover-metadata-api.version>
            </properties>
        </profile>
        <profile>
            <!-- 测试环境1 -->
            <id>test1</id>
            <properties>
                <activatedProperties>test1</activatedProperties>
                <rover-server-proto.version>${rover-server-proto.release.version}-SNAPSHOT</rover-server-proto.version>
                <rover-server-api.version>${rover-server-api.release.version}-BETA-SNAPSHOT</rover-server-api.version>
                <rover-metadata-api.version>1.0.0-BETA-SNAPSHOT</rover-metadata-api.version>
            </properties>
        </profile>
        <profile>
            <!-- 测试环境2 -->
            <id>test2</id>
            <properties>
                <activatedProperties>test2</activatedProperties>
                <rover-server-proto.version>${rover-server-proto.release.version}-SNAPSHOT</rover-server-proto.version>
                <rover-server-api.version>${rover-server-api.release.version}-BETA-SNAPSHOT</rover-server-api.version>
                <rover-metadata-api.version>1.0.0-BETA-SNAPSHOT</rover-metadata-api.version>
            </properties>
        </profile>
        <profile>
            <!-- 测试环境3 -->
            <id>test3</id>
            <properties>
                <activatedProperties>test3</activatedProperties>
                <rover-server-proto.version>${rover-server-proto.release.version}-SNAPSHOT</rover-server-proto.version>
                <rover-server-api.version>${rover-server-api.release.version}-BETA-SNAPSHOT</rover-server-api.version>
                <rover-metadata-api.version>1.0.0-BETA-SNAPSHOT</rover-metadata-api.version>
            </properties>
        </profile>
        <profile>
            <!-- 标准化环境 -->
            <id>standardize</id>
            <properties>
                <activatedProperties>standardize</activatedProperties>
                <rover-server-proto.version>${rover-server-proto.release.version}-SNAPSHOT</rover-server-proto.version>
                <rover-server-api.version>${rover-server-api.release.version}-BETA-SNAPSHOT</rover-server-api.version>
                <rover-metadata-api.version>1.0.0-STANDARDIZE-SNAPSHOT</rover-metadata-api.version>
            </properties>
        </profile>
        <profile>
            <!-- 生产环境 -->
            <id>product</id>
            <properties>
                <activatedProperties>product</activatedProperties>
                <rover-server-proto.version>${rover-server-proto.release.version}</rover-server-proto.version>
                <rover-server-api.version>${rover-server-api.release.version}</rover-server-api.version>
            </properties>
        </profile>
    </profiles>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud-dependencies.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba-dependencies.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.jdx.rover</groupId>
                <artifactId>rover-dependencies</artifactId>
                <version>${rover-dependencies.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>com.google.protobuf</groupId>
                <artifactId>protobuf-java</artifactId>
                <version>${protobuf-java.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.protobuf</groupId>
                <artifactId>protobuf-java-util</artifactId>
                <version>${protobuf-java-util.version}</version>
            </dependency>
            <dependency>
               <groupId>com.jdx.rover</groupId>
               <artifactId>rover-server-proto</artifactId>
               <version>${rover-server-proto.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jdx.rover</groupId>
                <artifactId>rover-server-api</artifactId>
                <version>${rover-server-api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jdx.rover</groupId>
                <artifactId>rover-metadata-api</artifactId>
                <version>${rover-metadata-api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jdx.rover</groupId>
                <artifactId>rover-common</artifactId>
                <version>${rover-common.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jdx.rover</groupId>
                <artifactId>shadow-boot-starter</artifactId>
                <version>${shadow-boot-starter.version}</version>
            </dependency>

            <!--redisson配置 -->
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-boot-starter</artifactId>
                <version>${redisson-spring-boot-starter.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-starter-actuator</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.redisson</groupId>
                        <artifactId>redisson-spring-data-30</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.redisson</groupId>
                <!-- for Spring Data Redis v.2.7.x -->
                <artifactId>redisson-spring-data-27</artifactId>
                <version>${redisson-spring-boot-starter.version}</version>
            </dependency>
            <!--hutool通用 -->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool-all.version}</version>
            </dependency>

            <!--mock redis-->
            <dependency>
                <groupId>com.github.kstyrc</groupId>
                <artifactId>embedded-redis</artifactId>
                <version>${embedded-redis.version}</version>
                <scope>test</scope>
            </dependency>

            <!--WebSocket客户端 -->
            <dependency>
                <groupId>org.java-websocket</groupId>
                <artifactId>Java-WebSocket</artifactId>
                <version>${Java-WebSocket.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>com.wangyin.schedule</groupId>
                <artifactId>schedule-client-starter</artifactId>
                <version>${schedule-client-starter.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.quartz-scheduler</groupId>
                        <artifactId>quartz</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.lmax</groupId>
                <artifactId>disruptor</artifactId>
                <version>${disruptor.version}</version>
            </dependency>

            <dependency>
                <groupId>net.jodah</groupId>
                <artifactId>expiringmap</artifactId>
                <version>${expiringmap.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jdx.rover.mqtt</groupId>
                <artifactId>rover-mqtt-spring-boot-starter</artifactId>
                <version>${rover-mqtt-spring-boot-starter.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.integration</groupId>
                <artifactId>spring-integration-mqtt</artifactId>
                <version>${spring-integration-mqtt.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.eclipse.paho</groupId>
                        <artifactId>org.eclipse.paho.client.mqttv3</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-collections4</artifactId>
                <version>${commons-collections4.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jd.jmq</groupId>
                <artifactId>jmq-client-spring</artifactId>
                <version>${jmq.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jdx.rover</groupId>
                <artifactId>rover-jsf-provider</artifactId>
                <version>${rover-jsf-provider.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jaxb</groupId>
                <artifactId>jaxb-runtime</artifactId>
                <version>${jaxb-runtime.version}</version>
            </dependency>
            <dependency>
                <groupId>javax.xml.bind</groupId>
                <artifactId>jaxb-api</artifactId>
                <version>${jaxb-api.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>${commons-lang3.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jd.ump</groupId>
                <artifactId>profiler</artifactId>
                <version>${profiler.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jdx.rover.local.cache</groupId>
                <artifactId>rover-local-cache-boot-starter</artifactId>
                <version>${rover-local-cache-boot-starter.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.tomee</groupId>
                <artifactId>jakartaee-api</artifactId>
                <version>${jakartaee-api.version}</version>
                <scope>provided</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

</project>