package com.jdx.rover.server.common.utils.host;

import java.net.InetAddress;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
public class HostUtils {
  /**
   * 获取host名称
   *
   * @return
   */
  public static String getHostName() {
    try {
      return InetAddress.getLocalHost().getHostName();
    } catch (Exception e) {
      log.error("Failed to get hostname of self. ", e);
      return "host";
    }
  }
}
