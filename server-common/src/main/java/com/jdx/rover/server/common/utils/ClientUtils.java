/*
 * Copyright (c) 2021 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.server.common.utils;

/**
 * 客户端帮助类,获取唯一标识
 *
 * <AUTHOR>
 */
public class ClientUtils {
  public static String createEndpointKey(String name, String type) {
    StringBuilder sb = new StringBuilder();
    sb.append(name);
    sb.append('@');
    sb.append(type);
    String key = sb.toString();
    return key;
  }
}
