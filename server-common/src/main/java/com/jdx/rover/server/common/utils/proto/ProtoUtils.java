package com.jdx.rover.server.common.utils.proto;

import com.google.protobuf.MessageOrBuilder;
import com.google.protobuf.util.JsonFormat;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
public class ProtoUtils {
    public static final String protoToJson(MessageOrBuilder message) {
        String result;
        try {
            result = JsonFormat.printer().omittingInsignificantWhitespace().print(message);
        } catch (Exception e) {
            result = "";
            log.error("proto转换成json错误,{}", message, e);
        }
        return result;
    }
}
