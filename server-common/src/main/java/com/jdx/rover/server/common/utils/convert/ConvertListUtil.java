package com.jdx.rover.server.common.utils.convert;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 转换列表数据
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/8/25
 */
public class ConvertListUtil {
    /**
     * 转换列表数据
     *
     * @param sourceList 源数据列表
     * @param converter  转换函数
     * @param <T>        源数据类型
     * @param <R>        目标数据类型
     * @return 转换后的列表
     */
    public static <T, R> List<R> convertList(List<T> sourceList, Function<T, R> converter) {
        if (sourceList == null || sourceList.isEmpty()) {
            return Collections.emptyList();
        }
        return sourceList.stream()
                .map(converter)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
}
