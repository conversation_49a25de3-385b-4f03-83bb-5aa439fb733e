package com.jdx.rover.server.common.utils.date;

import java.util.Date;

/**
 * 日期工具类
 *
 * <AUTHOR>
 */
public class ServerDateUtils {
    /**
     * 百万
     */
    public static final int MILLION = 1_000_000;

    /**
     * 通过毫秒获取日期
     *
     * @param nanosecond 纳秒
     * @return 纳秒转换的日期
     */
    public static Date getDateFromNanosecond(long nanosecond) {
        return new Date(nanosecond / MILLION);
    }
}
