server:
  port: 8080
spring:
  cloud:
    nacos:
      discovery:
        server-addr: http://jdxnacos-beta.jd.local/
        username: nacos
        password: nacos-beta@jdlX2022
  kafka: # 默认的kafka集群,发送String
    bootstrap-servers: broker-kafka-pqxxtsd6av-az1-0.jvessel-open-hb.jdcloud.com:9092,broker-kafka-pqxxtsd6av-az1-1.jvessel-open-hb.jdcloud.com:9092,broker-kafka-pqxxtsd6av-az1-2.jvessel-open-hb.jdcloud.com:9092
  data:
    redis:
      database: 14
      host: redis-yh7thvwgyt4s-proxy-nlb.jvessel-open-hb.jdcloud.com
      password: jdlX2022
project:
  kafka:
    second:
      bootstrap-servers: broker-kafka-pqxxtsd6av-az1-0.jvessel-open-hb.jdcloud.com:9092,broker-kafka-pqxxtsd6av-az1-1.jvessel-open-hb.jdcloud.com:9092,broker-kafka-pqxxtsd6av-az1-2.jvessel-open-hb.jdcloud.com:9092
websocket:
  maxTextMessageBufferSize: 8192
  maxBinaryMessageBufferSize: 81920
  maxSessionIdleTimeout: 60000
rover:
  mqtt:
    broker:
      jdxmqtt:
        url: tcp://jdxmqtt-beta.jd.local:2000
      vehiclemqtt:
        url: tcp://jdxmqtt-beta.jd.local:2000

jmq:
  address: nameserver.jmq.jd.local:80
  password: 871f044edd164da4974e4716fea79a7d
  app: servermqtttest