server:
  port: 8014
spring:
  cloud:
    nacos:
      discovery:
        server-addr: *************:8848
        username: nacos
        password: nacos@jdlX2022
  kafka: # 默认的kafka集群,发送String
    bootstrap-servers: *************:9092
  data:
    redis:
      host: *************
      password: jdlX2022@redis-test
project:
  kafka:
    second:
      bootstrap-servers:  *************:9092
rover:
  mqtt:
    broker:
      jdxmqtt:
        url: tcp://*************:1883
      vehiclemqtt:
        url: tcp://*************:1883