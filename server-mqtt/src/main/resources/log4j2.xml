<?xml version="1.0" encoding="UTF-8" ?>
<Configuration status="OFF">
    <Properties>
        <Property name="APP_NAME">rover-server-mqtt</Property>
        <Property name="INFO_FILE_NAME">app_info</Property>
        <Property name="ERROR_FILE_NAME">app_error</Property>
        <Property name="LOG_PATH">/export/Logs/${APP_NAME}</Property>
		<Property name="LOG_PATTERN">%d{yyyy-MM-dd HH:mm:ss.SSS}|%p|V1|%X{PFTID}|%X{REQID}|${APP_NAME}|${ENV}|${REGION}|${GROUP}|${IP}|%X{JLOG_SOURCE_IP}|%pid|[%t]|%X{JLOG_LOAD_PRESS}|%X{JLOG_SWIM_LANE}|%X{PIN}|%X{IDENTITY_ID}|%X{UUID}|%c - %m%n</Property>
    </Properties>
    <Appenders>
        <!-- 1、日志输出到控制台 -->
        <Console name="Console" target="SYSTEM_OUT">
            <!--过滤器设置输出的级别-->
            <ThresholdFilter level="INFO" onMatch="ACCEPT" onMismatch="DENY"/>
            <!-- 日志输出格式 -->
            <PatternLayout pattern="${LOG_PATTERN}"/>
        </Console>

        <!-- 2、日志输出到文件 INFO级别,每天产生一个新的日志文件,单个日志文件最大为1GB-->
        <RollingFile name="InfoDayRollingFile" fileName="${LOG_PATH}/${INFO_FILE_NAME}.log"
                     filePattern="${LOG_PATH}/info/$${date:yyyy-MM}/${INFO_FILE_NAME}-%d{yyyy-MM-dd}-%i.log.gz">
            <!--过滤器设置输出的级别-->
            <ThresholdFilter level="INFO" onMatch="ACCEPT" onMismatch="DENY"/>
            <!-- 日志输出格式 -->
            <PatternLayout pattern="${LOG_PATTERN}"/>
            <!--Policies:触发策略决定何时执行备份 -->
            <Policies>
                <!--TimeBasedTriggeringPolicy:日志文件按照时间备份 interval:每1天生成一个新文件 modulate:"0+interval"决定启动后第一次备份时间 -->
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
                <!--SizeBasedTriggeringPolicy:日志文件按照大小备份,size:指定日志文件最大为1000MB，单位可以为KB、MB或GB -->
                <SizeBasedTriggeringPolicy size="1000MB"/>
            </Policies>
            <!--DefaultRolloverStrategy:翻转策略决定如何执行备份 -->
            <!--max:最多保存5个备份文件，结合时间使用后，在每个时间段内最多有5个备份，多出来的会被覆盖 -->
            <!--compressionLevel:配置日志压缩级别，范围0-9，0不压缩，1压缩速度最快，9压缩率最好，目前只对于zip压缩文件类型有效 -->
            <DefaultRolloverStrategy max="100" compressionLevel="1">
                <!--Delete:删除匹配到的过期备份文件 -->
                <!--maxDepth:由于备份文件保存在${LOG_PATH}/info/$${date:yyyy-MM},所以目录深度设置为2 -->
                <Delete basePath="${LOG_PATH}/info" maxDepth="2">
                    <!--IfFileName:匹配文件名称 -->
                    <!--glob:匹配2级目录深度下的以.log.gz结尾的备份文件 -->
                    <IfFileName glob="*/*.log.gz"/>
                    <!--IfLastModified:匹配文件修改时间 -->
                    <!--age:匹配超过180天的文件，单位D、H、M、S分别表示天、小时、分钟、秒-->
                    <IfLastModified age="180D"/>
                </Delete>
            </DefaultRolloverStrategy>
        </RollingFile>

        <!-- 3、日志输出到文件 ERROR级别 每天产生一个新的日志文件-->
        <RollingFile name="ErrorDayRollingFile" fileName="${LOG_PATH}/${ERROR_FILE_NAME}.log"
                     filePattern="${LOG_PATH}/error/$${date:yyyy-MM}/${ERROR_FILE_NAME}-%d{yyyy-MM-dd}-%i.log.gz">
            <ThresholdFilter level="ERROR" onMatch="ACCEPT" onMismatch="DENY"/>
            <PatternLayout pattern="${LOG_PATTERN}"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
                <SizeBasedTriggeringPolicy size="1000MB"/>
            </Policies>
            <DefaultRolloverStrategy max="100" compressionLevel="1">
                <Delete basePath="${LOG_PATH}/error" maxDepth="2">
                    <IfFileName glob="*/*.log.gz"/>
                    <IfLastModified age="180D"/>
                </Delete>
            </DefaultRolloverStrategy>
        </RollingFile>
    </Appenders>

    <!--Loggers:定义日志级别和使用的Appenders -->
    <Loggers>
        <!-- 关闭kafka非业务日志，例如来自kafka-client包 -->
        <Logger name="org.apache.kafka" level="OFF"/>
        <!-- 关闭handleInboundPubRel方法中mqtt qos2交付成功info日志 -->
        <Logger name="org.eclipse.paho.mqttv5.client.internal.ClientState" level="warn"/>
        <!--jsf日志级别改为WARN-->
        <Logger name="com.jd.jsf.gd.registry.jsf" level="WARN"/>

        <!-- 根logger的设置，若代码中未找到指定的logger，则会根据继承机制，使用根logger-->
        <Root level="INFO">
            <appender-ref ref="InfoDayRollingFile"/>
            <appender-ref ref="ErrorDayRollingFile"/>
            <appender-ref ref="Console"/>
        </Root>
    </Loggers>
</Configuration>