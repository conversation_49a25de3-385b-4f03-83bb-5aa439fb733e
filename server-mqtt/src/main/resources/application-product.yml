server:
  port: 8080
spring:
  cloud:
    nacos:
      discovery:
        server-addr: http://jdxnacos.jd.local/
        username: nacos
        password: nacos@jdlX2022
  kafka: # 默认的kafka集群,发送String
    bootstrap-servers: broker-kafka-dzk69k556l-az1-0.jvessel-open-hb.jdcloud.com:9092,broker-kafka-dzk69k556l-az1-1.jvessel-open-hb.jdcloud.com:9092,broker-kafka-dzk69k556l-az1-2.jvessel-open-hb.jdcloud.com:9092
  data:
    redis:
      database: 2
      host: redis-ozj2fnhyspho-proxy-nlb.jvessel-open-hb.jdcloud.com
      password: jdlX2022
project:
  kafka:
    second:
      bootstrap-servers: broker-kafka-dzk69k556l-az1-0.jvessel-open-hb.jdcloud.com:9092,broker-kafka-dzk69k556l-az1-1.jvessel-open-hb.jdcloud.com:9092,broker-kafka-dzk69k556l-az1-2.jvessel-open-hb.jdcloud.com:9092
rover:
  mqtt:
    broker:
      jdxmqtt:
        url: tcp://jdxmqtt.jd.local:2000
      vehiclemqtt:
        url: tcp://jdxmqtt.jd.local:2000

jmq:
  address: nameserver.jmq.jd.local:80
  password: d223a93e00cf4a508a5d9d5578e76cf2
  app: roverservermqtt
  topic:
    suffix: