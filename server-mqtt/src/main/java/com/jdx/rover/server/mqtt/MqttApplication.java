/*
 * Copyright (c) 2021-2022 JD.com, Inc. All Rights Reserved.
 */
package com.jdx.rover.server.mqtt;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.ImportResource;
import org.springframework.integration.annotation.IntegrationComponentScan;

/**
 * <AUTHOR>
 */
@EnableDiscoveryClient
@ComponentScan(basePackages = {"com.jdx.rover.server.repository", "com.jdx.rover.server.mqtt", "com.jdx.rover.server.service"})
@EnableFeignClients(basePackages = {"com.jdx.rover.server.repository.feign"})
@SpringBootApplication
@IntegrationComponentScan(basePackages = {"com.jdx.rover.mqtt.autoconfigure.gateway"})
@ImportResource(locations = {"classpath:jmq.xml"})
public class MqttApplication {
  public static void main(String[] args) {
    SpringApplication.run(MqttApplication.class, args);
  }
}
