/*
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.server.mqtt.config;

import lombok.extern.slf4j.Slf4j;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.codec.JsonJacksonCodec;
import org.redisson.config.Config;
import org.redisson.spring.starter.RedissonProperties;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.data.redis.RedisProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.ReflectionUtils;

import java.io.IOException;
import java.lang.reflect.Method;

/**
 * 配置 redisson
 * 序列化方式为JsonJacksonCodec
 * SubscriptionsPerConnection为5000
 * subscriptionConnectionPoolSize为5000
 *
 * <AUTHOR>
 */
@Configuration
@Slf4j
public class RedissonConfig {
  private static final String REDIS_PROTOCOL_PREFIX = "redis://";
  private static final String REDISS_PROTOCOL_PREFIX = "rediss://";

  @Value("${project.redisson.timeout:10000}")
  private Integer timeout;

  @Value("${project.redisson.subscriptionsPerConnection:5000}")
  private Integer subscriptionsPerConnection;

  @Value("${project.redisson.subscriptionConnectionPoolSize:5000}")
  private Integer subscriptionConnectionPoolSize;

  @Value("${project.redisson.connectionPoolSize:500}")
  private Integer connectionPoolSize;

  @Value("${project.redisson.nettyThreads:256}")
  private Integer nettyThreads;

  /**
   * 超时配置参考 https://github.com/redisson/redisson/wiki/16.-FAQ#q-what-is-the-cause-of-redistimeoutexception
   */
  @Bean(destroyMethod = "shutdown")
  public RedissonClient redisson(RedissonProperties redissonProperties, RedisProperties redisProperties) throws IOException {
    Method timeoutMethod = ReflectionUtils.findMethod(RedisProperties.class, "getTimeout");
    Object timeoutValue = ReflectionUtils.invokeMethod(timeoutMethod, redisProperties);
    if (null == timeoutValue) {
      timeout = 10000;
    } else if (!(timeoutValue instanceof Integer)) {
      Method millisMethod = ReflectionUtils.findMethod(timeoutValue.getClass(), "toMillis");
      timeout = ((Long) ReflectionUtils.invokeMethod(millisMethod, timeoutValue)).intValue();
    } else {
      timeout = (Integer) timeoutValue;
    }

    Config config = new Config();
    String prefix = REDIS_PROTOCOL_PREFIX;
    Method method = ReflectionUtils.findMethod(RedisProperties.class, "isSsl");
    if (method != null && (Boolean) ReflectionUtils.invokeMethod(method, redisProperties)) {
      prefix = REDISS_PROTOCOL_PREFIX;
    }
    config.useSingleServer()
        .setAddress(prefix + redisProperties.getHost() + ":" + redisProperties.getPort())
        .setConnectTimeout(timeout)
        .setTimeout(timeout)
        .setDatabase(redisProperties.getDatabase())
        .setPassword(redisProperties.getPassword())
        .setSubscriptionsPerConnection(subscriptionsPerConnection)
        .setSubscriptionConnectionPoolSize(subscriptionConnectionPoolSize)
        .setConnectionPoolSize(connectionPoolSize);
    config.setNettyThreads(nettyThreads);
    config.setCodec(new JsonJacksonCodec());
    log.info("Redisson参数timeout={},subscriptionsPerConnection={},subscriptionConnectionPoolSize={},nettyThreads={},connectionPoolSize={}"
        , timeout, subscriptionsPerConnection, subscriptionConnectionPoolSize, connectionPoolSize, nettyThreads);
    return Redisson.create(config);
  }
}
