/*
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.server.mqtt.listener;

import com.fasterxml.jackson.core.type.TypeReference;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.mqtt.autoconfigure.annotation.MqttListener;
import com.jdx.rover.mqtt.autoconfigure.constant.MqttSerializerType;
import com.jdx.rover.mqtt.autoconfigure.domain.MqttMessageDTO;
import com.jdx.rover.server.api.domain.constants.mqtt.MqttBroker;
import com.jdx.rover.server.api.domain.dto.mqtt.MqttOnline;
import com.jdx.rover.server.api.domain.dto.mqtt.MqttRequestDTO;
import com.jdx.rover.server.api.domain.dto.pdu.PduMqttOnline;
import com.jdx.rover.server.domain.enums.mqtt.MqttTopicConstant;
import com.jdx.rover.server.domain.enums.mqtt.MqttWillTopicConstant;
import com.jdx.rover.server.mqtt.config.ProducerTopicProperties;
import com.jdx.rover.server.mqtt.service.MqttToKafkaService;
import com.jdx.rover.server.repository.jmq.JmqProducerManager;
import com.jdx.rover.server.repository.shadow.ShadowEventEnum;
import com.jdx.rover.server.repository.shadow.ShadowEventUtils;
import com.jdx.rover.shadow.boot.starter.util.EventUtil;
import com.jdx.rover.shadow.boot.starter.vo.EventRecordVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.paho.mqttv5.common.util.MqttTopicValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * mqtt遗嘱和上线消息
 *
 * <AUTHOR>
 * @date  2022-01-01
 */
@Slf4j
@Service
public class MqttWillListener {
    @Autowired
    private MqttToKafkaService mqttToKafkaService;

    @Autowired
    private KafkaTemplate<String, String> kafkaTemplate;

    @Value("${spring.profiles.active}")
    private String profileActive;
    /**
     * jmq消息发送producer
     */
    @Autowired
    private JmqProducerManager jmqProducerManager;

    /**
     * 生产主题配置类
     */
    @Autowired
    private ProducerTopicProperties producerTopicProperties;

    /**
     * 订阅遗嘱消息
     *
     * @param message
     */
    @MqttListener(topics = {MqttTopicConstant.ANDROID_WILL, MqttTopicConstant.ROVER_WILL, MqttTopicConstant.VIDEO_WILL
            , MqttWillTopicConstant.DRIVE_CONTROL_PAD_WILL, MqttWillTopicConstant.DRIVE_COCKPIT_WILL}
            , serializerType = MqttSerializerType.JSON, clazz = {MqttRequestDTO.class, MqttOnline.class}
            , shareGroup = MqttTopicConstant.SHARE_GROUP
            , mqttBroker = {MqttBroker.JDX_MQTT,MqttBroker.VEHICLE_MQTT})
    public void roverWill(MqttMessageDTO<MqttRequestDTO<MqttOnline>> message) {
        String jsonStr = JsonUtils.writeValueAsString(message.getPayload());
        log.info("收到will,topic={},payload={}", message.getTopic(), jsonStr);
        mqttToKafkaService.sendKafka(message.getTopic(), jsonStr);
        sendShadowEvent(message);
    }

    /**
     * 订阅遗嘱消息
     *
     * @param message
     */
    @MqttListener(topics = {MqttWillTopicConstant.PDU_WILL},
            shareGroup = MqttTopicConstant.SHARE_GROUP
            , mqttBroker = {MqttBroker.JDX_MQTT, MqttBroker.VEHICLE_MQTT})
    public void pduWill(MqttMessageDTO<String> message) {
        log.info("收到pdu will,topic={}, payload={}", message.getTopic(), message.getPayload());
        String jsonStr = message.getPayload();
        mqttToKafkaService.sendKafka(message.getTopic(), jsonStr);
        MqttRequestDTO<PduMqttOnline> messageDTO = JsonUtils.readValue(jsonStr, new TypeReference<MqttRequestDTO<PduMqttOnline>>() {
        });
        sendPduShadowEvent(messageDTO);
    }

    /**
     * 发送影子事件
     *
     * @param message
     */
    private void sendPduShadowEvent(MqttRequestDTO<PduMqttOnline> message) {
        List<String> paramList = new ArrayList<>(4);
        String vehicleName = message.getHeader().getSendName();
        paramList.add(vehicleName);
        if (message.getData().getOnline()) {
            paramList.add("成功");
        } else {
            paramList.add("断开");
        }
        paramList.add(message.getData().getSimiccid());
        paramList.add(String.valueOf(message.getData().getNetcsq()));
        EventRecordVO eventRecordVO = ShadowEventUtils.buildCommonEvent(vehicleName, ShadowEventEnum.SERVER_PDU_MQTT_STATE);
        eventRecordVO.setBodyArray(paramList.toArray(new String[0]));
        EventUtil.sendEvent(eventRecordVO);
    }


    /**
     * 发送影子事件
     *
     * @param message
     */
    private void sendShadowEvent(MqttMessageDTO<MqttRequestDTO<MqttOnline>> message) {
        List<String> paramList = new ArrayList<>(3);
        String vehicleName = message.getPayload().getHeader().getVehicleName();
        paramList.add(vehicleName);
        if (message.getPayload().getData().getOnline()) {
            paramList.add("成功");
        } else {
            paramList.add("断开");
        }
        ShadowEventEnum shadowEventEnum;
        if (MqttTopicValidator.isMatched(MqttTopicConstant.ANDROID_WILL, message.getTopic())) {
            shadowEventEnum = ShadowEventEnum.SERVER_ANDROID_MQTT_STATE;
        } else if (MqttTopicValidator.isMatched(MqttTopicConstant.VIDEO_WILL, message.getTopic())) {
            shadowEventEnum = ShadowEventEnum.SERVER_VIDEO_MQTT_STATE;
        } else {
            shadowEventEnum = ShadowEventEnum.SERVER_VIDEO_MQTT_STATE;
        }
        EventRecordVO eventRecordVO = ShadowEventUtils.buildCommonEvent(vehicleName, shadowEventEnum);
        eventRecordVO.setBodyArray(paramList.toArray(new String[0]));
        EventUtil.sendEvent(eventRecordVO);
    }

    /**
     * 中控远驾遗嘱消息转发
     *
     * @param message
     */
    @MqttListener(topics = {"r/+/drive/+/will/+"}, shareGroup = MqttTopicConstant.SHARE_GROUP
            , mqttBroker = {MqttBroker.JDX_MQTT,MqttBroker.VEHICLE_MQTT})
    public void monitorWill(MqttMessageDTO<String> message) {
        String mqttTopic = message.getTopic();
        String[] topicArray = StringUtils.split(mqttTopic, "/");
        if (topicArray == null || topicArray.length < 3) {
            return;
        }
        if (!Objects.equals(topicArray[1], profileActive)) {
            return;
        }
        jmqProducerManager.sendOrdered(producerTopicProperties.getRDriveWill(), message.getPayload(), topicArray[topicArray.length - 1]);
        log.info("收到mqtt透传will消息,topic={},payload={}", message.getTopic(), message.getPayload());
    }
}
