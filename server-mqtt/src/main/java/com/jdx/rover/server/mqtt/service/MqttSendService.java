/*
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.server.mqtt.service;

import com.jdx.rover.common.utils.ParameterCheckUtility;
import com.jdx.rover.mqtt.autoconfigure.MqttMultiConnectionManager;
import com.jdx.rover.mqtt.autoconfigure.constant.MqttQos;
import com.jdx.rover.server.api.domain.constants.mqtt.MqttBroker;
import com.jdx.rover.server.api.domain.vo.mqtt.MqttMessageVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.integration.mqtt.support.MqttHeaders;
import org.springframework.integration.support.MessageBuilder;
import org.springframework.messaging.MessageChannel;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class MqttSendService {
    @Autowired
    private MqttToKafkaService mqttToKafkaService;

    @Autowired
    private MqttMultiConnectionManager mqttMultiConnectionManager;

    /**
     * 发送mqtt消息
     *
     * @param mqttMessageVO
     */
    public void sendMqtt(MqttMessageVO mqttMessageVO) {
        log.info("待发送mqtt消息={}", mqttMessageVO);
        ParameterCheckUtility.checkNotNull(mqttMessageVO, "mqttMessageVO");
        ParameterCheckUtility.checkNotNullNorEmptyAfterTrimming(mqttMessageVO.getTopic(), "mqttMessageVO#topic");
        sendMqttOnly(mqttMessageVO);
        mqttToKafkaService.sendKafka(mqttMessageVO);
    }

    /**
     * 只发送mqtt消息
     *
     * @param mqttMessageVO
     */
    public void sendMqttOnly(MqttMessageVO mqttMessageVO) {
        if (mqttMessageVO.getQos() == null || mqttMessageVO.getQos() < MqttQos.QOS_AT_MOST_ONCE || mqttMessageVO.getQos() > MqttQos.QOS_ONLY_ONCE) {
            mqttMessageVO.setQos(MqttQos.QOS_AT_LEAST_ONCE);
        }
        if (mqttMessageVO.getRetained() == null) {
            mqttMessageVO.setRetained(false);
        }
        if (Objects.isNull(mqttMessageVO.getMqttBrokerId())) {
            mqttMessageVO.setMqttBrokerId(MqttBroker.JDX_MQTT);
        }
        sendToMqttQosRetained(mqttMessageVO.getTopic(), mqttMessageVO.getQos(), mqttMessageVO.getRetained()
                , mqttMessageVO.getMessage(), mqttMessageVO.getMqttBrokerId());
    }

    /**
     * 发送mqtt消息,数据字段支持字符串String,字节数组byte[],Json对象
     * 注意其它都需要转换成byte[]发送
     *
     * @param topic    消息主题
     * @param qos      消息质量
     * @param retained 是否保留
     * @param data     数据(String,byte[],json对象)
     * @see com.jdx.rover.mqtt.autoconfigure.constant.MqttQos
     */
    private void sendToMqttQosRetained(String topic, Integer qos, Boolean retained, Object data, String mqttBrokerId) {
        MessageChannel messageChannel = mqttMultiConnectionManager.getOutChannel(mqttBrokerId);
        MessageBuilder<Object> builder = MessageBuilder.withPayload(data)
                .setHeader(MqttHeaders.TOPIC, topic)
                .setHeader(MqttHeaders.QOS, qos)
                .setHeader(MqttHeaders.RETAINED, retained);
        messageChannel.send(builder.build());
    }
}
