/*
 * Copyright (c) 2024 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.server.mqtt.listener.ota;

import com.jdx.rover.mqtt.autoconfigure.annotation.MqttListener;
import com.jdx.rover.mqtt.autoconfigure.constant.MqttSerializerType;
import com.jdx.rover.mqtt.autoconfigure.domain.MqttMessageDTO;
import com.jdx.rover.server.api.domain.constants.mqtt.MqttBroker;
import com.jdx.rover.server.common.utils.proto.ProtoUtils;
import com.jdx.rover.server.domain.enums.mqtt.MqttTopicConstant;
import com.jdx.rover.server.mqtt.service.MqttToKafkaService;
import jdx.rover.ota.proto.OtaDataDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * mqtt远程遥控连接消息
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Slf4j
@Service
public class MqttOtaTaskStepListener {
    /**
     * mqtt消息转发kafka消息
     */
    private final MqttToKafkaService mqttToKafkaService;

    /**
     * 远程遥控连接消息
     */
    @MqttListener(topics = {MqttTopicConstant.OTA_PROGRESS, MqttTopicConstant.OTA_UPDATER_DELAY_REPLY}, serializerType = MqttSerializerType.BYTE
            , shareGroup = MqttTopicConstant.SHARE_GROUP
            , mqttBroker = {MqttBroker.JDX_MQTT,MqttBroker.VEHICLE_MQTT})
    public void otaProgress(MqttMessageDTO<byte[]> message) {
        OtaDataDto.OtaDataDTO dto = null;
        try {
            dto = OtaDataDto.OtaDataDTO.parseFrom(message.getPayload());
            String jsonStr = ProtoUtils.protoToJson(dto);
            mqttToKafkaService.sendKafka(message.getTopic(), jsonStr);
            log.info("receive topic={},payload={}", message.getTopic(), jsonStr);
        } catch (Exception e) {
            log.error("MqttOtaTaskStepListener.otaProgress error topic={},payload={}", message.getTopic()
                    , ProtoUtils.protoToJson(dto), e);
        }
    }
}
