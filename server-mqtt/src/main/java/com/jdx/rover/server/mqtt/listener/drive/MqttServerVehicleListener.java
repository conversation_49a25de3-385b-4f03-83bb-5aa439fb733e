/*
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.server.mqtt.listener.drive;

import cn.hutool.core.util.HexUtil;
import com.jdx.rover.mqtt.autoconfigure.annotation.MqttListener;
import com.jdx.rover.mqtt.autoconfigure.constant.MqttSerializerType;
import com.jdx.rover.mqtt.autoconfigure.domain.MqttMessageDTO;
import com.jdx.rover.server.api.domain.constants.mqtt.MqttBroker;
import com.jdx.rover.server.common.utils.proto.ProtoUtils;
import com.jdx.rover.server.domain.enums.mqtt.MqttTopicConstant;
import jdx.rover.remote.drive.server.proto.DriveServerVehicle;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * mqtt远程遥控连接消息
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class MqttServerVehicleListener {
    /**
     * 远程遥控连接消息
     *
     * @param message
     */
    @MqttListener(topics = {"reply/r/drive/server/vehicle/+/+"}
            , serializerType = MqttSerializerType.BYTE, shareGroup = MqttTopicConstant.SHARE_GROUP
            , mqttBroker = {MqttBroker.JDX_MQTT,MqttBroker.VEHICLE_MQTT})
    public void serverVehicle(MqttMessageDTO<byte[]> message) {
        try {
            DriveServerVehicle.ServerToVehicle dto = DriveServerVehicle.ServerToVehicle.parseFrom(message.getPayload());
            log.info("receive topic={},payload={}", message.getTopic(), ProtoUtils.protoToJson(dto));
        } catch (Exception e) {
            log.error("hand drive server to vehicle info error topic={},payload={}, error={}", message.getTopic()
                    , HexUtil.encodeHexStr(message.getPayload()), e.getMessage());
        }
    }
}
