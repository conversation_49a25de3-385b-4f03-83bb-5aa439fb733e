/*
 * Copyright (c) 2024 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.server.mqtt.jmq;

import com.fasterxml.jackson.core.type.TypeReference;
import com.jd.jmq.client.consumer.MessageListener;
import com.jd.jmq.common.message.Message;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.server.api.domain.vo.mqtt.MqttMessageVO;
import com.jdx.rover.server.mqtt.service.MqttSendService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;

/**
 * 通过jmq字符串发送mqtt消息
 *
 * <AUTHOR>
 * @date 2024/9/19
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class MqttSendStringConsumer implements MessageListener {

    private final MqttSendService mqttSendService;

    /**
     * 接收到消息后的处理
     *
     * @param messageList 消息
     */
    @Override
    public void onMessage(List<Message> messageList) {
        if (CollectionUtils.isEmpty(messageList)) {
            return;
        }
        if (messageList.size() > 1) {
            log.info("Received topic={}, size={}", messageList.get(0).getTopic(), messageList.size());
        }

        for (Message message : messageList) {
            try {
                handleOneMessage(message);
            } catch (Exception e) {
                log.error("处理消息失败!{}", message, e);
            }
        }
    }

    private void handleOneMessage(Message message) {
        log.info("待发送mqtt字符串消息={}", message.getText());
        MqttMessageVO<String> mqttMessageVO = JsonUtils.readValue(message.getText(), new TypeReference<MqttMessageVO<String>>() {
        });
        if (Objects.isNull(mqttMessageVO)) {
            log.info("收到mqtt发送消息转换结果为null={}", message);
            return;
        }
        mqttSendService.sendMqttOnly(mqttMessageVO);
    }
}
