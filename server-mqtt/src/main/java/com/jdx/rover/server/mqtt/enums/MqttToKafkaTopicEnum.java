/*
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.server.mqtt.enums;

import cn.hutool.extra.spring.SpringUtil;
import com.jdx.rover.server.domain.enums.mqtt.MqttDriveTopicConstant;
import com.jdx.rover.server.domain.enums.mqtt.MqttTopicConstant;
import com.jdx.rover.server.domain.enums.mqtt.MqttWillTopicConstant;
import com.jdx.rover.server.mqtt.config.ProducerTopicProperties;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.paho.mqttv5.common.util.MqttTopicValidator;

/**
 * mqtt主题关联kafka主题.
 *
 * <AUTHOR>
 * @date  2022-01-01
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
@Slf4j
public enum MqttToKafkaTopicEnum {
    /**
     * mqtt转kafka主题枚举
     */
    ANDROID_WILL(MqttWillTopicConstant.ANDROID_WILL, SpringUtil.getBean(ProducerTopicProperties.class).getMqttAndroidWill(), "安卓遗嘱"),

    REPLY_R_DRIVE_CONTROL_CONNECT(MqttDriveTopicConstant.REPLY_R_DRIVE_CONTROL_CONNECT, SpringUtil.getBean(ProducerTopicProperties.class).getReplyRDriveControlConnectServer(), "平行驾驶接管响应指令"),

    REPLY_ANDROID_COMMAND(MqttTopicConstant.REPLY_ANDROID_COMMAND, SpringUtil.getBean(ProducerTopicProperties.class).getMqttReplyAndroidCommand(), "回复安卓指令"),
    ANDROID_INFO(MqttTopicConstant.ANDROID_INFO, SpringUtil.getBean(ProducerTopicProperties.class).getMqttAndroidInfo(), "安卓消息"),

    OTA_PROGRESS(MqttTopicConstant.OTA_PROGRESS, SpringUtil.getBean(ProducerTopicProperties.class).getMqttOtaProgress(), "OTA升级进度"),

    PDU_WILL(MqttWillTopicConstant.PDU_WILL, SpringUtil.getBean(ProducerTopicProperties.class).getRPduWill(), "PDU遗嘱"),
    ;

    /**
     * mqtt主题
     */
    private String mqttTopic;

    /**
     * kafka主题
     */
    private String kafkaTopic;

    /**
     * 描述标题
     */
    private String title;

    public static MqttToKafkaTopicEnum getByMqttTopic(final String mqttTopic) {
        for (MqttToKafkaTopicEnum itemEnum : MqttToKafkaTopicEnum.values()) {
            boolean matched = MqttTopicValidator.isMatched(itemEnum.mqttTopic, mqttTopic);
            if (matched) {
                return itemEnum;
            }
        }
        return null;
    }
}
