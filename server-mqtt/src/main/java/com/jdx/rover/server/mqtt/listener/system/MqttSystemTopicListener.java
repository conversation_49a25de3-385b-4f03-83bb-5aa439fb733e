/*
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.server.mqtt.listener.system;

import com.jd.jmq.common.message.Message;
import com.jdx.rover.mqtt.autoconfigure.annotation.MqttListener;
import com.jdx.rover.mqtt.autoconfigure.domain.MqttMessageDTO;
import com.jdx.rover.server.api.domain.constants.mqtt.MqttBroker;
import com.jdx.rover.server.domain.enums.mqtt.MqttTopicConstant;
import com.jdx.rover.server.mqtt.config.ProducerTopicProperties;
import com.jdx.rover.server.repository.jmq.JmqProducerManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * mqtt遗嘱和上线消息
 *
 * <AUTHOR>
 * @date  2022-01-01
 */
@RequiredArgsConstructor
@Slf4j
@Service
public class MqttSystemTopicListener {
    /**
     * jmq消息发送producer
     */
    private final JmqProducerManager jmqProducerManager;

    /**
     * 生产主题配置类
     */
    private final ProducerTopicProperties producerTopicProperties;

    /**
     * 订阅mqtt连接消息
     */
    @MqttListener(topics = {"$SYS/brokers/+/clients/+/connected"}, shareGroup = MqttTopicConstant.SHARE_GROUP
            , mqttBroker = {MqttBroker.JDX_MQTT,MqttBroker.VEHICLE_MQTT})
    public void connected(MqttMessageDTO<String> message) {
        jmqProducerManager.sendAsync(new Message(producerTopicProperties.getMqttSystemConnected(), message.getPayload()));
        log.info("收到mqtt客户端上线事件,topic={},payload={}", message.getTopic(), message.getPayload());
    }

    /**
     * 订阅mqtt断开连接消息
     */
    @MqttListener(topics = {"$SYS/brokers/+/clients/+/disconnected"}, shareGroup = MqttTopicConstant.SHARE_GROUP
            , mqttBroker = {MqttBroker.JDX_MQTT,MqttBroker.VEHICLE_MQTT})
    public void disconnected(MqttMessageDTO<String> message) {
        jmqProducerManager.sendAsync(new Message(producerTopicProperties.getMqttSystemDisconnected(), message.getPayload()));
        log.info("收到mqtt客户端下线事件,topic={},payload={}", message.getTopic(), message.getPayload());
    }
}
