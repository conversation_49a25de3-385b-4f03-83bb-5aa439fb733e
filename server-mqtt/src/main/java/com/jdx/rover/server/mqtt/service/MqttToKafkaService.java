/*
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.server.mqtt.service;

import com.jd.jmq.common.message.Message;
import com.jdx.rover.server.api.domain.vo.mqtt.MqttMessageVO;
import com.jdx.rover.server.mqtt.enums.MqttToKafkaTopicEnum;
import com.jdx.rover.server.repository.jmq.JmqProducerManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.serialization.ByteArraySerializer;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.core.ProducerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

/**
 * mqtt消息转发kafka消息
 *
 * <AUTHOR>
 * @date  2022-01-01
 */
@Service
@Slf4j
public class MqttToKafkaService {

    @Value("${spring.kafka.bootstrap-servers}")
    private String bootstrapServers;

    @Autowired
    private KafkaTemplate<String, String> kafkaTemplate;
    /**
     * jmq消息发送producer
     */
    @Autowired
    private JmqProducerManager jmqProducerManager;

    /**
     * 发送字节的kafka
     */
    private static KafkaTemplate<String, byte[]> kafkaByteTemplate;

    @PostConstruct
    public void init() {
        kafkaByteTemplate = createByteTemplate();
    }

    /**
     * 发送mqtt消息
     *
     * @param mqttMessageVO
     */
    public void sendKafka(MqttMessageVO mqttMessageVO) {
        Object message = mqttMessageVO.getMessage();
        String jsonStr = null;
        try {
            if (message != null) {
                if (message instanceof String) {
                    jsonStr = String.class.cast(message);
                } else if (message instanceof byte[]) {
                    jsonStr = new String((byte[]) message);
                }
            }
            sendKafka(mqttMessageVO.getTopic(), jsonStr);
        } catch (Exception e) {
            log.error("发送kafka消息失败,{}", mqttMessageVO, e);
        }
    }

    /**
     * mqtt消息转发kafka消息
     *
     * @param mqttTopic
     * @param payload
     */
    public void sendKafka(String mqttTopic, String payload) {
        MqttToKafkaTopicEnum em = MqttToKafkaTopicEnum.getByMqttTopic(mqttTopic);
        if (em == null) {
//            log.error("未找到对应的kafka主题!mqttTopic={}", mqttTopic);
            return;
        }
        if (StringUtils.isBlank(em.getKafkaTopic())) {
            return;
        }
        jmqProducerManager.sendOrdered(new Message(em.getKafkaTopic(), payload, mqttTopic));
//        kafkaTemplate.send(em.getKafkaTopic(), payload);
    }

    /**
     * 创建发送byte[]数组kafka模板
     */
    private KafkaTemplate<String, byte[]> createByteTemplate() {
        Map<String, Object> props = new HashMap<>();
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
        props.put(ProducerConfig.RETRIES_CONFIG, 0);
        props.put(ProducerConfig.BATCH_SIZE_CONFIG, 16384);
        props.put(ProducerConfig.LINGER_MS_CONFIG, 1);
        props.put(ProducerConfig.BUFFER_MEMORY_CONFIG, 33554432);
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, ByteArraySerializer.class);
        ProducerFactory<String, byte[]> pf = new DefaultKafkaProducerFactory(props);
        KafkaTemplate<String, byte[]> template = new KafkaTemplate<>(pf);
        return template;
    }

    public KafkaTemplate<String, byte[]> getKafkaByteTemplate() {
        return kafkaByteTemplate;
    }
}
