/*
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.server.mqtt.jsf;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.jsf.provider.AbstractProvider;
import com.jdx.rover.server.api.domain.vo.mqtt.MqttMessageVO;
import com.jdx.rover.server.api.service.mqtt.MqttSendJsfService;
import com.jdx.rover.server.mqtt.service.MqttSendService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 发送mqtt的jsf服务
 *
 * <AUTHOR>
 * @date 2024/10/12
 */
@Service
@RequiredArgsConstructor
public class MqttSendJsfServiceImpl extends AbstractProvider<MqttSendJsfService> implements MqttSendJsfService {
    private final MqttSendService mqttSendService;

    /**
     * 发送字符串mqtt消息
     */
    @Override
    public HttpResult<Void> sendString(MqttMessageVO<String> mqttMessageVO) {
        mqttSendService.sendMqtt(mqttMessageVO);
        return HttpResult.success();
    }

    /**
     * 发送byte数组mqtt消息
     */
    @Override
    public HttpResult<Void> sendBytes(MqttMessageVO<byte[]> mqttMessageVO) {
        mqttSendService.sendMqtt(mqttMessageVO);
        return HttpResult.success();
    }
}