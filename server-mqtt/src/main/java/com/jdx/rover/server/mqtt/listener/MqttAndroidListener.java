/*
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.server.mqtt.listener;

import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.mqtt.autoconfigure.annotation.MqttListener;
import com.jdx.rover.mqtt.autoconfigure.domain.MqttMessageDTO;
import com.jdx.rover.server.api.domain.constants.mqtt.MqttBroker;
import com.jdx.rover.server.api.domain.dto.mqtt.MqttRequestDTO;
import com.jdx.rover.server.api.domain.dto.mqtt.MqttResponseDTO;
import com.jdx.rover.server.api.domain.dto.mqtt.MqttResponseHeader;
import com.jdx.rover.server.api.domain.enums.mqtt.MqttMessageStateEnum;
import com.jdx.rover.server.api.domain.vo.mqtt.MqttMessageVO;
import com.jdx.rover.server.domain.enums.mqtt.MqttTopicConstant;
import com.jdx.rover.server.domain.enums.mqtt.MqttTopicPrefixConstant;
import com.jdx.rover.server.mqtt.service.MqttSendService;
import com.jdx.rover.server.mqtt.service.MqttToKafkaService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * mqtt 安卓上报消息
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class MqttAndroidListener {
    @Autowired
    private MqttToKafkaService mqttToKafkaService;

    @Autowired
    private MqttSendService mqttSendService;

    /**
     * 安卓透传消息
     *
     * @param message
     */
    @MqttListener(topics = {MqttTopicConstant.ANDROID_COMMAND, MqttTopicConstant.REPLY_ANDROID_COMMAND
            , MqttTopicConstant.REPLY_ANDROID_INFO}
            , shareGroup = MqttTopicConstant.SHARE_GROUP
            , mqttBroker = {MqttBroker.JDX_MQTT,MqttBroker.VEHICLE_MQTT})
    public void androidCommand(MqttMessageDTO<String> message) {
        String payload = message.getPayload();
        mqttToKafkaService.sendKafka(message.getTopic(), payload);
        log.info("收到mqtt透传消息,topic={},payload={}", message.getTopic(), payload);
    }

    /**
     * 安卓透传消息
     *
     * @param message
     */
    @MqttListener(topics = {MqttTopicConstant.ANDROID_INFO}, shareGroup = MqttTopicConstant.SHARE_GROUP
            , mqttBroker = {MqttBroker.JDX_MQTT,MqttBroker.VEHICLE_MQTT})
    public void androidInfo(MqttMessageDTO<String> message) {
        String payload = message.getPayload();
        mqttToKafkaService.sendKafka(message.getTopic(), payload);

        MqttRequestDTO mqttRequestDTO = JsonUtils.readValue(payload, MqttRequestDTO.class);
        String vehicleName = mqttRequestDTO.getHeader().getVehicleName();
        MqttResponseHeader mqttResponseHeader = new MqttResponseHeader();
        mqttResponseHeader.setMessageType(mqttRequestDTO.getHeader().getMessageType());
        mqttResponseHeader.setRequestId(mqttRequestDTO.getHeader().getRequestId());
        mqttResponseHeader.setVehicleName(vehicleName);
        mqttResponseHeader.setRequestTime(mqttRequestDTO.getHeader().getRequestTime());
        mqttResponseHeader.setResponseTime(System.currentTimeMillis());
        mqttResponseHeader.setMessageState(MqttMessageStateEnum.SUCCESS.getValue());
        MqttResponseDTO<String> mqttResponseDTO = new MqttResponseDTO<>();
        mqttResponseDTO.setHeader(mqttResponseHeader);
        String topicName = MqttTopicPrefixConstant.REPLY_ANDROID_INFO_PREFIX + vehicleName;

        MqttMessageVO mqttMessageVO = new MqttMessageVO();
        mqttMessageVO.setTopic(topicName);
        mqttMessageVO.setMessage(JsonUtils.writeValueAsString(mqttResponseDTO));
        mqttMessageVO.setMqttBrokerId(MqttBroker.VEHICLE_MQTT);
        mqttSendService.sendMqttOnly(mqttMessageVO);

        log.info("收到安卓mqtt透传消息,topic={},payload={}", message.getTopic(), payload);
    }
}
