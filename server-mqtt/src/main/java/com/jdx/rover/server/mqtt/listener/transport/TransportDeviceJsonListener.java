/*
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.server.mqtt.listener.transport;

import com.jd.jmq.common.message.Message;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.mqtt.autoconfigure.annotation.MqttListener;
import com.jdx.rover.mqtt.autoconfigure.domain.MqttMessageDTO;
import com.jdx.rover.server.api.domain.constants.mqtt.MqttBroker;
import com.jdx.rover.server.api.domain.dto.mqtt.MqttToJmqDTO;
import com.jdx.rover.server.domain.enums.mqtt.MqttTopicConstant;
import com.jdx.rover.server.mqtt.config.ProducerTopicProperties;
import com.jdx.rover.server.repository.jmq.JmqProducerManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 设备JSON上报
 *
 * <AUTHOR>
 * @date 2024-09-20
 */
@RequiredArgsConstructor
@Slf4j
@Service
public class TransportDeviceJsonListener {
    /**
     * 环境变量
     */
    @Value("${spring.profiles.active}")
    private String profileActive;

    /**
     * jmq消息发送producer
     */
    private final JmqProducerManager jmqProducerManager;

    /**
     * 生产主题配置类
     */
    private final ProducerTopicProperties producerTopicProperties;

    /**
     * 订阅mqtt连接消息
     */
    @MqttListener(topics = {"t/+/+/+/+/json/property", "t/+/+/+/+/json/will", "t/+/+/+/+/json/events", "t/+/+/+/+/json/services/reply", "t/+/+/+/+/json/ota/events"}
            , shareGroup = MqttTopicConstant.SHARE_GROUP
            , mqttBroker = {MqttBroker.JDX_MQTT,MqttBroker.VEHICLE_MQTT})
    public void sendJsonProperty(MqttMessageDTO<String> message) {
        try {
            log.info("收到设备JSON上报,topic={},payload={}", message.getTopic(), message.getPayload());
            String[] topicArray = StringUtils.split(message.getTopic(), "/");
            if (!Objects.equals(topicArray[1], profileActive)) {
                return;
            }

            Message jmqMsg = new Message();
            if (Objects.equals(topicArray[6], "events")) {
                jmqMsg.setTopic(producerTopicProperties.getMqttJsonEvents());
            } else if (Objects.equals(topicArray[6], "services")) {
                jmqMsg.setTopic(producerTopicProperties.getMqttJsonServices());
            } else if (Objects.equals(topicArray[6], "ota")) {
                jmqMsg.setTopic(producerTopicProperties.getMqttJsonOta());
            } else {
                jmqMsg.setTopic(producerTopicProperties.getMqttJsonProperty());
            }
            MqttToJmqDTO<String> mqttToJmqMessage = new MqttToJmqDTO<>(message.getTopic(), message.getPayload());
            jmqMsg.setText(JsonUtils.writeValueAsString(mqttToJmqMessage));
            jmqMsg.setBusinessId(topicArray[3]);
            jmqProducerManager.sendOrdered(jmqMsg);
        } catch (Exception e) {
            log.error("mqtt连接发送jmq失败,topic={},payload={}", message.getTopic(), message.getPayload(), e);
        }
    }
}
