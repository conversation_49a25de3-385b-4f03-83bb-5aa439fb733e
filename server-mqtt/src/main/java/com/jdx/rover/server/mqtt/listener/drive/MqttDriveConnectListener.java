/*
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.server.mqtt.listener.drive;

import com.jdx.rover.common.constant.NumberConstant;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.mqtt.autoconfigure.annotation.MqttListener;
import com.jdx.rover.mqtt.autoconfigure.constant.MqttSerializerType;
import com.jdx.rover.mqtt.autoconfigure.domain.MqttMessageDTO;
import com.jdx.rover.server.api.domain.constants.mqtt.MqttBroker;
import com.jdx.rover.server.api.domain.dto.drive.DriveConnectDTO;
import com.jdx.rover.server.common.utils.proto.ProtoUtils;
import com.jdx.rover.server.domain.enums.mqtt.MqttTopicConstant;
import com.jdx.rover.server.mqtt.service.MqttToKafkaService;
import jdx.rover.remote.drive.control.proto.DriveControlDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * mqtt远程遥控连接消息
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class MqttDriveConnectListener {
    @Autowired
    private MqttToKafkaService mqttToKafkaService;

    /**
     * 远程遥控连接消息
     *
     * @param message
     */
    @MqttListener(topics = {"reply/r/drive/control/connect/server/+"}
            , serializerType = MqttSerializerType.BYTE
            , shareGroup = MqttTopicConstant.SHARE_GROUP
            , mqttBroker = {MqttBroker.JDX_MQTT,MqttBroker.VEHICLE_MQTT})
    public void connect(MqttMessageDTO<byte[]> message) {
        DriveControlDto.ControlConnect dto = null;
        try {
            dto = DriveControlDto.ControlConnect.parseFrom(message.getPayload());
            DriveConnectDTO driveConnectDTO = buildDriveConnectDTO(dto, message.getTopic());
            String jsonStr = JsonUtils.writeValueAsString(driveConnectDTO);
            mqttToKafkaService.sendKafka(message.getTopic(), jsonStr);
            log.info("receive topic={},payload={}", message.getTopic(), ProtoUtils.protoToJson(dto));
        } catch (Exception e) {
            log.error("hand teleoperation info error topic={},payload={}", message.getTopic()
                    , ProtoUtils.protoToJson(dto), e);
        }
    }

    /**
     * 构建远程遥控连接对象
     *
     * @param dto
     * @return
     */
    private DriveConnectDTO buildDriveConnectDTO(DriveControlDto.ControlConnect dto
            , String topicName) {
        DriveConnectDTO result = new DriveConnectDTO();
        if (dto == null) {
            return result;
        }
        String clientName = StringUtils.substringAfterLast(topicName, "/");
        result.setClientName(clientName);
        result.setVehicleName(dto.getVehicleName());
        result.setRecordTime(new Date(dto.getResponseHeader().getRequestId() / NumberConstant.MILLION));
        result.setCockpitNumber(dto.getCockpitName());
        result.setConnectOperation(dto.getConnectOperation().name());
        result.setMessageState(dto.getResponseHeader().getMessageState().name());
        result.setErrorCode(dto.getResponseHeader().getErrorCode() + "");
        result.setErrorMessage(dto.getResponseHeader().getErrorMessage());
        return result;
    }
}
