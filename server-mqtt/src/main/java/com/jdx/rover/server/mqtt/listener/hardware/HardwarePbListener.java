/*
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.server.mqtt.listener.hardware;

import cn.hutool.core.codec.Base64Encoder;
import cn.hutool.core.text.StrPool;
import com.jdx.rover.mqtt.autoconfigure.annotation.MqttListener;
import com.jdx.rover.mqtt.autoconfigure.constant.MqttSerializerType;
import com.jdx.rover.mqtt.autoconfigure.domain.MqttMessageDTO;
import com.jdx.rover.server.api.domain.constants.mqtt.MqttBroker;
import com.jdx.rover.server.domain.enums.mqtt.MqttTopicConstant;
import com.jdx.rover.server.mqtt.config.ProducerTopicProperties;
import com.jdx.rover.server.repository.jmq.JmqProducerManager;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * MQTT车身域硬件信息监听器
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class HardwarePbListener {
    /**
     * 业务类型在主题路径中的索引位置
     */
    private static final int BUSINESS_TYPE_INDEX = 2;

    /**
     * 业务ID在主题路径中的索引位置
     */
    private static final int BUSINESS_ID_INDEX = 3;

    /**
     * jmq消息发送producer
     */
    private final JmqProducerManager jmqProducerManager;

    /**
     * 生产主题配置类
     */
    private final ProducerTopicProperties producerTopicProperties;

    /**
     * 业务类型到JMQ主题的映射
     */
    private static final Map<String, String> BUSINESS_TO_JMQ_MAP = new ConcurrentHashMap<>(3);

    /**
     * 初始化业务类型到JMQ主题的映射
     */
    @PostConstruct
    public void init() {
        BUSINESS_TO_JMQ_MAP.put("property", producerTopicProperties.getMqttHardwareProperty());
        BUSINESS_TO_JMQ_MAP.put("services_reply", producerTopicProperties.getMqttHardwareServicesReply());
        BUSINESS_TO_JMQ_MAP.put("data", producerTopicProperties.getMqttHardwareData());
    }

    /**
     * 车身域硬件信息
     */
    @MqttListener(
            topics = {
                    MqttTopicConstant.R_HARDWARE_STATE,
                    MqttTopicConstant.R_HARDWARE_PROPERTY,
                    MqttTopicConstant.R_HARDWARE_SERVICES_REPLY
            },
            serializerType = MqttSerializerType.BYTE,
            shareGroup = MqttTopicConstant.SHARE_GROUP,
            mqttBroker = {MqttBroker.JDX_MQTT, MqttBroker.VEHICLE_MQTT}
    )
    public void hardwarePb(MqttMessageDTO<byte[]> message) {
        String topic = message.getTopic();
        byte[] payload = message.getPayload();
        try {
            String[] topicArray = StringUtils.split(message.getTopic(), StrPool.SLASH);
            String businessType = topicArray[BUSINESS_TYPE_INDEX];
            String jmqTopic = getJmqTopicForBusinessType(businessType);
            if (jmqTopic == null) {
                log.error("未识别的主题,topic={},payload={}", topic, Base64Encoder.encode(payload));
                return;
            }
            String businessId = topicArray[BUSINESS_ID_INDEX];
            jmqProducerManager.sendMqttBytesToJmq(topic, payload, jmqTopic, businessId);
            log.info("收到车身域硬件数据上报,topic={},payload={}", topic, Base64Encoder.encode(payload));
        } catch (Exception e) {
            log.error("mqtt连接发送jmq失败,topic={},payload={}", topic, Base64Encoder.encode(payload), e);
        }
    }

    /**
     * 根据业务类型获取对应的JMQ主题
     *
     * @param businessType 业务类型
     * @return 对应的JMQ主题，如果没有匹配则返回null
     */
    private String getJmqTopicForBusinessType(String businessType) {
        return BUSINESS_TO_JMQ_MAP.get(businessType);
    }
}
