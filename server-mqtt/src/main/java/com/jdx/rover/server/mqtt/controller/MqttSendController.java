/*
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.server.mqtt.controller;

import com.jdx.rover.server.api.domain.vo.mqtt.MqttMessageVO;
import com.jdx.rover.server.mqtt.service.MqttSendService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * <AUTHOR>
 */
@RestController
public class MqttSendController {
  @Autowired
  private MqttSendService mqttSendService;

  /**
   * 发送字符串mqtt消息
   *
   * @param mqttMessageVO
   */
  @PostMapping("/server/mqtt/send/string")
  public void sendString(@RequestBody @Valid MqttMessageVO<String> mqttMessageVO) {
    mqttSendService.sendMqtt(mqttMessageVO);
  }

  /**
   * 发送byte数组mqtt消息
   *
   * @param mqttMessageVO
   */
  @PostMapping("/server/mqtt/send/bytes")
  public void sendBytes(@RequestBody @Valid MqttMessageVO<byte[]> mqttMessageVO) {
    mqttSendService.sendMqtt(mqttMessageVO);
  }
}
