package com.jdx.rover.server.mqtt.listener;

import com.jdx.rover.mqtt.autoconfigure.annotation.MqttListener;
import com.jdx.rover.mqtt.autoconfigure.domain.MqttMessageDTO;
import com.jdx.rover.server.api.domain.constants.mqtt.MqttBroker;
import com.jdx.rover.server.domain.enums.mqtt.MqttTopicConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * mqtt PDU上报消息
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class MqttPduListener {

  /**
   * PDU透传消息
   *
   * @param message 消息
   */
  @MqttListener(topics = {MqttTopicConstant.PDU_COMMAND, MqttTopicConstant.REPLY_PDU_COMMAND}
          , shareGroup = MqttTopicConstant.SHARE_GROUP
          , mqttBroker = {MqttBroker.JDX_MQTT, MqttBroker.VEHICLE_MQTT})
  public void pduCommand(MqttMessageDTO<String> message) {
    String payload = message.getPayload();
    log.info("收到pdu mqtt透传消息,topic={},payload={}", message.getTopic(), payload);
  }


}
