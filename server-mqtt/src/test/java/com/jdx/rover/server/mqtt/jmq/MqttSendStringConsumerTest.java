package com.jdx.rover.server.mqtt.jmq;

import com.google.common.collect.Lists;
import com.jd.jmq.common.message.Message;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.server.api.domain.vo.mqtt.MqttMessageVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * 通过jmq字符串发送mqtt消息测试
 *
 * <AUTHOR>
 * @date 2024/12/5
 */
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@Slf4j
@SpringBootTest
class MqttSendStringConsumerTest {

    /**
     * 通过jmq字符串发送mqtt消息
     */
    private final MqttSendStringConsumer mqttSendStringConsumer;

    @Test
    void onMessage() {
        MqttMessageVO<String> mqttMessageVO = new MqttMessageVO<>();
        mqttMessageVO.setTopic("t/test/pda/pda0001/pda/json/services");
        mqttMessageVO.setMessage("{\"header\":{\"messageType\":\"test\",\"requestId\":1658238662844077435,\"sendName\":\"PDA00001\",\"requestTime\":1658238662844,\"needResponse\":true},\"data\":{}}");

        Message message = new Message();
        message.setTopic("mqtt_send_string_test");
        message.setText(JsonUtils.writeValueAsString(mqttMessageVO));
        message.setBusinessId("pda0001");
        mqttSendStringConsumer.onMessage(Lists.newArrayList(message));
    }
}