package com.jdx.rover.server.mqtt.listener.transport;

import cn.hutool.core.util.HexUtil;
import com.jdx.rover.mqtt.autoconfigure.domain.MqttMessageDTO;
import com.jdx.rover.server.common.utils.proto.ProtoUtils;
import com.jdx.rover.transport.base.BaseRequestHeader;
import com.jdx.rover.transport.base.LastWill;
import com.jdx.rover.transport.inspector.ReportPhotoTask;
import com.jdx.rover.transport.inspector.RobotHeartbeat;
import com.jdx.rover.transport.inspector.RobotHeartbeat.ErrorCodeEnum;
import com.jdx.rover.transport.inspector.RobotTask;
import com.jdx.rover.transport.inspector.RobotTask.FromActionEnum;
import com.jdx.rover.transport.inspector.RobotTask.TaskTypeEnum;
import com.jdx.rover.transport.inspector.RobotTask.ToActionEnum;
import com.jdx.rover.transport.inspector.TransportInspectorDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * 注
 *
 * <AUTHOR>
 * @date 2024/10/25
 */
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@Slf4j
@SpringBootTest
public class TransportDevicePbListenerTest {
    private final TransportDevicePbListener transportDevicePbListener;

    @Test
    void sendDevicePb() {
        MqttMessageDTO<byte[]> mqttMessageDTO = getMqttMessageDTO();
        transportDevicePbListener.sendDevicePb(mqttMessageDTO);
    }

    private static MqttMessageDTO<byte[]> getMqttMessageDTO() {
//        TransportInspectorDTO.Builder dtoBuilder = getRobotHeartbeatDto();
//        TransportInspectorDTO.Builder dtoBuilder = getOnlineWillDto();
        TransportInspectorDTO.Builder dtoBuilder = getReportPhotoTask();
        System.out.println(HexUtil.encodeHexStr(dtoBuilder.build().toByteArray()));
        System.out.println(ProtoUtils.protoToJson(dtoBuilder));

        MqttMessageDTO<byte[]> mqttMessageDTO = new MqttMessageDTO<>();
        mqttMessageDTO.setTopic("t/test/inspector/XJ00002/inspector/pb/property");
        mqttMessageDTO.setTopic("t/test/inspector/XJ00002/inspector/pb/will");
        mqttMessageDTO.setTopic("t/test/inspector/XJ00002/inspector/pb/events");
        mqttMessageDTO.setTopic("t/test/inspector/XJ00002/inspector/pb/services/reply");
        mqttMessageDTO.setPayload(dtoBuilder.build().toByteArray());
        return mqttMessageDTO;
    }

    private static TransportInspectorDTO.Builder getOnlineWillDto() {
        BaseRequestHeader.Builder requestHeaderBuilder = getHeaderBuilder();
        TransportInspectorDTO.Builder builder = TransportInspectorDTO.newBuilder();
        builder.setRequestHeader(requestHeaderBuilder);
        builder.setLastWill(LastWill.newBuilder().setOnlineStatus(0).build());
        return builder;
    }

    public static TransportInspectorDTO.Builder getRobotHeartbeatDto() {
        BaseRequestHeader.Builder requestHeaderBuilder = getHeaderBuilder();

        RobotHeartbeat.Builder dataBuilder = RobotHeartbeat.newBuilder();
        dataBuilder.setLocateState(1);
        dataBuilder.setEmergencyState(1);
        dataBuilder.setBattery(1);
        dataBuilder.setCharging(1);
        dataBuilder.setCheckState(1);
        dataBuilder.setX(1.0D);
        dataBuilder.setY(1.0D);
        dataBuilder.setYaw(1.0D);
        dataBuilder.setBuildId("a");
        dataBuilder.setRegionId("a");
        dataBuilder.setRegionName("a");
        dataBuilder.setMapId("a");
        dataBuilder.setMapVersion("a");
        dataBuilder.setEsimSignal("a");
        dataBuilder.setMotorEnabled(0);
        dataBuilder.setErrorCodeValue(0);
        dataBuilder.setErrorCode(ErrorCodeEnum.UNKNOWN_ERROR_CODE);
        dataBuilder.setTaskTypeValue(0);
        dataBuilder.setTaskType(com.jdx.rover.transport.inspector.RobotHeartbeat.TaskTypeEnum.TYPE_UNKNOWN);
        dataBuilder.setTimestamp(0L);
        dataBuilder.setVersion("1.0.0");
        dataBuilder.setLocateConfidence(0.0D);
        dataBuilder.setLinear(0.0D);
        dataBuilder.setAngular(0.0D);
        dataBuilder.setTakeOverState(false);
        dataBuilder.setAngular(0.0);
        TransportInspectorDTO.Builder builder = TransportInspectorDTO.newBuilder();
        builder.setRequestHeader(requestHeaderBuilder);
        builder.setRobotHeartbeat(dataBuilder);
        return builder;
    }

    private static TransportInspectorDTO.Builder getRobotTask() {
        BaseRequestHeader.Builder requestHeaderBuilder = getHeaderBuilder();

        RobotTask.Builder dataBuilder = RobotTask.newBuilder();
        dataBuilder.setTaskId("a");
        dataBuilder.setStationId("b");
        dataBuilder.setStationType("c");
        dataBuilder.setTaskType(TaskTypeEnum.TYPE_NAVIGATION);
        dataBuilder.setFromAction(FromActionEnum.FROM_LEAVE_NOW);
        dataBuilder.setToAction(ToActionEnum.TO_WAIT_LIGHT_LEAVE);
        dataBuilder.setPriority(1);
        dataBuilder.setIsChangeTask(true);

        TransportInspectorDTO.Builder builder = TransportInspectorDTO.newBuilder();
        builder.setRequestHeader(requestHeaderBuilder);
        builder.setRobotTask(dataBuilder);
        return builder;
    }


    private static TransportInspectorDTO.Builder getReportPhotoTask() {
        BaseRequestHeader.Builder requestHeaderBuilder = getHeaderBuilder();

        ReportPhotoTask.Builder dataBuilder = ReportPhotoTask.newBuilder();
        dataBuilder.setSubTaskId("a");
        dataBuilder.setPlanQty(1);

        TransportInspectorDTO.Builder builder = TransportInspectorDTO.newBuilder();
        builder.setRequestHeader(requestHeaderBuilder.setMessageType("REPORT_PHOTO_TASK"));
        builder.setReportPhotoTask(dataBuilder);
        return builder;
    }

    private static BaseRequestHeader.Builder getHeaderBuilder() {
        BaseRequestHeader.Builder requestHeaderBuilder = BaseRequestHeader.newBuilder();
        requestHeaderBuilder.setRequestId(System.currentTimeMillis() * 1000000);
        requestHeaderBuilder.setRequestTime(System.currentTimeMillis() * 1000000);
        requestHeaderBuilder.setSendName("XJ00002");
        requestHeaderBuilder.setNeedResponse(false);
        return requestHeaderBuilder;
    }
}