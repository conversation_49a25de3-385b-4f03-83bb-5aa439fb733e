package com.jdx.rover.server.mqtt.listener.ota;

import com.jdx.rover.mqtt.autoconfigure.domain.MqttMessageDTO;
import jdx.rover.ota.proto.OtaDataDto;
import jdx.rover.ota.proto.OtaHeader;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * 注
 *
 * <AUTHOR>
 * @date 2024/1/29
 */
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@Slf4j
@SpringBootTest
public class MqttOtaTaskStepListenerTest {
    /**
     * mqtt远程遥控连接消息
     */
    private final MqttOtaTaskStepListener mqttOtaTaskStepListener;

    @Test
    @Disabled
    void otaProgress() {
        MqttMessageDTO<byte[]> mqttMessageDTO = getMqttMessageDTO();
        mqttOtaTaskStepListener.otaProgress(mqttMessageDTO);
    }

    private static MqttMessageDTO<byte[]> getMqttMessageDTO() {
        OtaDataDto.OtaDataDTO.Builder dtoBuilder = getOtaDataDto();

        MqttMessageDTO<byte[]> mqttMessageDTO = new MqttMessageDTO<>();
        mqttMessageDTO.setTopic("ota/progress/JD0001/server");
        mqttMessageDTO.setPayload(dtoBuilder.build().toByteArray());
        return mqttMessageDTO;
    }

    public static OtaDataDto.OtaDataDTO.Builder getOtaDataDto() {
        OtaHeader.RequestHeader.Builder requestHeaderBuilder = OtaHeader.RequestHeader.newBuilder();
        requestHeaderBuilder.setRequestId(System.currentTimeMillis() * 1000000);
        requestHeaderBuilder.setRequestTime(System.currentTimeMillis() * 1000000);
        requestHeaderBuilder.setClientName("JD0017");
        requestHeaderBuilder.setNeedResponse(false);
        requestHeaderBuilder.setRetry(false);


        OtaDataDto.OtaTaskStep.Builder dataBuilder = OtaDataDto.OtaTaskStep.newBuilder();
        dataBuilder.setIssueTaskNumber("111");
        dataBuilder.setAppName("map");
        dataBuilder.setVehicleName("JD0017");
        dataBuilder.setVersion("201449");
        dataBuilder.setStep(10);
        dataBuilder.setEvent(OtaDataDto.OtaTaskStep.Event.DOWNLOAD);
        dataBuilder.setDownloadMethod(OtaDataDto.OtaTaskStep.DownloadMethod.FULL_SPEED);

        OtaDataDto.OtaDataDTO.Builder dtoBuilder = OtaDataDto.OtaDataDTO.newBuilder();
        dtoBuilder.setRequestHeader(requestHeaderBuilder);
        dtoBuilder.addMessageType(OtaDataDto.MessageType.OTA_TASK_STEP);
        dtoBuilder.setOtaTaskStep(dataBuilder);
        return dtoBuilder;
    }
}