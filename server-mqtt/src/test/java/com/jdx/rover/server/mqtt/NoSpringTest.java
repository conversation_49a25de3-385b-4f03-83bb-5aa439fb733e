/*
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.server.mqtt;

import com.fasterxml.jackson.databind.JsonNode;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.common.utils.request.RequestIdUtils;
import com.jdx.rover.server.api.domain.dto.mqtt.MqttOnline;
import com.jdx.rover.server.api.domain.dto.mqtt.MqttRequestDTO;
import com.jdx.rover.server.api.domain.dto.mqtt.MqttRequestHeader;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 */
public class NoSpringTest {
  @Test
  void test() throws Exception {
    String jsonStr = "{\"online\":false}";
    JsonNode jsonNode = JsonUtils.readTree(jsonStr);
    JsonNode jsonNodeOnline = jsonNode.get("online");
    System.out.println(jsonNodeOnline.asBoolean());

    MqttRequestHeader header = new MqttRequestHeader();
    header.setVehicleName("JD0001");
    header.setRequestId(RequestIdUtils.getRequestId());

    MqttRequestDTO<MqttOnline> mqttRequestDTO = new MqttRequestDTO();
    MqttOnline mqttOnline = new MqttOnline();
    mqttOnline.setOnline(true);
    mqttRequestDTO.setHeader(header);
    mqttRequestDTO.setData(mqttOnline);
    System.out.println(JsonUtils.writeValueAsString(mqttRequestDTO));
  }
}
