/*
 * Copyright (c) 2024 JD.com, Inc. All Rights Reserved.
 */
package com.jdx.rover.server.mqtt.listener.transport;

import com.jdx.rover.mqtt.autoconfigure.domain.MqttMessageDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * 设备JSON上报测试
 *
 * <AUTHOR>
 * @date 2024/12/23
 */
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@Slf4j
@SpringBootTest
class TransportDeviceJsonListenerTest {
    /**
     * 设备JSON上报
     */
    private final TransportDeviceJsonListener transportDeviceJsonListener;

    @Test
    void sendJsonProperty() {
        MqttMessageDTO<String> mqttMessageDTO = new MqttMessageDTO<>();
        mqttMessageDTO.setTopic("t/beta/pda/3210029878/pda/json/services/reply");
        mqttMessageDTO.setPayload("{\"header\":{\"messageState\":\"RUNNING\",\"requestId\":1733393848264828990,\"requestTime\":1734927587119,\"sendName\":\"3210029878\"}}\"}");
        transportDeviceJsonListener.sendJsonProperty(mqttMessageDTO);
    }
}