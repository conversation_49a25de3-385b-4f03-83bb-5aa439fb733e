package com.jdx.rover.server.mqtt.jmq;

import com.google.common.collect.Lists;
import com.jd.jmq.common.message.Message;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.server.api.domain.vo.mqtt.MqttMessageVO;
import com.jdx.rover.server.mqtt.listener.transport.TransportDevicePbListenerTest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * 注
 *
 * <AUTHOR>
 * @date 2024/10/25
 */
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@Slf4j
@SpringBootTest
class MqttSendByteConsumerTest {

    private final MqttSendByteConsumer mqttSendByteConsumer;
//    private final Producer producer;

    @Test
    void onMessage() throws Exception {
        MqttMessageVO<byte[]> mqttMessageVO = new MqttMessageVO<>();
        mqttMessageVO.setTopic("t/test/inspector/XJ00002/inspector/pb/services");
        mqttMessageVO.setMessage(TransportDevicePbListenerTest.getRobotHeartbeatDto().build().toByteArray());

        Message message = new Message();
        message.setTopic("mqtt_send_byte_test");
        message.setBody(JsonUtils.getObjectMapper().writeValueAsBytes(mqttMessageVO));
        message.setBusinessId("XJ00002");
        mqttSendByteConsumer.onMessage(Lists.newArrayList(message));
//        producer.send(message);
//        Thread.sleep(50000);
    }
}