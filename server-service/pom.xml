<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright (c) 2022 www.jd.com All rights reserved.
  ~ 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
  -->

<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>rover-server</artifactId>
        <groupId>com.jdx.rover</groupId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>server-service</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.jdx.rover</groupId>
            <artifactId>server-repository</artifactId>
            <version>${project.parent.version}</version>
        </dependency>

        <!--kafka配置 -->
        <dependency>
            <groupId>org.springframework.kafka</groupId>
            <artifactId>spring-kafka</artifactId>
        </dependency>
    </dependencies>
</project>