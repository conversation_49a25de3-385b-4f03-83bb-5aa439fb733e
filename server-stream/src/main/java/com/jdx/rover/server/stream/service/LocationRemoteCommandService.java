/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.server.stream.service;

import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.common.utils.ParameterCheckUtility;
import com.jdx.rover.common.utils.request.RequestIdUtils;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.server.api.domain.enums.SeverErrorEnum;
import com.jdx.rover.server.api.domain.vo.LocationManualVehiclePoseCommandVO;
import com.jdx.rover.server.api.domain.vo.LocationPullPointMapCommandVO;
import com.jdx.rover.server.api.domain.vo.RemoteCommandVO;
import com.jdx.rover.server.domain.constants.RedisTopicConstant;
import com.jdx.rover.server.domain.entity.Client;
import com.jdx.rover.server.domain.entity.CommandMessage;
import jdx.rover.idl.proto.RemoteServiceEntity;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RTopic;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * 定位模块指令交互
 *
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@Service
public class LocationRemoteCommandService {

  @Autowired
  private RedissonClient redissonClient;

  private static final String topicName = RedisTopicConstant.SERVER_HERMES_PREFIX;

  /**
   * 摄取激光点云快照
   * @param pullPointMapCommandVo
   * @return
   */
  public HttpResult publishGeneratePointMapCommand(LocationPullPointMapCommandVO pullPointMapCommandVo) {
    ParameterCheckUtility.checkNotNull(pullPointMapCommandVo, "pullPointMapCommandVo");
    ParameterCheckUtility.checkNotNullNorEmptyAfterTrimming(pullPointMapCommandVo.getVehicleName(), "pullPointMapCommandVo#vehicleName");
    log.info("Receive generate vehicle point cloud command {}.", JsonUtils.writeValueAsString(pullPointMapCommandVo));
    if (!checkHermesConnection(pullPointMapCommandVo.getVehicleName())) {
      return HttpResult.error(SeverErrorEnum.ERROR_HERMES_ABSENT.getCode(), SeverErrorEnum.ERROR_HERMES_ABSENT.getMessage());
    }
    Boolean uploadStatus = pullPointMapCommandVo.getNeed_upload();
    createBusinessRequest(pullPointMapCommandVo, "LocInit", uploadStatus);
    return HttpResult.success();
  }

  /**
   * 推送人工定位位姿
   * @param manualVehiclePoseCommandVo
   * @return
   */
  public HttpResult publishResetLocationPoseCommand(LocationManualVehiclePoseCommandVO manualVehiclePoseCommandVo) {
    ParameterCheckUtility.checkNotNull(manualVehiclePoseCommandVo, "manualVehiclePoseCommandVo");
    ParameterCheckUtility.checkNotNullNorEmptyAfterTrimming(manualVehiclePoseCommandVo.getVehicleName(), "manualVehiclePoseCommandVo#vehicleName");
    log.info("Receive reset location pose command {}.", JsonUtils.writeValueAsString(manualVehiclePoseCommandVo));
    if (!checkHermesConnection(manualVehiclePoseCommandVo.getVehicleName())) {
      return HttpResult.error(SeverErrorEnum.ERROR_HERMES_ABSENT.getCode(), SeverErrorEnum.ERROR_HERMES_ABSENT.getMessage());
    }
    RemoteServiceEntity.Pose.Builder poseBuilder = RemoteServiceEntity.Pose.newBuilder();
    poseBuilder.setX(manualVehiclePoseCommandVo.getX());
    poseBuilder.setY(manualVehiclePoseCommandVo.getY());
    poseBuilder.setZ(manualVehiclePoseCommandVo.getZ());
    poseBuilder.setRoll(manualVehiclePoseCommandVo.getRoll());
    poseBuilder.setYaw(manualVehiclePoseCommandVo.getYaw());
    poseBuilder.setPitch(manualVehiclePoseCommandVo.getPitch());
    createBusinessRequest(manualVehiclePoseCommandVo, "LocInit", poseBuilder.build());
    return HttpResult.success();
  }

  private boolean checkHermesConnection(String vehicleName) {
    RTopic rTopic = redissonClient.getTopic(topicName + vehicleName);
    return rTopic.countSubscribers() > 0;
  }

  private void createBusinessRequest(RemoteCommandVO commandVo, String moduleName, Object command) {
    RemoteServiceEntity.Header.Builder headerBuilder = RemoteServiceEntity.Header.newBuilder();

    headerBuilder.setModuleName(moduleName);
    headerBuilder.setTimestamp(System.currentTimeMillis());
    Long sequenceNum = System.currentTimeMillis();
    headerBuilder.setSequenceNum(sequenceNum.intValue());
    headerBuilder.setDisableToSend(Optional.ofNullable(commandVo.getNeedResponse()).orElse(true));
    headerBuilder.setProcessCallCount(Optional.ofNullable(commandVo.getRequestId()).orElse(RequestIdUtils.getRequestId()));

    RemoteServiceEntity.LocInit.Builder locInitBuilder = RemoteServiceEntity.LocInit.newBuilder();
    locInitBuilder.setId(commandVo.getRequestId().intValue());
    locInitBuilder.setHeader(headerBuilder.build());
     if (command instanceof Boolean) {
       locInitBuilder.setIsNeedUpload((Boolean)command);
    } else if (command instanceof RemoteServiceEntity.Pose) {
      locInitBuilder.setPose((RemoteServiceEntity.Pose)command);
    } else {
      return;
    }
    RemoteServiceEntity.RemoteServiceRequest.Builder remoteServiceRequestBuilder =
            RemoteServiceEntity.RemoteServiceRequest.newBuilder();
    remoteServiceRequestBuilder.setRemoteServiceType(
            RemoteServiceEntity.RemoteServiceRequest.RemoteServiceType.LOC_INIT);
    remoteServiceRequestBuilder.setLocInit(locInitBuilder.build());
    createAndPublishDispatchResponse(commandVo.getVehicleName(), remoteServiceRequestBuilder.build());
  }

  private boolean createAndPublishDispatchResponse(String vehicleName,
                                                   RemoteServiceEntity.RemoteServiceRequest remoteServiceRequest) {
    RemoteServiceEntity.DispatchResponse.Builder builder =
            RemoteServiceEntity.DispatchResponse.newBuilder();
    builder.setRemoteServiceRequest(remoteServiceRequest);
    log.info("Send message {} to Vehicle {}", remoteServiceRequest, vehicleName);
    RemoteServiceEntity.DispatchResponse response = builder.build();
    String responseString = response.toString();

    Client clientInfo = new Client();
    clientInfo.setName(vehicleName);
    CommandMessage commandMessage = new CommandMessage();
    commandMessage.setClient(clientInfo);
    commandMessage.setMessage(responseString);
    String topic = topicName + vehicleName;
    RTopic rTopic = redissonClient.getTopic(topic);
    long result = 0;
    if (rTopic.countSubscribers() > 0) {
      result = rTopic.publish(JsonUtils.writeValueAsString(commandMessage));
      log.info("Success publish location command msg to topic {}, result {}", topic, result);
    } else {
      log.error("Push hermes message error, topic {} listener {} absent", topic, rTopic.countSubscribers());
    }
    return Long.compare(result, 0) > 0;
  }
}
