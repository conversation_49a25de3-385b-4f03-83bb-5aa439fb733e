package com.jdx.rover.server.stream.provider;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.jsf.provider.AbstractProvider;
import com.jdx.rover.server.api.domain.vo.ScheduleGoNextStopCommandVO;
import com.jdx.rover.server.api.domain.vo.SchedulePlanningRouteCommandVO;
import com.jdx.rover.server.api.domain.vo.ScheduleRestRoutingCommandVO;
import com.jdx.rover.server.api.jsf.service.command.ScheduleRemoteCommandService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * @description: ScheduleRemoteCommandServiceImpl
 * @author: wangguotai
 * @create: 2024-09-26 21:43
 **/
@Service
@RequiredArgsConstructor
public class ScheduleRemoteCommandServiceImpl extends AbstractProvider<ScheduleRemoteCommandService> implements ScheduleRemoteCommandService {

    private final com.jdx.rover.server.stream.service.ScheduleRemoteCommandService remoteCommandService;

    @Override
    public HttpResult<Void> checkHermesLink(String vehicleName) {
        return remoteCommandService.checkHermesLink(vehicleName);
    }

    @Override
    public HttpResult<Void> publishGoNextStopCommand(ScheduleGoNextStopCommandVO goNextStopCommand) {
        return remoteCommandService.publishGoNextStopCommand(goNextStopCommand);
    }

    @Override
    public HttpResult<Void> publishResetRoutingCommand(ScheduleRestRoutingCommandVO resetRoutingCommand) {
        return remoteCommandService.publishResetRoutingCommand(resetRoutingCommand);
    }

    @Override
    public HttpResult<Void> publishPlanningRoutingCommand(SchedulePlanningRouteCommandVO planningRouteCommand) {
        return remoteCommandService.publishPlanningRoutingCommand(planningRouteCommand);
    }
}