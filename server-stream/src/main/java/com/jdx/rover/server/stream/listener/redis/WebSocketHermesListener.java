/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.server.stream.listener.redis;

import com.google.protobuf.TextFormat;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.server.domain.entity.CommandMessage;
import java.io.IOException;
import java.nio.ByteBuffer;
import jakarta.websocket.Session;
import jdx.rover.idl.proto.RemoteServiceEntity;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.listener.MessageListener;

/**
 * <AUTHOR>
 */
@Slf4j
public class WebSocketHermesListener implements MessageListener<String> {
  private Session session;

  public WebSocketHermesListener(Session session) {
    this.session = session;
  }

  @Override
  public void onMessage(CharSequence channel, String msg) {
    log.info("开始发送之类={}", msg);
    CommandMessage commandMessage = JsonUtils.readValue(msg, CommandMessage.class);
    RemoteServiceEntity.DispatchResponse.Builder builder = RemoteServiceEntity.DispatchResponse.newBuilder();
    try {
      TextFormat.getParser().merge(commandMessage.getMessage(), builder);
      synchronized (session) {
        this.session.getBasicRemote().sendBinary(ByteBuffer.wrap(builder.build().toByteArray()));
      }
      log.info("指令发送成功={}", msg);
    } catch (TextFormat.ParseException e) {
      log.error("指令解析失败!msg={}", msg);
    } catch (IOException e) {
      log.error("指令发送失败!msg={}", msg);
    } catch (Exception e) {
      log.error("指令发送失败!msg={}", msg);
    }
  }
}

