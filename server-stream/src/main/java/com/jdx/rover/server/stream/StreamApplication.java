/*
 * Copyright (c) 2021 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.server.stream;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.ImportResource;

/**
 * 入口函数类
 *
 * <AUTHOR>
 * @date  2022-01-01
 */
@ComponentScan(basePackages = {"com.jdx.rover.server.repository", "com.jdx.rover.server.stream"})
@EnableDiscoveryClient
@SpringBootApplication
@ImportResource(locations = {"classpath:jmq.xml"})
public class StreamApplication {
  public static void main(String[] args) {
    SpringApplication.run(StreamApplication.class, args);
  }
}
