package com.jdx.rover.server.stream.provider;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.jsf.provider.AbstractProvider;
import com.jdx.rover.server.api.domain.vo.LocationManualVehiclePoseCommandVO;
import com.jdx.rover.server.api.domain.vo.LocationPullPointMapCommandVO;
import com.jdx.rover.server.api.jsf.service.command.LocationRemoteCommandService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * @description: LocationRemoteCommandServiceImpl
 * @author: wangguotai
 * @create: 2024-09-26 21:17
 **/
@Service
@RequiredArgsConstructor
public class LocationRemoteCommandServiceImpl extends AbstractProvider<LocationRemoteCommandService> implements LocationRemoteCommandService {

    private final com.jdx.rover.server.stream.service.LocationRemoteCommandService locationRemoteCommandService;

    @Override
    public HttpResult<Void> publishGeneratePointMapCommand(LocationPullPointMapCommandVO pullPointMapCommandVo) {
        return locationRemoteCommandService.publishGeneratePointMapCommand(pullPointMapCommandVo);
    }

    @Override
    public HttpResult<Void> publishResetLocationPoseCommand(LocationManualVehiclePoseCommandVO manualVehiclePoseCommandVo) {
        return locationRemoteCommandService.publishResetLocationPoseCommand(manualVehiclePoseCommandVo);
    }
}