/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.server.stream.websocket;

import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.common.utils.exception.AppException;
import com.jdx.rover.server.api.domain.dto.power.ServerPowerManagerDTO;
import com.jdx.rover.server.api.domain.enums.power.PowerManagerAckEnum;
import com.jdx.rover.server.api.domain.vo.PowerManagerCommandVO;
import com.jdx.rover.server.domain.bo.websocket.PowerManagerClientBO;
import com.jdx.rover.server.domain.constants.RedisTopicConstant;
import com.jdx.rover.server.repository.jmq.JmqProducerManager;
import com.jdx.rover.server.repository.redis.RedissonUtils;
import com.jdx.rover.server.repository.shadow.ShadowEventEnum;
import com.jdx.rover.server.repository.shadow.ShadowEventUtils;
import com.jdx.rover.server.stream.config.ProducerTopicProperties;
import com.jdx.rover.server.stream.config.ServletAwareConfigurator;
import com.jdx.rover.server.stream.listener.redis.WebSocketPowerManagerListener;
import com.jdx.rover.server.stream.utils.WebsocketUtils;
import com.jdx.rover.shadow.boot.starter.util.EventUtil;
import com.jdx.rover.shadow.boot.starter.vo.EventRecordVO;
import jdx.rover.power.manager.dto.PowerManagerDto;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RTopic;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import jakarta.websocket.CloseReason;
import jakarta.websocket.OnClose;
import jakarta.websocket.OnError;
import jakarta.websocket.OnMessage;
import jakarta.websocket.OnOpen;
import jakarta.websocket.Session;
import jakarta.websocket.server.PathParam;
import jakarta.websocket.server.ServerEndpoint;
import java.io.IOException;
import java.io.InputStream;
import java.nio.ByteBuffer;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 电量管理指令上行下行ws链路
 *
 * <AUTHOR>
 * @version 1.0
 * @date  2022-01-01
 */
@ServerEndpoint(value = "/stream/ws/power/manager/{vehicleName}/{moduleName}", configurator = ServletAwareConfigurator.class)
@Component
@Slf4j
public class PowerManagerWebSocket {
  /**
   * 记录当前在线连接数
   */
  private static AtomicInteger onlineCount = new AtomicInteger(0);

  /**
   * Websocket客户端
   */
  private PowerManagerClientBO client = new PowerManagerClientBO();

  /**
   * WebSocket是线程安全的,有用户连接时才会创建一个新的端点实例,所以spring依赖注入会失败,改成静态变量就好了
   */
  private static KafkaTemplate<String, String> kafkaTemplate;

  /**
   * jmq消息发送producer
   */
  private static JmqProducerManager jmqProducerManager;
  /**
   * 生产主题配置类
   */
  private static ProducerTopicProperties producerTopicProperties;

  @Autowired
  public void setKafkaTemplate(KafkaTemplate<String, String> kafkaTemplate) {
    PowerManagerWebSocket.kafkaTemplate = kafkaTemplate;
  }

  @Autowired
  public void setJmqProducerManager(JmqProducerManager jmqProducerManager) {
    PowerManagerWebSocket.jmqProducerManager = jmqProducerManager;
  }

  @Autowired
  public void setProducerTopicProperties(ProducerTopicProperties producerTopicProperties) {
    PowerManagerWebSocket.producerTopicProperties = producerTopicProperties;
  }


  /**
   * 接收webSocket消息
   */
  @OnMessage
  public void onMessage(InputStream message, Session session, @PathParam("vehicleName") String vehicleName, @PathParam("moduleName") String moduleName) throws Exception {
    Assert.hasLength(vehicleName, "客户端名称不能为空!");
    Assert.hasLength(moduleName, vehicleName + "的模块名称不能为空!");

    PowerManagerDto.PowerManagerDTO powerManagerDTO;
    try {
      powerManagerDTO = PowerManagerDto.PowerManagerDTO.parseFrom(message);
      replayHeartbeat(session, powerManagerDTO);
      handleAck(powerManagerDTO);
      log.info("收到电源管理消息,车辆={},连接={},内容={}", vehicleName, session.getId(), powerManagerDTO);
    } catch (IllegalStateException e) {
      throw new AppException("Failed to get legal state from webSocket.", e);
    } catch (IOException e) {
      throw new AppException("Failed to read all bytes.", e);
    }
  }

  /**
   * 回复心跳消息
   *
   * @param session
   * @param powerManagerDTO
   * @throws IOException
   */
  private void replayHeartbeat(Session session, PowerManagerDto.PowerManagerDTO powerManagerDTO) throws IOException {
    if (powerManagerDTO == null || powerManagerDTO.getRequestHeader() == null
        || !Objects.equals(powerManagerDTO.getRequestHeader().getMessageType(), PowerManagerDto.MessageType.HEARTBEAT)) {
      return;
    }
    PowerManagerDto.PowerManagerDTO.Builder builder = PowerManagerDto.PowerManagerDTO.newBuilder();
    PowerManagerDto.ResponseHeader.Builder responseHeaderBuilder = PowerManagerDto.ResponseHeader.newBuilder();
    responseHeaderBuilder.setMessageType(PowerManagerDto.MessageType.HEARTBEAT);
    responseHeaderBuilder.setSequenceNum(powerManagerDTO.getRequestHeader().getSequenceNum());
    responseHeaderBuilder.setVehicleId(powerManagerDTO.getRequestHeader().getVehicleId());
    responseHeaderBuilder.setClientTimestamp(powerManagerDTO.getRequestHeader().getTimestamp());
    responseHeaderBuilder.setServerTimestamp(System.currentTimeMillis() * 1000000L);
    builder.setResponseHeader(responseHeaderBuilder);
    ByteBuffer data = ByteBuffer.wrap(builder.build().toByteArray());
    synchronized (session) {
      session.getBasicRemote().sendBinary(data);
    }
  }

  /**
   * 处理确认消息
   *
   * @param powerManagerDTO
   */
  private void handleAck(PowerManagerDto.PowerManagerDTO powerManagerDTO) {
    if (powerManagerDTO == null || powerManagerDTO.getResponseHeader() == null
        || !Objects.equals(powerManagerDTO.getResponseHeader().getMessageType(), PowerManagerDto.MessageType.POWER_MANAGER_COMMAND)) {
      return;
    }
    if (powerManagerDTO.getPowerManagerResponse() == null || powerManagerDTO.getPowerManagerResponse().getAck() == null) {
      return;
    }
    String vehicleName = powerManagerDTO.getResponseHeader().getVehicleId();
    String ack = powerManagerDTO.getPowerManagerResponse().getAck().name();
    PowerManagerAckEnum powerManagerAckEnum = PowerManagerAckEnum.of(ack);
    if (powerManagerAckEnum == null) {
      log.error("车辆{}电源管理返回{}解析失败!", vehicleName, ack);
      return;
    }
    log.info("车辆{}收到电源管理{}消息!", vehicleName, powerManagerAckEnum.getTitle());

    Assert.notNull(powerManagerDTO.getResponseHeader().getVehicleId(), "车辆名称为空");
    ServerPowerManagerDTO serverPowerManagerDTO = new ServerPowerManagerDTO();
    PowerManagerDto.PowerManagerResponse response = powerManagerDTO.getPowerManagerResponse();
    serverPowerManagerDTO.setVehicleName(vehicleName);
    serverPowerManagerDTO.setModuleName(response.getBody().name());
    serverPowerManagerDTO.setAction(response.getAction().name());
    serverPowerManagerDTO.setAck(response.getAck().name());
    serverPowerManagerDTO.setAckInfo(response.getAckInfo());
    serverPowerManagerDTO.setRequestId(String.valueOf(powerManagerDTO.getResponseHeader().getServerTimestamp()));
    String jsonMsg = JsonUtils.writeValueAsString(serverPowerManagerDTO);
    jmqProducerManager.sendOrderedAndLog(producerTopicProperties.getServerPowerManager(), jsonMsg, vehicleName);
  }

  /**
   * 打开连接
   *
   * @param session The session.
   */
  @OnOpen
  public void onOpen(Session session, @PathParam("vehicleName") String vehicleName, @PathParam("moduleName") String moduleName) {
    // 在线数加1
    int onlineCountInt = onlineCount.incrementAndGet();

    Assert.hasLength(vehicleName, "客户端名称不能为空!");
    Assert.hasLength(moduleName, vehicleName + "的模块名称不能为空!");

    this.client.setSessionId(session.getId());
    this.client.setVehicleName(vehicleName);
    this.client.setModuleName(moduleName);

    String topicName = getRTopicName(vehicleName);
    RTopic rTopic = RedissonUtils.getRTopic(topicName);
    WebSocketPowerManagerListener webSocketPowerManagerListener = new WebSocketPowerManagerListener(session);
    int listenerId = rTopic.addListener(PowerManagerCommandVO.class, webSocketPowerManagerListener);
    this.client.setEventListenerId(listenerId);
    log.info("webSocket add topic={},listener count={},subscribers={},client={}", topicName, rTopic.countListeners(), rTopic.countSubscribers(), this.client);

    String clientIp = WebsocketUtils.getWebSocketIp(session);
    log.info("电源管理新增连接: {}, 连接IP：{}, 当前在线数: {}.", this.client, clientIp, onlineCountInt);

    EventRecordVO eventRecordVO = ShadowEventUtils.buildConnectEvent(this.client.getVehicleName()
            , ShadowEventEnum.SERVER_VEHICLE_POWER_WS_STATE, "成功");
    EventUtil.sendEvent(eventRecordVO);
  }

  /**
   * 关闭连接
   *
   * @param session The session.
   */
  @OnClose
  public void onClose(Session session, CloseReason closeReason) {
    // 在线数减1
    int onlineCountInt = onlineCount.decrementAndGet();
    if (this.client.getEventListenerId() != null) {
      String topicName = getRTopicName(this.client.getVehicleName());
      RTopic rTopic = RedissonUtils.getRTopic(topicName);
      rTopic.removeListener(this.client.getEventListenerId());
      log.info("webSocket remove topic={},listener count={},subscribers={},client={}", topicName, rTopic.countListeners(), rTopic.countSubscribers(), this.client);
    }

    String clientIp = WebsocketUtils.getWebSocketIp(session);
    log.info("电源管理关闭连接: {}, 连接IP：{},当前在线数: {}, 关闭原因: {}.", this.client, clientIp, onlineCountInt, closeReason.getCloseCode().getCode());

    EventRecordVO eventRecordVO = ShadowEventUtils.buildConnectEvent(this.client.getVehicleName()
            , ShadowEventEnum.SERVER_VEHICLE_POWER_WS_STATE, "断开");
    EventUtil.sendEvent(eventRecordVO);
  }

  /**
   * 连接异常
   *
   * @param throwable The exception.
   */
  @OnError
  public void onError(Session session, Throwable throwable) {
    String clientIp = WebsocketUtils.getWebSocketIp(session);
    log.error("电源管理异常连接: {}, 连接IP：{}.", this.client, clientIp, throwable);
    try {
      if (session != null) {
        session.close();
        log.info("电源管理onError关闭: {}", this.client);
      }
    } catch (Exception e) {
      log.error("电源管理onError关闭出错: {}, 连接IP：{}.", this.client, clientIp, e);
    }
  }

  /**
   * 获取订阅主题名称
   *
   * @return
   */
  private String getRTopicName(String vehicleName) {
    String topicName = RedisTopicConstant.SERVER_POWER_MANAGER_PREFIX + vehicleName;
    return topicName;
  }
}