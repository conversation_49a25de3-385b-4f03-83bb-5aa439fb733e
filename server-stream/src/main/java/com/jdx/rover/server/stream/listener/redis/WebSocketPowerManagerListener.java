/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.server.stream.listener.redis;

import com.google.protobuf.TextFormat;
import com.jdx.rover.common.utils.ParameterCheckUtility;
import com.jdx.rover.server.api.domain.vo.PowerManagerCommandVO;
import jdx.rover.power.manager.dto.PowerManagerDto;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.listener.MessageListener;

import jakarta.websocket.Session;
import java.io.IOException;
import java.nio.ByteBuffer;

/**
 * <AUTHOR>
 */
@Slf4j
public class WebSocketPowerManagerListener implements MessageListener<PowerManagerCommandVO> {
  private Session session;

  public WebSocketPowerManagerListener(Session session) {
    this.session = session;
  }

  @Override
  public void onMessage(CharSequence channel, PowerManagerCommandVO powerManagerCommandVO) {
    log.info("开始电源管理指令={}", powerManagerCommandVO);
    try {
      PowerManagerDto.PowerManagerDTO powerManagerDTO = buildPowerManagerDTO(powerManagerCommandVO);
      ByteBuffer byteBuffer = ByteBuffer.wrap(powerManagerDTO.toByteArray());
      synchronized (session) {
        this.session.getBasicRemote().sendBinary(byteBuffer);
      }
      log.info("电源管理发送成功={}", powerManagerCommandVO);
    } catch (TextFormat.ParseException e) {
      log.error("电源管理解析失败!参数={}", powerManagerCommandVO, e);
    } catch (IOException e) {
      log.error("电源管理发送失败!参数={}", powerManagerCommandVO, e);
    } catch (Exception e) {
      log.error("电源管理发送失败!参数={}", powerManagerCommandVO, e);
    }
  }


  public PowerManagerDto.PowerManagerDTO buildPowerManagerDTO(PowerManagerCommandVO powerManagerCommandVO) {
    ParameterCheckUtility.checkNotNull(powerManagerCommandVO, "powerManagerCommandVO");
    log.info("Receive power manager command {}.", powerManagerCommandVO);

    PowerManagerDto.RequestHeader.Builder requestHeaderBuilder = PowerManagerDto.RequestHeader.newBuilder();
    // TODO 设置序列号
    requestHeaderBuilder.setSequenceNum(1);
    requestHeaderBuilder.setTimestamp(powerManagerCommandVO.getRequestTime());
    requestHeaderBuilder.setMessageType(PowerManagerDto.MessageType.POWER_MANAGER_COMMAND);
    requestHeaderBuilder.setVehicleId(powerManagerCommandVO.getVehicleName());

    PowerManagerDto.PowerManagerRequest.Builder requestBuilder = PowerManagerDto.PowerManagerRequest.newBuilder();
    requestBuilder.setAction(PowerManagerDto.Action.valueOf(powerManagerCommandVO.getAction()));
    requestBuilder.setBody(PowerManagerDto.Body.valueOf(powerManagerCommandVO.getModuleName()));
    requestBuilder.setTime(0);

    PowerManagerDto.PowerManagerDTO.Builder builder = PowerManagerDto.PowerManagerDTO.newBuilder();
    builder.setRequestHeader(requestHeaderBuilder);
    builder.setPowerManagerRequest(requestBuilder);

    PowerManagerDto.PowerManagerDTO powerManagerDTO = builder.build();

    return powerManagerDTO;
  }
}

