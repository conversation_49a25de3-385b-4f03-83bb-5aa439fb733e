/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.server.stream.controller;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.server.api.domain.vo.LocationManualVehiclePoseCommandVO;
import com.jdx.rover.server.api.domain.vo.LocationPullPointMapCommandVO;
import com.jdx.rover.server.api.service.command.LocationRemoteCommandApi;
import com.jdx.rover.server.stream.service.LocationRemoteCommandService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 车端定位模块交互指令
 * </p>
 *
 *
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@RequestMapping(value = "/server/stream/location_command")
public class LocationRemoteCommandController implements LocationRemoteCommandApi {

  @Autowired
  private LocationRemoteCommandService locationRemoteCommandService;

  /**
   * 通知车端上报激光点云地图帧
   * @param pullPointMapCommandVo
   * @return
   */
  @PostMapping("/get_point_map")
  @Override
  public HttpResult publishGeneratePointMapCommand(LocationPullPointMapCommandVO pullPointMapCommandVo) {
    return locationRemoteCommandService.publishGeneratePointMapCommand(pullPointMapCommandVo);
  }

  /**
   * 下发车端地图人工定位位姿
   * @param manualVehiclePoseCommandVo
   * @return
   */
  @PostMapping("/reset_location_pose")
  @Override
  public HttpResult publishResetLocationPoseCommand(LocationManualVehiclePoseCommandVO manualVehiclePoseCommandVo) {
    return locationRemoteCommandService.publishResetLocationPoseCommand(manualVehiclePoseCommandVo);
  }
}
