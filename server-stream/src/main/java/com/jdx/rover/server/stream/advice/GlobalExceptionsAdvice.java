/*
 * Copyright (c) 2021-2022 JD.com, Inc. All Rights Reserved.
 */
package com.jdx.rover.server.stream.advice;

import com.jdx.rover.common.utils.enums.HttpCodeEnum;
import com.jdx.rover.common.utils.exception.AppException;
import com.jdx.rover.common.utils.result.HttpResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.validation.BindException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.util.stream.Collectors;

/**
 * 全局异常处理
 *
 * <AUTHOR>
 */
@RestControllerAdvice
@Slf4j
public class GlobalExceptionsAdvice {
  /**
   * 处理Get请求中 使用@Valid 验证路径中请求实体校验失败后抛出的异常
   *
   * @param e
   * @return
   */
  @ExceptionHandler(BindException.class)
  public Object bindExceptionHandler(BindException e) {
    String message = e.getBindingResult().getAllErrors().stream().map(DefaultMessageSourceResolvable::getDefaultMessage).collect(Collectors.joining());
    HttpResult result = buildErrorResult(HttpCodeEnum.BAD_REQUEST.getValue(), message, e);
    return result;
  }

  /**
   * 处理非法参数错误
   *
   * @param e
   * @return
   */
  @ExceptionHandler(IllegalArgumentException.class)
  public Object bindIllegalArgumentException(IllegalArgumentException e) {
    HttpResult result = buildErrorResult(HttpCodeEnum.BAD_REQUEST.getValue(), e.getMessage(), e);
    return result;
  }

  /**
   * 处理请求参数格式错误 @RequestParam上validate失败后抛出的异常是javax.validation.ConstraintViolationException
   *
   * @param e
   * @return
   */
  @ExceptionHandler(ConstraintViolationException.class)
  public Object constraintViolationExceptionHandler(ConstraintViolationException e) {
    String message = e.getConstraintViolations().stream().map(ConstraintViolation::getMessage).collect(Collectors.joining());
    HttpResult result = buildErrorResult(HttpCodeEnum.BAD_REQUEST.getValue(), message, e);
    return result;
  }

  /**
   * 处理请求参数格式错误 @RequestBody上validate失败后抛出的异常是MethodArgumentNotValidException异常。
   *
   * @param e
   * @return
   */
  @ExceptionHandler(MethodArgumentNotValidException.class)
  public Object methodArgumentNotValidExceptionHandler(MethodArgumentNotValidException e) {
    String message = e.getBindingResult().getAllErrors().stream().map(DefaultMessageSourceResolvable::getDefaultMessage).collect(Collectors.joining());
    HttpResult result = buildErrorResult(HttpCodeEnum.BAD_REQUEST.getValue(), message, e);
    return result;
  }

  /**
   * 功能描述：自定义异常处理
   *
   * @param e
   * @return 返回处理结果
   */
  @ExceptionHandler(value = AppException.class)
  public Object appExceptionHandler(AppException e) {
    String code = e.getCode();
    if (StringUtils.isBlank(code)) {
      code = HttpCodeEnum.INNER_SERVER_ERROR.getValue();
    }
    HttpResult result = buildErrorResult(code, e.getMessage(), e);
    return result;
  }

  /**
   * 功能描述：全局异常处理
   *
   * @param e
   * @return 返回处理结果
   * @throws Exception
   */
  @ExceptionHandler(value = Exception.class)
  public Object errorHandler(Exception e) {
//        可以通过匹配类型处理不同错误 if (e instanceof MethodArgumentNotValidException)
    HttpResult result = buildErrorResult(HttpCodeEnum.INNER_SERVER_ERROR.getValue(), "系统异常", e);
    return result;
  }

  private HttpResult buildErrorResult(String code, String message, Exception e) {
    log.error("错误日志为：{}", e.getMessage(), e);
    HttpResult result = HttpResult.error(code, message);
    log.info("返回错误结果={}", result);
    return result;
  }
}
