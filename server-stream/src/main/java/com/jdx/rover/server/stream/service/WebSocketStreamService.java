/***************************************************************************
 // *
 // * Copyright (c) 2020 JD.com, Inc. All Rights Reserved
 // *
 // **************************************************************************/
//
package com.jdx.rover.server.stream.service;

import com.jdx.rover.common.utils.ParameterCheckUtility;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.server.domain.entity.Client;
import com.jdx.rover.server.domain.entity.CommandMessage;
import com.jdx.rover.server.repository.redis.client.ClientRepository;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import jakarta.websocket.Session;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

/**
 * 内容上行下行ws链路
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Slf4j
public class WebSocketStreamService {
  /**
   * 活跃的正在连接webSocket session
   */
  private static ConcurrentMap<String, Session> sessionMap = new ConcurrentHashMap<>();

  /**
   * 活跃的webSocket session
   */
  private static ConcurrentMap<String, Client> clientMap = new ConcurrentHashMap<>();

  @Autowired
  private RedisTemplate<String, Object> redisTemplate;

  public static ConcurrentMap<String, Session> getSessionMap() {
    return sessionMap;
  }

  public static ConcurrentMap<String, Client> getClientMap() {
    return clientMap;
  }

  /**
   * http通知车辆客户端,不成功通过redis广播通知其它服务器
   */
  public boolean notifyClientByHttp(CommandMessage commandMessage) {
    return notifyClient(commandMessage, true);
  }

  /**
   * redis广播通知车辆客户端,不再二次广播,防止循环调用
   *
   * @param commandMessage The command message.
   * @throws IllegalArgumentException if the argument does not meet requirement.
   */
  public boolean notifyClientByRedis(CommandMessage commandMessage) {
    return notifyClient(commandMessage, false);
  }

  /**
   * 通知车辆客户端
   *
   * @param commandMessage The command message.
   * @throws IllegalArgumentException if the argument does not meet requirement.
   */
  public boolean notifyClient(CommandMessage commandMessage, boolean isBroadcast) {
    checkParameter(commandMessage);
    Session session = getValidSession(commandMessage);
    if (session == null) {
      // session不在当前服务器,通知其他服务器
      if (isBroadcast) {
        String topicNameFormat = "server_stream_test_%s";
        String topicName = String.format(topicNameFormat, commandMessage.getClient().getName());
        redisTemplate.convertAndSend(topicName, JsonUtils.writeValueAsString(commandMessage));
      }
      return false;
    }
    session.getAsyncRemote().sendText(commandMessage.getMessage());
    return true;
  }

  /**
   * 校验参数
   *
   * @param commandMessage
   */
  private static void checkParameter(CommandMessage commandMessage) {
    ParameterCheckUtility.checkNotNull(commandMessage, "commandMessage");
    Client client = commandMessage.getClient();
    String message = commandMessage.getMessage();
    ParameterCheckUtility.checkNotNull(client, "commandMessage#client");
    ParameterCheckUtility.checkNotNull(client.getHostName(), "commandMessage#client#HostName");
    ParameterCheckUtility.checkNotNull(client.getName(), "commandMessage#client#Name");
//    ParameterCheckUtility.checkNotNull(client.getSessionId(), "commandMessage#client#sessionId");
    ParameterCheckUtility.checkNotNull(message, "commandMessage#message");
    ParameterCheckUtility.checkNotNull(sessionMap, "commandMessage#ACTIVE_SESSION");
  }

  /**
   * 获取有效的sessionId,无效的返回null
   *
   * @param commandMessage
   * @return
   */
  private static Session getValidSession(CommandMessage commandMessage) {
    Client client = commandMessage.getClient();
    String key = ClientRepository.createEndpointKey(client);
    Client expectClient = clientMap.get(key);
    if (isDifferentSession(client, expectClient)) {
      // redis广播的时候,不属于自己机器的客户端会不一致
      log.debug("Client not equals exceptClient vehicle {} session id {}.",
          client.getName(), client.getSessionId());
      return null;
    }

    Session session = sessionMap.get(expectClient.getSessionId());
    if (session == null) {
      return null;
    }
    return session;
  }

  /**
   * 是否为不同的session
   *
   * @param client
   * @param expectClient
   * @return
   */
  private static boolean isDifferentSession(Client client, Client expectClient) {
    if (client == null || expectClient == null) {
      return true;
    }
    if (!Objects.equals(client.getName(), expectClient.getName())) {
      return true;
    }
    if (!Objects.equals(client.getType(), expectClient.getType())) {
      return true;
    }
    if (!Objects.equals(client.getHostName(), expectClient.getHostName())) {
      return true;
    }
//    if (!Objects.equals(client.getSessionId(), expectClient.getSessionId())) {
//      return true;
//    }
    return false;
  }
}