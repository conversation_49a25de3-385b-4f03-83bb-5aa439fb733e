/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.server.stream.controller;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.server.api.domain.vo.PassNoSignalIntersectionCommandVO;
import com.jdx.rover.server.api.domain.vo.RemoteCommandVO;
import com.jdx.rover.server.api.domain.vo.RemoteControlCommandVO;
import com.jdx.rover.server.api.domain.vo.RemoteResetAbnormalCommandVO;
import com.jdx.rover.server.api.domain.vo.RemoteTrafficLightCommandVO;
import com.jdx.rover.server.api.domain.vo.SwitchLightCommandVO;
import com.jdx.rover.server.stream.service.RemoteCommandService;
import jdx.rover.idl.proto.RemoteServiceEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * This is api interface for mini monitor vehicle command.
 * </p>
 *
 * <p>
 * <strong>Thread Safety: </strong> This class is mutable and not thread safe. But it will be used
 * as entity so it'll not cause any thread safe problem.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@RequestMapping(value = "/server/stream/command")
public class RemoteCommandController {

  @Autowired
  private RemoteCommandService remoteCommandService;

  /**
   * <p>
   * Post emergency stop command.
   * </p>
   */
  @PostMapping("/emergency_stop")
  public HttpResult publishEmergencyStopCommand(@RequestBody RemoteCommandVO remoteCommandVo) {
    return remoteCommandService.publishEmergencyStopCommand(remoteCommandVo);
  }

  /**
   * <p>
   * Post as arrived command.
   * </p>
   */
  @PostMapping("/as_arrived")
  public HttpResult publishAsArrivedCommand(@RequestBody RemoteCommandVO remoteCommandVo) {
    return remoteCommandService.publishAsArrivedCommand(remoteCommandVo);
  }

  /**
   * <p>
   * Post recovery command.
   * </p>
   */
  @PostMapping("/recovery")
  public HttpResult publishRecoveryCommand(@RequestBody RemoteCommandVO remoteCommandVo) {
    return remoteCommandService.publishRecoveryCommand(remoteCommandVo);
  }

  /**
   * <p>
   * Post restart command.
   * </p>
   */
  @PostMapping("/restart")
  public HttpResult publishRestartCommand(@RequestBody RemoteCommandVO remoteCommandVo) {
    return remoteCommandService.publishRestartCommand(remoteCommandVo);
  }

  /**
   * <p>
   * Post remote control command.
   * </p>
   */
  @PostMapping("/remote_control")
  public HttpResult publishRemoteControlCommand(@RequestBody RemoteControlCommandVO remoteControlCommandVo) {
    return remoteCommandService.publishRemoteControlCommand(remoteControlCommandVo);
  }

  /**
   * <p>
   * Publish reset abnormal command.
   * </p>
   */
  @PostMapping("/reset_abnormal")
  public HttpResult publishResetAbnormalCommand(@RequestBody RemoteResetAbnormalCommandVO remoteResetAbnormalCommandVo) {
    return remoteCommandService.publishResetAbnormalCommand(remoteResetAbnormalCommandVo);
  }

  /**
   * <p>
   * Publish control traffic light command.
   * </p>
   */
  @PostMapping("/control_traffic_light")
  public HttpResult publishControlTrafficLightCommand(@RequestBody RemoteTrafficLightCommandVO remoteTrafficLightCommandVo) {
    return remoteCommandService.publishControlTrafficLightCommand(remoteTrafficLightCommandVo);
  }

  /**
   * <p>
   * Publish control lamp command.
   * </p>
   */
  @PostMapping("/control_lamp")
  public HttpResult publishControlLampCommand(@RequestBody RemoteCommandVO remoteCommandVO) {
    return remoteCommandService.publishLampControlCommand(remoteCommandVO);
  }

  /**
   * <p>
   * Publish relieves button stop command.
   * </p>
   */
  @PostMapping("/relieve_button_stop")
  public HttpResult publishRelieveButtonStopCommand(@RequestBody RemoteCommandVO remoteCommandVO) {
    return remoteCommandService.publishRelievesButtonStopCommand(remoteCommandVO);
  }

  /**
   * 通过无信号路口
   */
  @PostMapping("/pass_no_signal_intersection")
  public HttpResult passNoSignalIntersection(@RequestBody PassNoSignalIntersectionCommandVO passNoSignalIntersectionCommandVO) {
    return remoteCommandService.passNoSignalIntersection(passNoSignalIntersectionCommandVO);
  }

  /**
   * 切换地图有图模式
   */
  @PostMapping("/runHaveMap")
  public HttpResult runHaveMap(@RequestBody RemoteCommandVO remoteCommandVO) {
    return remoteCommandService.runMapState(RemoteServiceEntity.ManualIntervention.OperationType.RUN_HAVE_MAP.name(), remoteCommandVO);
  }

  /**
   * 切换地图无图模式
   */
  @PostMapping("/runNoMap")
  public HttpResult runNoMap(@RequestBody RemoteCommandVO remoteCommandVO) {
    return remoteCommandService.runMapState(RemoteServiceEntity.ManualIntervention.OperationType.RUN_NO_MAP.name(), remoteCommandVO);
  }

  /**
   * 通过无信号路口
   */
  @PostMapping("/switch_light")
  public HttpResult switchLight(@RequestBody SwitchLightCommandVO switchLightCommandVO) {
    return remoteCommandService.switchLight(switchLightCommandVO);
  }
}
