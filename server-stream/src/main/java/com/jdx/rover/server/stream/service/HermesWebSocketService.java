/***************************************************************************
 // *
 // * Copyright (c) 2020 JD.com, Inc. All Rights Reserved
 // *
 // **************************************************************************/
//
package com.jdx.rover.server.stream.service;

import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.common.utils.ParameterCheckUtility;
import com.jdx.rover.server.api.domain.dto.hermes.HermesChassisCommandModuleDelayInfoDTO;
import com.jdx.rover.server.api.domain.dto.hermes.HermesVehicleControlCommandDelayInfoDTO;
import com.jdx.rover.server.api.domain.dto.mqtt.MqttResponseDTO;
import com.jdx.rover.server.api.domain.dto.mqtt.MqttResponseHeader;
import com.jdx.rover.server.api.domain.enums.ChassisCommandStateEnum;
import com.jdx.rover.server.repository.jmq.JmqProducerManager;
import com.jdx.rover.server.stream.config.ProducerTopicProperties;
import jdx.rover.idl.proto.RemoteServiceEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * hermes 服务
 *
 * <AUTHOR>
 * @version 1.0
 * @date  2022-01-01
 */
@Service
@Slf4j
public class HermesWebSocketService {

  @Autowired
  private KafkaTemplate<String, String> kafkaTemplate;

  /**
   * jmq消息发送producer
   */
  @Autowired
  private JmqProducerManager jmqProducerManager;

  /**
   * 生产主题配置类
   */
  @Autowired
  private ProducerTopicProperties producerTopicProperties;

  /**
   * </p>
   * This method help to save remoteControl response command.
   * </p>
   *
   * @param chassisCommandResponse The command response message.
   * @throws IllegalArgumentException if the argument does not meet requirement.
   */
  @Async
  public void saveResponse(RemoteServiceEntity.ChassisCommandResponse chassisCommandResponse, String vehicleName) {
    ParameterCheckUtility.checkNotNull(chassisCommandResponse, "chassisCommandResponse");
    ParameterCheckUtility.checkNotNull(chassisCommandResponse.getModuleDelayInfoList(),
            "chassisCommandResponse#moduleDelayInfo");
    ParameterCheckUtility.checkNotNull(chassisCommandResponse.getSequenceNum(), "chassisCommandResponse#seqNum");
    ParameterCheckUtility.checkNotNull(vehicleName, "vehicleName");

    List<HermesChassisCommandModuleDelayInfoDTO> delayInfoList = new ArrayList<>();
    List<RemoteServiceEntity.ModuleDelayInfo> moduleDelayInfos = chassisCommandResponse.getModuleDelayInfoList();
    for (RemoteServiceEntity.ModuleDelayInfo delayInfo : moduleDelayInfos) {
      HermesChassisCommandModuleDelayInfoDTO delayInfoDto = new HermesChassisCommandModuleDelayInfoDTO();
      delayInfoDto.setModuleName(delayInfo.getModuleName());
      delayInfoDto.setReceiveTime(delayInfo.getReceiveTime());
      delayInfoDto.setTransitTime(delayInfo.getTransitTime());
      delayInfoList.add(delayInfoDto);
    }
    HermesChassisCommandModuleDelayInfoDTO delayInfoDto = new HermesChassisCommandModuleDelayInfoDTO();
    delayInfoDto.setModuleName("server-upload");
    delayInfoDto.setReceiveTime(System.currentTimeMillis());
    delayInfoDto.setTransitTime(System.currentTimeMillis());
    delayInfoList.add(delayInfoDto);
    HermesVehicleControlCommandDelayInfoDTO responseDto = new HermesVehicleControlCommandDelayInfoDTO();
    responseDto.setDelayInfo(delayInfoList);
    responseDto.setId(chassisCommandResponse.getSequenceNum());
    responseDto.setHeading(chassisCommandResponse.getHeading());
    responseDto.setSpeed(chassisCommandResponse.getVelocity());
    if (!Objects.isNull(chassisCommandResponse.getFrontWheelAngle())) {
      responseDto.setFrontWheelAngle(BigDecimal.valueOf(Math.toDegrees(
              chassisCommandResponse.getFrontWheelAngle())).setScale(1, RoundingMode.HALF_UP).doubleValue());
    }
    responseDto.setVehicleName(vehicleName);
    ChassisCommandStateEnum commandState = ChassisCommandStateEnum.OK;
    if (chassisCommandResponse.getCommandState() == RemoteServiceEntity.ChassisCommandResponse.ChassisCommandState.TIME_EXCEPTION) {
      commandState = ChassisCommandStateEnum.TIME_EXCEPTION;
    }
    responseDto.setState(commandState);
    jmqProducerManager.sendOrderedAndLog(producerTopicProperties.getServerRemoteCommandDelay(), JsonUtils.writeValueAsString(responseDto), vehicleName);
  }

  /**
   * 发送车端响应
   */
  @Async
  public void sendHermesReply(RemoteServiceEntity.Header header, String vehicleName) {
    if (header == null || !header.getDisableToSend()) {
      return;
    }

    MqttResponseHeader mqttResponseHeader = new MqttResponseHeader();
    mqttResponseHeader.setMessageType(header.getModuleName());
    mqttResponseHeader.setRequestId(header.getProcessCallCount());
    mqttResponseHeader.setVehicleName(vehicleName);
    mqttResponseHeader.setResponseTime(header.getTimestamp());
    mqttResponseHeader.setMessageState("RUNNING");
    MqttResponseDTO<String> mqttResponseDTO = new MqttResponseDTO<>();
    mqttResponseDTO.setHeader(mqttResponseHeader);
    String jsonStr = JsonUtils.writeValueAsString(mqttResponseDTO);
    jmqProducerManager.sendOrderedAndLog(producerTopicProperties.getServerHermesReply(), jsonStr, vehicleName);
    log.info("Hermes响应数据={}", jsonStr);
  }
}