package com.jdx.rover.server.stream.websocket;

import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.common.utils.ParameterCheckUtility;
import com.jdx.rover.common.utils.request.RequestIdUtils;
import com.jdx.rover.server.api.domain.dto.webterminal.WebTerminalResponseDTO;
import com.jdx.rover.server.api.domain.dto.webterminal.WebTerminalResponseHeader;
import com.jdx.rover.server.api.domain.enums.webterminal.WebTerminalMessageTypeEnum;
import com.jdx.rover.server.api.domain.enums.ws.WsMessageStateEnum;
import com.jdx.rover.server.api.domain.vo.webterminal.WebTerminalRequestHeader;
import com.jdx.rover.server.api.domain.vo.webterminal.WebTerminalRequestVO;
import com.jdx.rover.server.domain.bo.websocket.TerminalSocketClientBO;
import com.jdx.rover.server.domain.constants.RedisTopicConstant;
import com.jdx.rover.server.domain.enums.RedisCacheEnum;
import com.jdx.rover.server.repository.redis.RedissonUtils;
import com.jdx.rover.server.repository.shadow.ShadowEventEnum;
import com.jdx.rover.server.repository.shadow.ShadowEventUtils;
import com.jdx.rover.server.stream.config.ServletAwareConfigurator;
import com.jdx.rover.server.stream.listener.redis.WebSocketTerminalListener;
import com.jdx.rover.server.stream.service.TerminalWebSocketService;
import com.jdx.rover.server.stream.utils.WebsocketUtils;
import com.jdx.rover.shadow.boot.starter.util.EventUtil;
import com.jdx.rover.shadow.boot.starter.vo.EventRecordVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RTopic;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import jakarta.websocket.CloseReason;
import jakarta.websocket.OnClose;
import jakarta.websocket.OnError;
import jakarta.websocket.OnMessage;
import jakarta.websocket.OnOpen;
import jakarta.websocket.Session;
import jakarta.websocket.server.ServerEndpoint;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * web Terminal 上行下行ws链路
 *
 * <AUTHOR>
 * @version 1.0
 */
@ServerEndpoint(value = "/stream/ws/web/terminal", configurator = ServletAwareConfigurator.class)
@Component
@Slf4j
public class TerminalWebSocket {
    /**
     * 记录当前在线连接数
     */
    private static AtomicInteger onlineCount = new AtomicInteger(0);

    /**
     * terminal socket客户端
     */
    private TerminalSocketClientBO client = new TerminalSocketClientBO();

    /**
     * WebSocket是线程安全的,有用户连接时才会创建一个新的端点实例,所以spring依赖注入会失败,改成静态变量就好了
     */
    private static TerminalWebSocketService terminalWebSocketService;

    @Autowired
    public void setThreeDimensionalMapService(TerminalWebSocketService terminalWebSocketService) {
        TerminalWebSocket.terminalWebSocketService = terminalWebSocketService;
    }

    /**
     * 打开连接
     *
     * @param session The session.
     */
    @OnOpen
    public void onOpen(Session session) {
        // 在线数加1
        int onlineCountInt = onlineCount.incrementAndGet();
        String vehicleName = session.getRequestParameterMap().get("vehicleName").get(0).toString();
        String terminalName = session.getRequestParameterMap().get("terminalName").get(0).toString();
        if (!StringUtils.isBlank(vehicleName) && !StringUtils.isBlank(terminalName)) {
            this.client.setVehicleName(vehicleName);
            this.client.setSessionId(RequestIdUtils.getRequestId() + "");
            this.client.setTerminalName(terminalName);
            String topicName = String.format(RedisTopicConstant.SERVER_TERMINAL_PREFIX, vehicleName, terminalName);
            RTopic rTopic = RedissonUtils.getRTopic(topicName);
            WebSocketTerminalListener webSocketTerminalListener = new WebSocketTerminalListener(session);
            int listenerId = rTopic.addListener(String.class, webSocketTerminalListener);
            this.client.setEventListenerId(listenerId);
            // 发布监控消息
            terminalWebSocketService.sendCommandToMonitor(setWebTerminalResponseVO(WebTerminalMessageTypeEnum.OPEN_SESSION.getValue()));
            RedissonUtils.setMapObject(RedisCacheEnum.WEB_TERMINAL_CONNECT_HASH.getValue()
                    , getObjectKey(this.client.getVehicleName(), this.client.getTerminalName()), this.client);
            log.info("web terminal Socket add topic={},listener count={},subscribers={},client={}", topicName, rTopic.countListeners(), rTopic.countSubscribers(), this.client);
        }
        String clientIp = WebsocketUtils.getWebSocketIp(session);
        log.info("web terminal Socket新增连接: {}, 连接IP：{}, 当前在线数: {}.", this.client, clientIp, onlineCountInt);

        EventRecordVO eventRecordVO = ShadowEventUtils.buildCommonEvent(vehicleName
                , ShadowEventEnum.SERVER_VEHICLE_WEB_TERMINAL_WS_STATE);
        eventRecordVO.setBodyArray(new String[]{vehicleName, terminalName, "成功"});
        EventUtil.sendEvent(eventRecordVO);
    }

    /**
     * 设置数据响应头信息
     *
     * @return
     */
    private WebTerminalRequestVO setWebTerminalResponseVO(String messageType) {
        WebTerminalRequestVO webTerminalRequestVO = new WebTerminalRequestVO();
        WebTerminalRequestHeader webTerminalRequestHeader = new WebTerminalRequestHeader();
        webTerminalRequestHeader.setTerminalName(this.client.getTerminalName());
        webTerminalRequestHeader.setRequestId(0L);
        webTerminalRequestHeader.setRequestTime(System.currentTimeMillis());
        webTerminalRequestHeader.setVehicleName(this.client.getVehicleName());
        webTerminalRequestHeader.setNeedResponse(false);
        webTerminalRequestVO.setHeader(webTerminalRequestHeader);
        webTerminalRequestVO.setMessageType(messageType);
        return webTerminalRequestVO;
    }

    /**
     * 设置数据响应头信息
     *
     * @return
     */
    private WebTerminalResponseDTO setWebTerminalResponseDTO(String messageType, String messageState) {
        WebTerminalResponseHeader webTerminalResponseHeader = new WebTerminalResponseHeader();
        webTerminalResponseHeader.setResponseTime(System.currentTimeMillis());
        webTerminalResponseHeader.setTerminalName(this.client.getTerminalName());
        webTerminalResponseHeader.setVehicleName(this.client.getVehicleName());
        webTerminalResponseHeader.setRequestId(0L);
        webTerminalResponseHeader.setMessageState(messageState);
        WebTerminalResponseDTO webTerminalResponseDTO = new WebTerminalResponseDTO();
        webTerminalResponseDTO.setHeader(webTerminalResponseHeader);
        webTerminalResponseDTO.setMessageType(messageType);
        return webTerminalResponseDTO;
    }

    /**
     * 接收webSocket消息
     */
    @OnMessage
    public void onMessage(String message, Session session) {
        log.info("web terminal Socket Receive message from session {},{},{}", session.getId(), this.client.getSessionId(), message);
        ParameterCheckUtility.checkNotNull(message, "message");
        WebTerminalRequestVO webTerminalRequestVO = JsonUtils.readValue(message, WebTerminalRequestVO.class);

        if (StringUtils.equals(webTerminalRequestVO.getMessageType(), WebTerminalMessageTypeEnum.HEARTBEAT.getValue())) {
            WebTerminalResponseDTO webTerminalResponseDTO = setWebTerminalResponseDTO(WebTerminalMessageTypeEnum.HEARTBEAT.getValue(), WsMessageStateEnum.RECEIVED.getValue());
            sendWebsocketData(session, webTerminalResponseDTO);
            return;
        }
        // 指令数据异步通知监控服务
        if (StringUtils.equals(webTerminalRequestVO.getMessageType(), WebTerminalMessageTypeEnum.COMMAND_DATA.getValue())) {
            terminalWebSocketService.sendCommandToMonitor(webTerminalRequestVO);
        } else if (StringUtils.equalsAny(webTerminalRequestVO.getMessageType()
                , WebTerminalMessageTypeEnum.SERVER_TO_VEHICLE_FILE.getValue()
                , WebTerminalMessageTypeEnum.VEHICLE_TO_SERVER_FILE.getValue())) {
            terminalWebSocketService.sendCommandResponseToMonitor(webTerminalRequestVO.getHeader().getVehicleName(), message);
        }
    }

    /**
     * 发送数据
     *
     * @param session
     * @param data
     */
    private void sendWebsocketData(Session session, Object data) {
        String jsonStr = JsonUtils.writeValueAsString(data);
        try {
            synchronized (session) {
                session.getBasicRemote().sendText(jsonStr);
            }
        } catch (Exception e) {
            log.error("web terminal ws send result exception", e);
        }
    }

    /**
     * 关闭连接
     *
     * @param session The session.
     */
    @OnClose
    public void onClose(Session session, CloseReason closeReason) {
        // 在线数减1
        int onlineCountInt = onlineCount.decrementAndGet();
        // 发布监控消息
        TerminalSocketClientBO clientRedis = RedissonUtils.getMapObject(RedisCacheEnum.WEB_TERMINAL_CONNECT_HASH.getValue()
                , getObjectKey(this.client.getVehicleName(), this.client.getTerminalName()));
        if (clientRedis == null || clientRedis.getSessionId().compareTo(this.client.getSessionId()) <= 0) {
            terminalWebSocketService.sendCommandToMonitor(setWebTerminalResponseVO(WebTerminalMessageTypeEnum.CLOSE_SESSION.getValue()));
        }
        if (this.client.getEventListenerId() != null) {
            String topicName = String.format(RedisTopicConstant.SERVER_TERMINAL_PREFIX, this.client.getVehicleName(), this.client.getTerminalName());
            RTopic rTopic = RedissonUtils.getRTopic(topicName);
            rTopic.removeListener(this.client.getEventListenerId());
            log.info("web terminal Socket remove topic={},listener count={},subscribers={},client={}", topicName, rTopic.countListeners(), rTopic.countSubscribers(), this.client);
        }
        String clientIp = WebsocketUtils.getWebSocketIp(session);
        log.info("web terminal Socket 关闭连接: {}, 连接IP：{},当前在线数: {}, 关闭原因: {}.", this.client, clientIp, onlineCountInt, closeReason.getCloseCode().getCode());

        EventRecordVO eventRecordVO = ShadowEventUtils.buildCommonEvent(this.client.getVehicleName()
                , ShadowEventEnum.SERVER_VEHICLE_WEB_TERMINAL_WS_STATE);
        eventRecordVO.setBodyArray(new String[]{this.client.getVehicleName(), this.client.getTerminalName(), "断开"});
        EventUtil.sendEvent(eventRecordVO);
    }

    /**
     * 连接异常
     *
     * @param throwable The exception.
     */
    @OnError
    public void onError(Session session, Throwable throwable) {
        String clientIp = WebsocketUtils.getWebSocketIp(session);
        log.error("web terminal Socket异常连接: {}, 连接IP：{}.", this.client, clientIp, throwable);
        try {
            if (session != null) {
                session.close();
                log.info("web terminal Socket onError 关闭: {}", this.client);
            }
        } catch (Exception e) {
            log.error("web terminal Socket onError 关闭出错: {}, 连接IP：{}.", this.client, clientIp, e);
        }
    }


    /**
     * 获取redis key
     *
     * @param vehicleName
     * @param terminalName
     * @return
     */
    private static String getObjectKey(String vehicleName, String terminalName) {
        String key = vehicleName + "::" + terminalName;
        return key;
    }
}
