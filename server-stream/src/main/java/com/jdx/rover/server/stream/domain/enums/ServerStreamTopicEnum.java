/*
 * Copyright (c) 2021 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.server.stream.domain.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * <p>
 * kafka主题枚举
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum ServerStreamTopicEnum {
  SERVER_STREAM_HERMES_ORIGIN("server_stream_hermes_origin", "hermes原始信息"),
  SERVER_STREAM_TEST("server_stream_test", "stream测试信息"),
  ;

  /**
   * <p>
   * 值
   * </p>
   */
  private String value;

  /**
   * <p>
   * 显示标题
   * </p>
   */
  private String title;
}
