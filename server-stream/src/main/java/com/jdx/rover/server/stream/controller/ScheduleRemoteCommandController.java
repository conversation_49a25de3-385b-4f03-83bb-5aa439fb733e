/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.server.stream.controller;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.server.api.domain.vo.ScheduleGoNextStopCommandVO;
import com.jdx.rover.server.api.domain.vo.SchedulePlanningRouteCommandVO;
import com.jdx.rover.server.api.domain.vo.ScheduleRestRoutingCommandVO;
import com.jdx.rover.server.api.service.command.ScheduleRemoteCommandApi;
import com.jdx.rover.server.stream.service.ScheduleRemoteCommandService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * This is api interface for schedule vehicle command.
 * </p>
 *
 * <p>
 * <strong>Thread Safety: </strong> This class is mutable and not thread safe.
 * But it will be used as entity so it'll not cause any thread safe problem.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@RequestMapping(value = "/server/stream/schedule_command")
public class ScheduleRemoteCommandController implements ScheduleRemoteCommandApi {

  @Autowired
  private ScheduleRemoteCommandService remoteCommandService;

  @Override
  @GetMapping("/hermes_check")
  public HttpResult checkHermesLink(@RequestParam(value = "vehicleName") String vehicleName) {
    return remoteCommandService.checkHermesLink(vehicleName);
  }

  @Override
  @PostMapping("/go_next")
  public HttpResult<Void> publishGoNextStopCommand(@RequestBody  ScheduleGoNextStopCommandVO goNextStopCommand) {
    return remoteCommandService.publishGoNextStopCommand(goNextStopCommand);
  }

  @Override
  @PostMapping("/reset_route")
  public HttpResult publishResetRoutingCommand(@RequestBody ScheduleRestRoutingCommandVO resetRoutingCommand) {
    return remoteCommandService.publishResetRoutingCommand(resetRoutingCommand);
  }

  @Override
  @PostMapping("/plan_route")
  public HttpResult publishPlanningRoutingCommand(@RequestBody SchedulePlanningRouteCommandVO planningRouteCommand) {
    return remoteCommandService.publishPlanningRoutingCommand(planningRouteCommand);
  }


}
