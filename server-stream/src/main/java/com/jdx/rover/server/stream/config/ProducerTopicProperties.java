/*
 * Copyright (c) 2024 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.server.stream.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 生产主题配置类
 * 读取application.yml中的jmq.topic.producer属性文件
 *
 * <AUTHOR>
 * @date 2024/12/16
 */
@Component
@ConfigurationProperties(prefix = "jmq.topic.producer")
@Data
public class ProducerTopicProperties {
    /**
     * 电源管理
     */
    private String serverPowerManager;
    /**
     * 远程工具上传
     */
    private String serverWebTerminalCommandUp;
    /**
     * Hermes回复
     */
    private String serverHermesReply;
    /**
     * 指令回复
     */
    private String serverRemoteCommandDelay;
}