/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.server.stream.config;

import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.codec.JsonJacksonCodec;
import org.redisson.config.Config;
import org.redisson.spring.starter.RedissonProperties;
import org.springframework.boot.autoconfigure.data.redis.RedisProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.ReflectionUtils;

import java.io.IOException;
import java.lang.reflect.Method;

/**
 * 配置 redisson
 * 序列化方式为JsonJacksonCodec
 * SubscriptionsPerConnection为5000
 * subscriptionConnectionPoolSize为5000
 *
 * <AUTHOR>
 */
@Configuration
public class RedissionConfig {
  private static final String REDIS_PROTOCOL_PREFIX = "redis://";
  private static final String REDISS_PROTOCOL_PREFIX = "rediss://";

  @Bean(destroyMethod = "shutdown")
  public RedissonClient redisson(RedissonProperties redissonProperties, RedisProperties redisProperties) throws IOException {
    Method timeoutMethod = ReflectionUtils.findMethod(RedisProperties.class, "getTimeout");
    Object timeoutValue = ReflectionUtils.invokeMethod(timeoutMethod, redisProperties);
    int timeout;
    if (null == timeoutValue) {
      timeout = 10000;
    } else if (!(timeoutValue instanceof Integer)) {
      Method millisMethod = ReflectionUtils.findMethod(timeoutValue.getClass(), "toMillis");
      timeout = ((Long) ReflectionUtils.invokeMethod(millisMethod, timeoutValue)).intValue();
    } else {
      timeout = (Integer) timeoutValue;
    }

    Config config = new Config();
    String prefix = REDIS_PROTOCOL_PREFIX;
    Method method = ReflectionUtils.findMethod(RedisProperties.class, "isSsl");
    if (method != null && (Boolean) ReflectionUtils.invokeMethod(method, redisProperties)) {
      prefix = REDISS_PROTOCOL_PREFIX;
    }
    config.useSingleServer()
        .setAddress(prefix + redisProperties.getHost() + ":" + redisProperties.getPort())
        .setConnectTimeout(timeout)
        .setTimeout(10000)
        .setDatabase(redisProperties.getDatabase())
        .setPassword(redisProperties.getPassword())
        .setSubscriptionsPerConnection(5000)
        .setSubscriptionConnectionPoolSize(5000)
        .setConnectionPoolSize(500);
    config.setNettyThreads(256);
    config.setCodec(new JsonJacksonCodec());
    return Redisson.create(config);
  }
}
