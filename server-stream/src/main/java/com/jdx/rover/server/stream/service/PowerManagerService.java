/***************************************************************************
 // *
 // * Copyright (c) 2020 JD.com, Inc. All Rights Reserved
 // *
 // **************************************************************************/
//
package com.jdx.rover.server.stream.service;

import com.jdx.rover.common.utils.ParameterCheckUtility;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.server.api.domain.enums.SeverErrorEnum;
import com.jdx.rover.server.api.domain.vo.PowerManagerCommandVO;
import com.jdx.rover.server.domain.constants.RedisTopicConstant;
import com.jdx.rover.server.repository.redis.RedissonUtils;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RTopic;
import org.springframework.stereotype.Service;

/**
 * hermes 服务
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Slf4j
public class PowerManagerService {
  public HttpResult<Void> publishPowerManagerCommand(PowerManagerCommandVO powerManagerCommandVO) {
    ParameterCheckUtility.checkNotNull(powerManagerCommandVO, "powerManagerCommandVO");
    ParameterCheckUtility.checkNotNull(powerManagerCommandVO.getVehicleName(), "powerManagerCommandVO#vehicleName");
    ParameterCheckUtility.checkNotNull(powerManagerCommandVO.getModuleName(), "powerManagerCommandVO#moduleName");
    ParameterCheckUtility.checkNotNull(powerManagerCommandVO.getAction(), "powerManagerCommandVO#action");
    log.info("Receive power manager command {}.", powerManagerCommandVO);

    String topicName = RedisTopicConstant.SERVER_POWER_MANAGER_PREFIX + powerManagerCommandVO.getVehicleName();
    RTopic rTopic = RedissonUtils.getRTopic(topicName);
    long resultCnt = rTopic.publish(powerManagerCommandVO);
    if (resultCnt == 0) {
      return HttpResult.error(SeverErrorEnum.ERROR_POWER_MANAGER_ABSENT.getCode(), SeverErrorEnum.ERROR_POWER_MANAGER_ABSENT.getMessage());
    }
    return HttpResult.success();
  }
}