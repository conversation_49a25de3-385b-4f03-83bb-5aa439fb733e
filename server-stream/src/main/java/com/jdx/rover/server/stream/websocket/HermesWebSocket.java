/***************************************************************************
 // *
 // * Copyright (c) 2020 JD.com, Inc. All Rights Reserved
 // *
 // **************************************************************************/
//
package com.jdx.rover.server.stream.websocket;

import com.jdx.rover.common.constant.NumberConstant;
import com.jdx.rover.common.utils.ParameterCheckUtility;
import com.jdx.rover.common.utils.exception.AppException;
import com.jdx.rover.server.common.utils.proto.ProtoUtils;
import com.jdx.rover.server.domain.bo.websocket.WebsocketClientBO;
import com.jdx.rover.server.domain.constants.RedisTopicConstant;
import com.jdx.rover.server.domain.entity.Client;
import com.jdx.rover.server.domain.enums.ClientTypeEnum;
import com.jdx.rover.server.repository.redis.RedissonUtils;
import com.jdx.rover.server.repository.shadow.ShadowEventEnum;
import com.jdx.rover.server.repository.shadow.ShadowEventUtils;
import com.jdx.rover.server.stream.config.ServletAwareConfigurator;
import com.jdx.rover.server.stream.listener.redis.WebSocketHermesListener;
import com.jdx.rover.server.stream.service.HermesWebSocketService;
import com.jdx.rover.server.stream.service.WebSocketStreamService;
import com.jdx.rover.server.stream.utils.WebsocketUtils;
import com.jdx.rover.shadow.boot.starter.util.EventUtil;
import com.jdx.rover.shadow.boot.starter.vo.EventRecordVO;
import jdx.rover.idl.proto.RemoteServiceEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RTopic;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import jakarta.websocket.CloseReason;
import jakarta.websocket.OnClose;
import jakarta.websocket.OnError;
import jakarta.websocket.OnMessage;
import jakarta.websocket.OnOpen;
import jakarta.websocket.Session;
import jakarta.websocket.server.ServerEndpoint;
import java.io.IOException;
import java.io.InputStream;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.nio.ByteBuffer;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Hermes指令上行下行ws链路
 *
 * <AUTHOR>
 * @version 1.0
 */
@ServerEndpoint(value = "/stream/ws/hermes", configurator = ServletAwareConfigurator.class)
@Component
@Slf4j
public class HermesWebSocket {
  /**
   * 记录当前在线连接数
   */
  private static AtomicInteger onlineCount = new AtomicInteger(0);

  /**
   * Websocket客户端
   */
  private WebsocketClientBO client = new WebsocketClientBO();

  /**
   * WebSocket是线程安全的,有用户连接时才会创建一个新的端点实例,所以spring依赖注入会失败,改成静态变量就好了
   */
  private static HermesWebSocketService hermesWebSocketService;

  @Autowired
  public void setThreeDimensionalMapService(HermesWebSocketService hermesWebSocketService) {
    HermesWebSocket.hermesWebSocketService = hermesWebSocketService;
  }

  /**
   * 接收webSocket消息
   */
  @OnMessage
  public void onMessage(InputStream message, Session session) throws Exception {
    log.info("HermesWebSocket Receive message from session {}", session.getId());
    ParameterCheckUtility.checkNotNull(message, "message");
    ParameterCheckUtility.checkNotNull(session, "session");

    RemoteServiceEntity.WebsocketClientAuthRequest websocketClientAuthRequest = null;
    RemoteServiceEntity.AuthInfo loginInfo = null;
    try {
      websocketClientAuthRequest = RemoteServiceEntity.WebsocketClientAuthRequest.parseFrom(message);
      loginInfo = websocketClientAuthRequest.getAuthInfo();
      if (websocketClientAuthRequest.hasChassisCommandResponse()) {
        log.info("Receive chassis command response {}", websocketClientAuthRequest.getChassisCommandResponse());
        hermesWebSocketService.saveResponse(websocketClientAuthRequest.getChassisCommandResponse(),
                loginInfo.getVehicleId());
      }

      log.info("收到hermes消息={}", ProtoUtils.protoToJson(websocketClientAuthRequest));
      if (websocketClientAuthRequest.getHeader() != null && websocketClientAuthRequest.getHeader().getDisableToSend()) {
        String vehicleName = loginInfo.getVehicleId();
        if (StringUtils.isBlank(vehicleName)) {
          vehicleName = this.client.getVehicleName();
        }
        hermesWebSocketService.sendHermesReply(websocketClientAuthRequest.getHeader(), vehicleName);
        // 指令下发,Hermes响应的不需要回复心跳,否则车端会当做心跳不通过
        return;
      }
    } catch (IllegalStateException e) {
      throw new AppException("Failed to get legal state from webSocket.", e);
    } catch (IOException e) {
      throw new AppException("Failed to read all bytes.", e);
    }
    RemoteServiceEntity.AuthResponse.Builder subBuilder = RemoteServiceEntity.AuthResponse.newBuilder();
    subBuilder.setAuthInfo(loginInfo);
    subBuilder.setLogInState(RemoteServiceEntity.AuthResponse.LogInType.LOG_IN_OK);
    RemoteServiceEntity.HeartbeatHeader.Builder heartbeatHeader = RemoteServiceEntity.HeartbeatHeader.newBuilder();
    heartbeatHeader.setVehicleId(websocketClientAuthRequest.getHeartbeatHeader().getVehicleId());
    heartbeatHeader.setSequenceNum(websocketClientAuthRequest.getHeartbeatHeader().getSequenceNum());
    heartbeatHeader.setClientTimestamp(websocketClientAuthRequest.getHeartbeatHeader().getClientTimestamp());
    heartbeatHeader.setServerTimestamp(System.currentTimeMillis() * NumberConstant.MILLION);
    subBuilder.setHeartbeatHeader(heartbeatHeader);

    RemoteServiceEntity.DispatchResponse.Builder builder = RemoteServiceEntity.DispatchResponse.newBuilder();
    builder.setAuthResponse(subBuilder);

    ByteBuffer data = ByteBuffer.wrap(builder.build().toByteArray());
    synchronized (session) {
      session.getBasicRemote().sendBinary(data);
    }
  }

  /**
   * 打开连接
   *
   * @param session The session.
   */
  @OnOpen
  public void onOpen(Session session) {
    // 在线数加1
    int onlineCountInt = onlineCount.incrementAndGet();
    if (session.getUserProperties() != null && session.getUserProperties().get("vehicleName") != null) {
      String vehicleName = session.getUserProperties().get("vehicleName").toString();
      this.client.setVehicleName(vehicleName);
      this.client.setSessionId(session.getId());
      // JD100测试套件(非实车),每30s左右重连一次,忽略不处理
      if (!StringUtils.equals(vehicleName, "JD100")) {
        String topicName = RedisTopicConstant.SERVER_HERMES_PREFIX + vehicleName;
        RTopic rTopic = RedissonUtils.getRTopic(topicName);
        WebSocketHermesListener webSocketHermesListener = new WebSocketHermesListener(session);
        int listenerId = rTopic.addListener(String.class, webSocketHermesListener);
        this.client.setEventListenerId(listenerId);
        log.info("webSocket add topic={},listener count={},subscribers={},client={}", topicName, rTopic.countListeners(), rTopic.countSubscribers(), this.client);
      }

      Client client = new Client();
      client.setName(vehicleName);
      client.setType(ClientTypeEnum.ROVER.getType());
      client.setSessionId(session.getId());
      try {
        client.setHostName(InetAddress.getLocalHost().getHostName());
      } catch (UnknownHostException e) {
        log.error("get host name error!vehicleName={}", vehicleName, e);
      }
      WebSocketStreamService.getClientMap().put(vehicleName, client);
    }
    String clientIp = WebsocketUtils.getWebSocketIp(session);
    log.info("Hermes新增连接: {}, 连接IP：{}, 当前在线数: {}.", this.client, clientIp, onlineCountInt);
    WebSocketStreamService.getSessionMap().put(session.getId(), session);

    EventRecordVO eventRecordVO = ShadowEventUtils.buildConnectEvent(this.client.getVehicleName()
            , ShadowEventEnum.SERVER_VEHICLE_HERMES_WS_STATE, "成功");
    EventUtil.sendEvent(eventRecordVO);
  }

  /**
   * 关闭连接
   *
   * @param session The session.
   */
  @OnClose
  public void onClose(Session session, CloseReason closeReason) {
    // 在线数减1
    int onlineCountInt = onlineCount.decrementAndGet();
    if (this.client.getEventListenerId() != null) {
      String topicName = RedisTopicConstant.SERVER_HERMES_PREFIX + this.client.getVehicleName();
      RTopic rTopic = RedissonUtils.getRTopic(topicName);
      rTopic.removeListener(this.client.getEventListenerId());
      log.info("webSocket remove topic={},listener count={},subscribers={},client={}", topicName, rTopic.countListeners(), rTopic.countSubscribers(), this.client);
    }

    String clientIp = WebsocketUtils.getWebSocketIp(session);
    log.info("Hermes关闭连接: {}, 连接IP：{},当前在线数: {}, 关闭原因: {}.", this.client, clientIp, onlineCountInt, closeReason.getCloseCode().getCode());
    WebSocketStreamService.getClientMap().values().removeIf(value -> Objects.equals(session.getId(), value.getSessionId()));
    WebSocketStreamService.getSessionMap().remove(session.getId());

    EventRecordVO eventRecordVO = ShadowEventUtils.buildConnectEvent(this.client.getVehicleName()
            , ShadowEventEnum.SERVER_VEHICLE_HERMES_WS_STATE, "断开");
    EventUtil.sendEvent(eventRecordVO);
  }

  /**
   * 连接异常
   *
   * @param throwable The exception.
   */
  @OnError
  public void onError(Session session, Throwable throwable) {
    String clientIp = WebsocketUtils.getWebSocketIp(session);
    log.error("Hermes异常连接: {}, 连接IP：{}.", this.client, clientIp, throwable);
    try {
      if (session != null) {
        session.close();
        log.info("Hermes onError 关闭: {}", this.client);
      }
    } catch (Exception e) {
      log.error("Hermes onError 关闭出错: {}, 连接IP：{}.", this.client, clientIp, e);
    }
  }
}