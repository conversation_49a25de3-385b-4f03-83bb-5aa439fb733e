/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.server.stream.service;

import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.common.utils.ParameterCheckUtility;
import com.jdx.rover.common.utils.request.RequestIdUtils;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.server.api.domain.enums.SeverErrorEnum;
import com.jdx.rover.server.api.domain.vo.RemoteCommandVO;
import com.jdx.rover.server.api.domain.vo.ScheduleGoNextStopCommandVO;
import com.jdx.rover.server.api.domain.vo.SchedulePlanningRouteCommandVO;
import com.jdx.rover.server.api.domain.vo.SchedulePlanningSpotCommandVO;
import com.jdx.rover.server.api.domain.vo.SchedulePlanningStopCommandVO;
import com.jdx.rover.server.api.domain.vo.ScheduleRestRoutingCommandVO;
import com.jdx.rover.server.domain.constants.RedisTopicConstant;
import com.jdx.rover.server.domain.entity.Client;
import com.jdx.rover.server.domain.entity.CommandMessage;
import jdx.rover.idl.proto.RemoteServiceEntity;
import jdx.rover.idl.proto.RemoteServiceEntity.ChassisCommand;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RTopic;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * <p>
 * This is api interface for schedule vehicle command.
 * </p>
 *
 * <p>
 * <strong>Thread Safety: </strong> This class is mutable and not thread safe. But it will be used
 * as entity so it'll not cause any thread safe problem.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@Service
public class ScheduleRemoteCommandService {

  @Autowired
  private RedissonClient redissonClient;

  private static final String topicName = RedisTopicConstant.SERVER_HERMES_PREFIX;

  /**
   * <p>
   * Check hermes link.
   * </p>
   *
   */
  public HttpResult checkHermesLink(String vehicleName) {
    ParameterCheckUtility.checkNotNullNorEmptyAfterTrimming(vehicleName, "vehicleName");
    log.info("Receive check hermes link command {}.", vehicleName);
    if (!checkHermesConnection(vehicleName)) {
      return HttpResult.error(SeverErrorEnum.ERROR_HERMES_ABSENT.getCode(), SeverErrorEnum.ERROR_HERMES_ABSENT.getMessage());
    }
    return HttpResult.success();
  }

  /**
   * <p>
   * Post go next stop command.
   * </p>
   * 
   */
  public HttpResult publishGoNextStopCommand(ScheduleGoNextStopCommandVO goNextStopCommand) {
    ParameterCheckUtility.checkNotNull(goNextStopCommand, "goNextStopCommand");
    ParameterCheckUtility.checkNotNullNorEmptyAfterTrimming(goNextStopCommand.getVehicleName(), "goNextStopCommand#vehicleName");
    log.info("Receive go next stop command {}.", goNextStopCommand);
    if (!checkHermesConnection(goNextStopCommand.getVehicleName())) {
      return HttpResult.error(SeverErrorEnum.ERROR_HERMES_ABSENT.getCode(), SeverErrorEnum.ERROR_HERMES_ABSENT.getMessage());
    }
    RemoteServiceEntity.DriveToNextCommand.Builder driveToNextBuilder =
        RemoteServiceEntity.DriveToNextCommand.newBuilder();
        driveToNextBuilder.setIndex(goNextStopCommand.getNextIndex());
        driveToNextBuilder.setRoutingId(goNextStopCommand.getRoutingId());
    RemoteServiceEntity.MotionCommand.Builder motionCommandBuilder =
        RemoteServiceEntity.MotionCommand.newBuilder();
    motionCommandBuilder.setDriveToNext(driveToNextBuilder.build());
    boolean result = createBusinessRequest(goNextStopCommand, "scheduleNavigation", motionCommandBuilder.build());
    if (!result) {
      return HttpResult.error(SeverErrorEnum.ERROR_HERMES_ABSENT.getCode(), SeverErrorEnum.ERROR_HERMES_ABSENT.getMessage());
    }
    return HttpResult.success();
  }

  /**
   * <p>
   * Post reset route command.
   * </p>
   * 
   */
  public HttpResult publishResetRoutingCommand(ScheduleRestRoutingCommandVO resetRoutingCommand) {
    ParameterCheckUtility.checkNotNull(resetRoutingCommand, "resetRoutingCommand");
    ParameterCheckUtility.checkNotNull(resetRoutingCommand.getRoutingId(),"resetRoutingCommand#routingId" );
    ParameterCheckUtility.checkNotNullNorEmptyAfterTrimming(resetRoutingCommand.getVehicleName(), "resetRoutingCommand#vehicleName");
    log.info("Receive reset routing command {}.", resetRoutingCommand);
    if (!checkHermesConnection(resetRoutingCommand.getVehicleName())) {
      return HttpResult.error(SeverErrorEnum.ERROR_HERMES_ABSENT.getCode(), SeverErrorEnum.ERROR_HERMES_ABSENT.getMessage());
    }
    RemoteServiceEntity.ResetRouteCommand.Builder resetRouteCommandBuilder = RemoteServiceEntity.ResetRouteCommand
            .newBuilder();
    resetRouteCommandBuilder.setRoutingId(resetRoutingCommand.getRoutingId());
    RemoteServiceEntity.MotionCommand.Builder motionCommandBuilder = RemoteServiceEntity.MotionCommand.newBuilder();
    motionCommandBuilder.setResetRoute(resetRouteCommandBuilder.build());
    boolean result  = createBusinessRequest(resetRoutingCommand, "scheduleNavigation", motionCommandBuilder.build());
    if (!result) {
      return HttpResult.error(SeverErrorEnum.ERROR_HERMES_ABSENT.getCode(), SeverErrorEnum.ERROR_HERMES_ABSENT.getMessage());
    }
    return HttpResult.success();
  }
  
  /**
   * <p>
   * Post planning route command.
   * </p>
   * 
   */
  public HttpResult publishPlanningRoutingCommand(SchedulePlanningRouteCommandVO planningRouteCommand) {
    ParameterCheckUtility.checkNotNull(planningRouteCommand, "planningRouteCommand");
    ParameterCheckUtility.checkNotNull(planningRouteCommand.getRoutingPath(),"resetRoutingCommand#routingPath" );
    ParameterCheckUtility.checkNotNullNorEmptyAfterTrimming(planningRouteCommand.getVehicleName(), "planningRouteCommand#vehicleName");
    log.info("Receive planning routing command Msg : {}", planningRouteCommand);
    if (!checkHermesConnection(planningRouteCommand.getVehicleName())) {
      return HttpResult.error(SeverErrorEnum.ERROR_HERMES_ABSENT.getCode(), SeverErrorEnum.ERROR_HERMES_ABSENT.getMessage());
    }
    RemoteServiceEntity.NaviRoutingCommand.Builder naviRoutingCommandBuilder =
            RemoteServiceEntity.NaviRoutingCommand.newBuilder();
    for (SchedulePlanningStopCommandVO stop : planningRouteCommand.getRoutingPath()) {
      RemoteServiceEntity.StopInfo.Builder stopInfoBuilder =
              RemoteServiceEntity.StopInfo.newBuilder();
      stopInfoBuilder.setIndex(stop.getIndex() - 1);
      stopInfoBuilder.setStopId(stop.getStopId());
      stopInfoBuilder.setStopSpot(queryRoutingPositionInfo(stop));

      for (SchedulePlanningSpotCommandVO parkingSpot : stop.getSpot()) {
        RemoteServiceEntity.RoutingPosition.Builder routingPositionBuilder =
                RemoteServiceEntity.RoutingPosition.newBuilder();
        routingPositionBuilder.setLongitude(parkingSpot.getLon());
        routingPositionBuilder.setLatitude(parkingSpot.getLat());
        routingPositionBuilder.setAngle(parkingSpot.getHeading());
        stopInfoBuilder.addParkingSpot(routingPositionBuilder.build());
      }
      stopInfoBuilder.setType(RemoteServiceEntity.StopInfo.Type.TODO);
      naviRoutingCommandBuilder.addStopInfo(stopInfoBuilder.build());
    }
    RemoteServiceEntity.NaviRoutingCommand.RestartCmd.Builder restartCmdBuilder =
            RemoteServiceEntity.NaviRoutingCommand.RestartCmd.newBuilder();
    restartCmdBuilder.setRoutingId(0);
    restartCmdBuilder.setAutoStart(false);
    restartCmdBuilder.setReuseRoutingId(false);
    naviRoutingCommandBuilder.setRestartCmd(restartCmdBuilder.build());
    boolean result = createBusinessRequest(planningRouteCommand, "scheduleNavigation", naviRoutingCommandBuilder.build());
    if (!result) {
      return HttpResult.error(SeverErrorEnum.ERROR_HERMES_ABSENT.getCode(), SeverErrorEnum.ERROR_HERMES_ABSENT.getMessage());
    }
    return HttpResult.success();
  }

  /**
   * <p>
   * Create business request.
   * </p>
   *
   * @param vo the name of vehicle
   * @param moduleName the module name of header
   * @param command the command
   */
  private boolean createBusinessRequest(RemoteCommandVO vo, String moduleName, Object command) {
    RemoteServiceEntity.Header.Builder headerBuilder = RemoteServiceEntity.Header.newBuilder();

    headerBuilder.setModuleName(moduleName);
    headerBuilder.setTimestamp(System.currentTimeMillis());
    Integer sequenceNum = 0;
    headerBuilder.setSequenceNum(sequenceNum);
    headerBuilder.setDisableToSend(Optional.ofNullable(vo.getNeedResponse()).orElse(true));
    headerBuilder.setProcessCallCount(Optional.ofNullable(vo.getRequestId()).orElse(RequestIdUtils.getRequestId()));
    RemoteServiceEntity.BusinessRequest.Builder businessRequestBuilder =
        RemoteServiceEntity.BusinessRequest.newBuilder();
    if (command instanceof RemoteServiceEntity.NaviRoutingCommand) {
      businessRequestBuilder
          .setNaviRoutingCommand((RemoteServiceEntity.NaviRoutingCommand) command);
    } else if (command instanceof RemoteServiceEntity.MotionCommand) {
      businessRequestBuilder.setMotionCommand((RemoteServiceEntity.MotionCommand) command);
    } else if (command instanceof RemoteServiceEntity.ChassisCommand) {
      RemoteServiceEntity.ChassisCommand chassisCommand = (RemoteServiceEntity.ChassisCommand) command;
      if (ChassisCommand.CommandType.SUPER == chassisCommand.getCommandType()) {
        businessRequestBuilder.setContinualChassisCommand(chassisCommand);
      } else {
        businessRequestBuilder.setChassisCommand(chassisCommand);
      }
    } else {
      return false;
    }
    businessRequestBuilder.setHeader(headerBuilder.build());
    RemoteServiceEntity.RemoteServiceRequest.Builder remoteServiceRequestBuilder =
        RemoteServiceEntity.RemoteServiceRequest.newBuilder();
    remoteServiceRequestBuilder.setRemoteServiceType(
        RemoteServiceEntity.RemoteServiceRequest.RemoteServiceType.BUSINESS_REQUEST);
    remoteServiceRequestBuilder.setBusinessRequest(businessRequestBuilder.build());
    return createAndPublishDispatchResponse(vo.getVehicleName(), remoteServiceRequestBuilder.build());
  }

  /**
   * <p>
   * Create and publish dispatch response.
   * </p>
   *
   * @param vehicleName the vehicle name
   * @param remoteServiceRequest the remote service request.
   */
  private boolean createAndPublishDispatchResponse(String vehicleName,
      RemoteServiceEntity.RemoteServiceRequest remoteServiceRequest) {
    RemoteServiceEntity.DispatchResponse.Builder builder =
        RemoteServiceEntity.DispatchResponse.newBuilder();
    builder.setRemoteServiceRequest(remoteServiceRequest);
    log.info("Send message {} to Vehicle {}", remoteServiceRequest, vehicleName);
    RemoteServiceEntity.DispatchResponse response = builder.build();
    String responseString = response.toString();

    Client clientInfo = new Client();
    clientInfo.setName(vehicleName);
    CommandMessage commandMessage = new CommandMessage();
    commandMessage.setClient(clientInfo);
    commandMessage.setMessage(responseString);
    String topic = topicName + vehicleName;
    RTopic rTopic = redissonClient.getTopic(topic);
    long result = 0;
    if (rTopic.countSubscribers() > 0) {
      result = rTopic.publish(JsonUtils.writeValueAsString(commandMessage));
      log.info("Success publish schedule command msg to topic {}, result {}", topic, result);
    } else {
      log.error("Push hermes message error, topic {} listener {} absent", topic, rTopic.countSubscribers());
    }
    return Long.compare(result, 0) > 0;
  }

  public RemoteServiceEntity.RoutingPosition queryRoutingPositionInfo(SchedulePlanningStopCommandVO stop) {
    RemoteServiceEntity.RoutingPosition.Builder routingPositionBuilder = RemoteServiceEntity.RoutingPosition
            .newBuilder();
    routingPositionBuilder.setLongitude(stop.getLon());
    routingPositionBuilder.setLatitude(stop.getLat());
    routingPositionBuilder.setAngle(stop.getHeading());
    RemoteServiceEntity.RoutingPosition routingPosition = routingPositionBuilder.build();
    return routingPosition;
  }

  private boolean checkHermesConnection(String vehicleName) {
    RTopic rTopic = redissonClient.getTopic(topicName + vehicleName);
    return rTopic.countSubscribers() > 0;
  }

}
