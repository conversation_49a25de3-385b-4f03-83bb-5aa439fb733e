/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.server.stream.controller;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.server.api.domain.vo.PowerManagerCommandVO;
import com.jdx.rover.server.api.service.command.PowerManagerCommandApi;
import com.jdx.rover.server.stream.service.PowerManagerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 电源管理controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping(value = "/server/stream/command/power/manager")
public class PowerManagerController implements PowerManagerCommandApi {

  @Autowired
  private PowerManagerService powerManagerService;

  /**
   * 远程重启/关闭
   */
  @Override
  @PostMapping
  public HttpResult<Void> publishPowerManagerCommand(@RequestBody PowerManagerCommandVO powerManagerCommandVO) {
    HttpResult<Void> httpResult = powerManagerService.publishPowerManagerCommand(powerManagerCommandVO);
    return httpResult;
  }
}
