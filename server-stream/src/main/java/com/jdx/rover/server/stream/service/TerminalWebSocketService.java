/***************************************************************************
 // *
 // * Copyright (c) 2020 JD.com, Inc. All Rights Reserved
 // *
 // **************************************************************************/
//
package com.jdx.rover.server.stream.service;

import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.server.api.domain.dto.webterminal.WebTerminalResponseDTO;
import com.jdx.rover.server.api.domain.dto.webterminal.WebTerminalResponseHeader;
import com.jdx.rover.server.api.domain.enums.ws.WsMessageStateEnum;
import com.jdx.rover.server.api.domain.vo.webterminal.WebTerminalRequestHeader;
import com.jdx.rover.server.api.domain.vo.webterminal.WebTerminalRequestVO;
import com.jdx.rover.server.repository.jmq.JmqProducerManager;
import com.jdx.rover.server.stream.config.ProducerTopicProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * terminal 服务
 *
 * <AUTHOR>
 * @version 1.0
 * @date  2022-01-01
 */
@Service
@Slf4j
public class TerminalWebSocketService {

  @Autowired
  private KafkaTemplate<String, String> kafkaTemplate;

  /**
   * jmq消息发送producer
   */
  @Autowired
  private JmqProducerManager jmqProducerManager;

  /**
   * 生产主题配置类
   */
  @Autowired
  private ProducerTopicProperties producerTopicProperties;


  /**
   * 发送车端响应
   */
  public String sendTerminalHeartbeatReply(WebTerminalRequestHeader header) {
    if (header == null) {
      return null;
    }
    WebTerminalResponseHeader responseHeader = new WebTerminalResponseHeader(header);
    responseHeader.setResponseTime(System.currentTimeMillis());
    responseHeader.setMessageState(WsMessageStateEnum.RECEIVED.getValue());
    WebTerminalResponseDTO responseDTO = new WebTerminalResponseDTO();
    responseDTO.setHeader(responseHeader);
    return JsonUtils.writeValueAsString(responseDTO);
  }

  /**
   * 发送监控
   *
   * @param webTerminalRequestVO
   */
  @Async
  public void sendCommandToMonitor(WebTerminalRequestVO webTerminalRequestVO) {
    String message = JsonUtils.writeValueAsString(webTerminalRequestVO);
    jmqProducerManager.sendOrderedAndLog(producerTopicProperties.getServerWebTerminalCommandUp(), message, webTerminalRequestVO.getHeader().getVehicleName());
  }

  public void sendCommandResponseToMonitor(String vehicleName, String message) {
    jmqProducerManager.sendOrderedAndLog(producerTopicProperties.getServerWebTerminalCommandUp(), message, vehicleName);
  }
}