package com.jdx.rover.server.stream.provider;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.jsf.provider.AbstractProvider;
import com.jdx.rover.server.api.domain.vo.PowerManagerCommandVO;
import com.jdx.rover.server.api.jsf.service.command.PowerManagerCommandService;
import com.jdx.rover.server.stream.service.PowerManagerService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * @description: PowerManagerCommandServiceImpl
 * @author: wangguotai
 * @create: 2024-09-26 21:32
 **/
@Service
@RequiredArgsConstructor
public class PowerManagerCommandServiceImpl extends AbstractProvider<PowerManagerCommandService> implements PowerManagerCommandService {

    private final PowerManagerService powerManagerService;

    @Override
    public HttpResult<Void> publishPowerManagerCommand(PowerManagerCommandVO powerManagerCommandVO) {
        return powerManagerService.publishPowerManagerCommand(powerManagerCommandVO);
    }
}