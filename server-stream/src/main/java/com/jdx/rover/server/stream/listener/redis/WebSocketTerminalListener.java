/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.server.stream.listener.redis;

import com.google.protobuf.TextFormat;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.listener.MessageListener;

import jakarta.websocket.Session;
import java.io.IOException;

/**
 * <AUTHOR>
 */
@Slf4j
public class WebSocketTerminalListener implements MessageListener<String> {
  private Session session;

  public WebSocketTerminalListener(Session session) {
    this.session = session;
  }

  @Override
  public void onMessage(CharSequence channel, String msg) {
    log.info("web terminal redison topic listener:{}", msg);
    try {
      synchronized (session) {
        this.session.getBasicRemote().sendText(msg);
      }
      log.info("web terminal redison topic listener succ.");
    } catch (TextFormat.ParseException e) {
      log.error("web terminal redison topic listener指令解析失败!msg={}", msg);
    } catch (IOException e) {
      log.error("web terminal redison topic listener指令发送失败!msg={}", msg);
    } catch (Exception e) {
      log.error("web terminal redison topic listener指令发送失败!msg={}", msg);
    }
  }
}

