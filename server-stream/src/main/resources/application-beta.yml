server:
  port: 8012
spring:
  cloud:
    nacos:
      discovery:
        server-addr: http://jdxnacos-beta.jd.local/
        username: nacos
        password: nacos-beta@jdlX2022
  kafka:
    bootstrap-servers: broker-kafka-poiybufu79-az1-0.jvessel-open-hb.jdcloud.com:9092,broker-kafka-poiybufu79-az1-1.jvessel-open-hb.jdcloud.com:9092,broker-kafka-poiybufu79-az1-2.jvessel-open-hb.jdcloud.com:9092
  data:
    redis:
      database: 2
      host: redis-hb3hpfz16cbi-proxy-nlb.jvessel-open-hb.jdcloud.com
      password: jdlX2022
websocket:
  maxBinaryMessageBufferSize: 81920
  maxSessionIdleTimeout: 600000

jmq:
  address: nameserver.jmq.jd.local:80
  password: b4ad745ee5a54408a4cdae1990942430
  app: serverstreamtest