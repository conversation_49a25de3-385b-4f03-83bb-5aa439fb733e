spring:
  application:
    name: rover-server-stream
  profiles:
    active: "@activatedProperties@"
  cloud:
    nacos:
      discovery:
        group: ${spring.profiles.active}_group
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss #jackson日期格式化,用于@RestController返回
  kafka:
    producer:
      retries: 5
  data:
    redis:
      password: root
      database: 1
      lettuce:
        pool:
          max-active: 500
          max-wait: -1ms
          min-idle: 0
          max-idle: 50
logging:
  config: classpath:log4j2.xml
websocket:
  maxTextMessageBufferSize: 81920
  maxBinaryMessageBufferSize: 819200
  maxSessionIdleTimeout: 60000
#jsf
jsf:
  provider:
    validation: true

jmq:
  address: test-nameserver.jmq.jd.local:50088
  password: 3f9e1de79acd4e3ba7e1e1aec51bf274
  app: roverServer
  topic:
    producer:
      serverPowerManager: server_power_manager_${spring.profiles.active}
      serverWebTerminalCommandUp: server_web_terminal_command_up_${spring.profiles.active}
      serverHermesReply: server_hermes_reply_${spring.profiles.active}
      serverRemoteCommandDelay: server_remote_command_delay_${spring.profiles.active}