server:
  port: 8012
spring:
  cloud:
    nacos:
      discovery:
        server-addr: http://jdxnacos.jd.local/
        username: nacos
        password: nacos@jdlX2022
  kafka:
    bootstrap-servers: broker-kafka-dzk69k556l-az1-0.jvessel-open-hb.jdcloud.com:9092,broker-kafka-dzk69k556l-az1-1.jvessel-open-hb.jdcloud.com:9092,broker-kafka-dzk69k556l-az1-2.jvessel-open-hb.jdcloud.com:9092
  data:
    redis:
      database: 2
      host: redis-ozj2fnhyspho-proxy-nlb.jvessel-open-hb.jdcloud.com
      password: jdlX2022

jmq:
  address: nameserver.jmq.jd.local:80
  password: 448a909b5347403a9da1fdac7039a7c5
  app: roverserverstream
  topic:
    producer:
      serverPowerManager: server_power_manager
      serverWebTerminalCommandUp: server_web_terminal_command_up
      serverHermesReply: server_hermes_reply
      serverRemoteCommandDelay: server_remote_command_delay