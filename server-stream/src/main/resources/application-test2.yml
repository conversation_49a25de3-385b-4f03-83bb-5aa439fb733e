server:
  port: 8012
spring:
  cloud:
    nacos:
      discovery:
        server-addr: http://jdxnacos-beta.jd.local/
        username: nacos
        password: nacos-beta@jdlX2022
  kafka:
    bootstrap-servers: broker-kafka-gugs5isimj-az1-0.jvessel-open-hb.jdcloud.com:9092,broker-kafka-gugs5isimj-az1-1.jvessel-open-hb.jdcloud.com:9092,broker-kafka-gugs5isimj-az1-2.jvessel-open-hb.jdcloud.com:9092
  data:
    redis:
      host: redis-aeir2xhl1h7s-proxy-nlb.jvessel-open-hb.jdcloud.com
      password: jdlX2022
      database: 2
websocket:
  maxBinaryMessageBufferSize: 81920
  maxSessionIdleTimeout: 60000

jmq:
  address: nameserver.jmq.jd.local:80
  password: b4ad745ee5a54408a4cdae1990942430
  app: serverstreamtest