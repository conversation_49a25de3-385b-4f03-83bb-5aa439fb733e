<?xml version="1.0" encoding="UTF-8" ?>
<Configuration status="OFF">
	<Properties>
		<Property name="APP_NAME">rover-server-stream</Property>
		<Property name="LOG_PATH">/export/Logs/${APP_NAME}</Property>
		<Property name="LOG_PATTERN">%d{yyyy-MM-dd HH:mm:ss.SSS}|%p|V1|%X{PFTID}|%X{REQID}|${APP_NAME}|${ENV}|${REGION}|${GROUP}|${IP}|%X{JLOG_SOURCE_IP}|%pid|[%t]|%X{JLOG_LOAD_PRESS}|%X{JLOG_SWIM_LANE}|%X{PIN}|%X{IDENTITY_ID}|%X{UUID}|%c - %m%n</Property>
	</Properties>
	<Appenders>
		<!-- 1、日志输出到控制台 -->
		<Console name="Console" target="SYSTEM_OUT">
			<!--过滤器设置输出的级别-->
			<ThresholdFilter level="INFO" onMatch="ACCEPT" onMismatch="DENY"/>
			<!-- 日志输出格式 -->
			<PatternLayout pattern="${LOG_PATTERN}"/>
		</Console>

		<!-- 2、日志输出到文件 INFO级别 每小时产生一个新的日志文件-->
		<RollingFile name="InfoHourRollingFile" fileName="${LOG_PATH}/app_info.log"
					 filePattern="${LOG_PATH}/info/$${date:yyyy-MM-dd}/app-%d{yyyy-MM-dd-HH}.log.gz">
			<!--过滤器设置输出的级别-->
			<ThresholdFilter level="INFO" onMatch="ACCEPT" onMismatch="DENY"/>
			<!-- 日志输出格式 -->
			<PatternLayout pattern="${LOG_PATTERN}"/>
			<!-- 设置日志备份频率 -->
			<Policies>
				<TimeBasedTriggeringPolicy interval="3"/>
			</Policies>
		</RollingFile>

		<!-- 3、日志输出到文件 ERROR级别 每小时产生一个新的日志文件-->
		<RollingFile name="ErrorHourRollingFile" fileName="${LOG_PATH}/app_error.log"
					 filePattern="${LOG_PATH}/error/$${date:yyyy-MM-dd}/app-%d{yyyy-MM-dd-HH}.log.gz">
			<!--过滤器设置输出的级别-->
			<ThresholdFilter level="ERROR" onMatch="ACCEPT" onMismatch="DENY"/>
			<!-- 日志输出格式 -->
			<PatternLayout pattern="${LOG_PATTERN}"/>
			<!-- 设置日志备份频率 -->
			<Policies>
				<TimeBasedTriggeringPolicy interval="3"/>
			</Policies>
		</RollingFile>
	</Appenders>
	<Loggers>
		<!--
          关闭kafka非业务日志，例如来自kafka-client包
        -->
		<Logger name="org.apache.kafka" level="OFF">
		</Logger>
		<!--jsf日志级别改为WARN-->
		<Logger name="com.jd.jsf.gd.registry.jsf" level="WARN"/>

		<!-- 根logger的设置，若代码中未找到指定的logger，则会根据继承机制，使用根logger-->
		<Root level="DEBUG">
			<appender-ref ref="InfoHourRollingFile"/>
			<appender-ref ref="ErrorHourRollingFile"/>
			<appender-ref ref="Console"/>
		</Root>
	</Loggers>
</Configuration>