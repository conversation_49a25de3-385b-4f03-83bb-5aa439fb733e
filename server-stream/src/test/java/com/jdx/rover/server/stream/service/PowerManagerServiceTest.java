package com.jdx.rover.server.stream.service;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.server.api.domain.enums.power.PowerManagerActionEnum;
import com.jdx.rover.server.api.domain.enums.power.PowerManagerModuleEnum;
import com.jdx.rover.server.api.domain.vo.PowerManagerCommandVO;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.*;

import static org.assertj.core.api.Assertions.assertThat;
/**
 * <AUTHOR>
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
class PowerManagerServiceTest {
  @Autowired
  private PowerManagerService powerManagerService;

  @Test
  void publishPowerManagerCommand() {
    PowerManagerCommandVO powerManagerCommandVO = new PowerManagerCommandVO();
    powerManagerCommandVO.setVehicleName("JD0001");
    powerManagerCommandVO.setModuleName(PowerManagerModuleEnum.CHASSIS.getValue());
    powerManagerCommandVO.setAction(PowerManagerActionEnum.REBOOT.getValue());
    HttpResult<Void> httpResult = powerManagerService.publishPowerManagerCommand(powerManagerCommandVO);
    assertThat(httpResult).isNotNull();
    assertThat(httpResult.getCode()).isEqualTo(HttpResult.STATUS_SUCCESS);
  }
}