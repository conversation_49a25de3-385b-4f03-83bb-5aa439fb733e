package com.jdx.rover.server.stream.websocket;

import jdx.rover.power.manager.dto.PowerManagerDto;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.lang.reflect.Method;


@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
class PowerManagerWebSocketTest {

  private PowerManagerWebSocket powerManagerWebSocket = new PowerManagerWebSocket();

  @Test
  void handleAckTest() throws Exception {
    PowerManagerDto.PowerManagerDTO info = PowerManagerWebSocketTest.buildPowerManagerDTO();
    Method checkParameterMethod = powerManagerWebSocket.getClass().getDeclaredMethod("handleAck", PowerManagerDto.PowerManagerDTO.class);
    checkParameterMethod.setAccessible(true);
    checkParameterMethod.invoke(powerManagerWebSocket, info);
  }

  public static PowerManagerDto.PowerManagerDTO buildPowerManagerDTO() {
    PowerManagerDto.PowerManagerDTO.Builder builder = PowerManagerDto.PowerManagerDTO.newBuilder();
    PowerManagerDto.ResponseHeader.Builder responseHeaderBuilder = PowerManagerDto.ResponseHeader.newBuilder();
    responseHeaderBuilder.setMessageType(PowerManagerDto.MessageType.POWER_MANAGER_COMMAND);
    responseHeaderBuilder.setSequenceNum(1);
    responseHeaderBuilder.setVehicleId("JD0004");
    responseHeaderBuilder.setClientTimestamp(System.currentTimeMillis() * 1000000L);
    responseHeaderBuilder.setServerTimestamp(1656571519436981393L);
    builder.setResponseHeader(responseHeaderBuilder);

    PowerManagerDto.PowerManagerResponse.Builder powerManagerResponseBuilder = PowerManagerDto.PowerManagerResponse.newBuilder();
    powerManagerResponseBuilder.setAck(PowerManagerDto.Ack.RUNNING);
    powerManagerResponseBuilder.setAckInfo("RUNNING INFO");
    powerManagerResponseBuilder.setAction(PowerManagerDto.Action.REBOOT);
    powerManagerResponseBuilder.setBody(PowerManagerDto.Body.CHASSIS);
    builder.setPowerManagerResponse(powerManagerResponseBuilder);

    PowerManagerDto.PowerManagerDTO info = builder.build();
    return info;
  }
}