package com.jdx.rover.server.stream.controller;

import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.server.api.domain.enums.power.PowerManagerActionEnum;
import com.jdx.rover.server.api.domain.enums.power.PowerManagerModuleEnum;
import com.jdx.rover.server.api.domain.vo.PowerManagerCommandVO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

/**
 * <AUTHOR>
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
class PowerManagerControllerTest {
  private MockMvc mockMvc;
  @Autowired
  private WebApplicationContext webApplicationContext;

  @BeforeEach
  public void setUp() throws Exception {
    mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
  }

  @Test
  void publishPowerManagerCommand() throws Exception {
    PowerManagerCommandVO powerManagerCommandVO = new PowerManagerCommandVO();
    powerManagerCommandVO.setVehicleName("JD0001");
    powerManagerCommandVO.setModuleName(PowerManagerModuleEnum.CHASSIS.getValue());
    powerManagerCommandVO.setAction(PowerManagerActionEnum.REBOOT.getValue());
    mockMvc
        .perform(MockMvcRequestBuilders.post("/server/stream/command/power/manager").contentType(MediaType.APPLICATION_JSON)
            .content(JsonUtils.writeValueAsString(powerManagerCommandVO)))
        .andExpect(MockMvcResultMatchers.status().isOk())
        .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(HttpResult.STATUS_SUCCESS));
  }
}