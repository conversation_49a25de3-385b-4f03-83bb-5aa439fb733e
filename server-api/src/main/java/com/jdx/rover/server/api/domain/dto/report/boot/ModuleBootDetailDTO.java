package com.jdx.rover.server.api.domain.dto.report.boot;

import lombok.Data;

import java.io.Serializable;

/**
 * 模块启动详情
 *
 * <AUTHOR>
 * @date 2023/5/9
 */
@Data
public class ModuleBootDetailDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 模块名称
     */
    private String name;

    /**
     * 启动开始时间：单位纳秒
     */
    private Long startTime;

    /**
     * 启动结束时间：单位纳秒
     */
    private Long endTime;

    /**
     * 启动状态
     */
    private String bootStatus;

    /**
     * 异常码
     */
    private Integer errorCode;

    /**
     * 异常消息
     */
    private String errorMsg;
}
