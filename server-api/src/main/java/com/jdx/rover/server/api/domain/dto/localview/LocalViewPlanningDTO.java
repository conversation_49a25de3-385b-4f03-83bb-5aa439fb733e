/*
 * Copyright (c) 2021-2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.server.api.domain.dto.localview;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 上报OBU车辆灯光状态数据
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/11/19
 */
@Data
public class LocalViewPlanningDTO implements Serializable {
    /**
     * 用于实现序列化的版本号。
     */
    private static final long serialVersionUID = 1L;

    /**
     * 规划决策
     */
    private PlanningDecisionDTO decision;

    /**
     * 红绿灯信息
     */
    private List<PlanningTrafficLightDTO> interestedLight;

}
