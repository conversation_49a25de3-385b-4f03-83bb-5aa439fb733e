/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.server.api.domain.vo;

import java.util.List;
import lombok.Data;
import lombok.ToString;

/**
 * <p>
 * This is a view object for control traffic light command.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Data
@ToString(callSuper=true)
public class RemoteTrafficLightCommandVO extends RemoteCommandVO {

  /**
   * <p>
   * Represents the id of traffic light. The default value is null. It's changeable.
   * </p>
   */
  private List<String> trafficLightId;


  /**
   * <p>
   * Represents the pass or cancel. It's changeable.
   * </p>
   */
  private Boolean passThrough;

}
