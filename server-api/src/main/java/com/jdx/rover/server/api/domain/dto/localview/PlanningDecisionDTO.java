/*
 * Copyright (c) 2021-2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.server.api.domain.dto.localview;

import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 车辆规划数据
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/11/19
 */
@Data
public class PlanningDecisionDTO implements Serializable {
    /**
     * 用于实现序列化的版本号。
     */
    private static final long serialVersionUID = 1L;

    /**
     * 车辆行为决策列表
     */
    private BehaviorDecisionsDTO behaviorDecisionList;

    /**
     * 是否在路口
     */
    private Boolean isInIntersection;

    /**
     * 是否在门/杆前
     */
    private Boolean isInFrontOfGate;

    /**
     * 是否在停靠
     */
    private Boolean isInPark;

}
