/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.server.api.service.vehicle;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.server.api.domain.dto.guardian.ModuleVersionInfoDTO;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleLauchStateInfoDTO;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleLocationAndPowerDTO;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleLocationDTO;
import com.jdx.rover.server.api.domain.dto.guardian.VehiclePncInfoDTO;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleRealtimeWheelInfoDTO;
import com.jdx.rover.server.api.domain.dto.vehicle.GridDataDTO;
import com.jdx.rover.server.api.domain.vo.vehicle.GridVO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface ServerVehicleApi {
  /**
   * 获取车辆实时位置
   *
   * @param vehicleName
   * @return
   */
  @GetMapping(value = "/server/business/vehicle/location/{vehicleName}")
  HttpResult<VehicleLocationDTO> getVehicleLocation(@PathVariable("vehicleName") String vehicleName);

  /**
   * 获取车辆实时位置和电量信息
   *
   * @param vehicleName
   * @return
   */
  @GetMapping(value = "/server/business/vehicle/locationAndPower/{vehicleName}")
  HttpResult<VehicleLocationAndPowerDTO> getLocationAndPower(@PathVariable("vehicleName") String vehicleName);

  /**
   * 获取车辆实时胎压信息
   *
   * @param vehicleName
   * @return
   */
  @GetMapping(value = "/server/business/vehicle/wheel_info/{vehicleName}")
  HttpResult<List<VehicleRealtimeWheelInfoDTO>> getWheelInfo(@PathVariable("vehicleName") String vehicleName);

   /**
   * 获取车辆初始化进度信息
   *
   * @param vehicleName
   * @return
   */
  @GetMapping(value = "/server/business/vehicle/lauch_state/{vehicleName}")
  HttpResult<VehicleLauchStateInfoDTO> getVehicleStartInitStateInfo(@PathVariable("vehicleName") String vehicleName);

  /**
   * 获取车辆当前版本信息
   *
   * @param vehicleName
   * @return
   */
  @GetMapping(value = "/server/business/vehicle/ota_version/{vehicleName}")
  HttpResult<Map<String, ModuleVersionInfoDTO>> getVehicleOtaVersionInfo(@PathVariable("vehicleName") String vehicleName);

  /**
   * 获取车辆实PNC信息
   *
   * @param vehicleName 车辆名称
   * @return
   */
  @GetMapping(value = "/server/business/vehicle/pnc/{vehicleName}")
  HttpResult<VehiclePncInfoDTO> getVehiclePnc(@PathVariable("vehicleName") String vehicleName);

  /**
   * 获取车辆实格口信息
   *
   * @param gridVO 格口入参
   * @return
   */
  @PostMapping(value = "/server/business/vehicle/getGridStatus")
  HttpResult<GridDataDTO> getGridStatus(@RequestBody GridVO gridVO);
}
