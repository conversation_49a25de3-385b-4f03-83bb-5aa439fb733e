/*
 * Copyright (c) 2021-2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.server.api.domain.enums.guardian;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * 系统状态枚举
 *
 * <AUTHOR> shiling
 * @version 1.0
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum SystemStateEnum {
  UNKNOWN("UNKNOWN", ""),
  OFFLINE("OFFLINE", "离线"),
  NORMAL("NORMAL", "正常"),
  SELF_REPAIR("SELF_REPAIR", "自修复中"),
  MALFUNCTION("MALFUNCTION", "系统故障"),
  CONNECTION_LOST("CONNECTION_LOST", "失联");

  /**
   * 状态
   */
  private String systemState;

  /**
   * 系统状态名称
   */
  private String systemStateName;
}
