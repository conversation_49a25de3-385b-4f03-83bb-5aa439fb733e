package com.jdx.rover.server.api.domain.dto.drive;

import lombok.Data;

import java.util.Date;

/**
 * 远程遥控信息
 *
 * <AUTHOR>
 */
@Data
public class DriveConnectDTO {
    /**
     * 响应客户端名称(如果车号,表示车端响应,如果驾驶舱号,表示驾驶舱响应)
     */
    private String clientName;

    /**
     * 车辆名称
     */
    private String vehicleName;

    /**
     * 驾驶舱名称
     */
    private String cockpitNumber;

    /**
     * 连接操作
     */
    private String connectOperation;

    /**
     * 消息状态
     */
    private String messageState;

    /**
     * 错误码
     */
    private String errorCode;

    /**
     * 错误消息
     */
    private String errorMessage;

    /**
     * 记录时间
     */
    private Date recordTime;

}
