/*
 * Copyright (c) 2021-2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.server.api.domain.enums.guardian;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * guardian连接状态.
 *
 * <AUTHOR>
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum GuardianConnectStatusEnum {
  CONNECT("connect", "连接"),
  DISCONNECT("disconnect", "断开"),
  ;

  /**
   * 值
   */
  private String value;

  /**
   * 中文描述
   */
  private String title;
}
