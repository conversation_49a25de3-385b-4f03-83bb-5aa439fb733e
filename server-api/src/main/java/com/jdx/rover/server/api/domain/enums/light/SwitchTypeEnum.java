/*
 * Copyright (c) 2020-2022 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.server.api.domain.enums.light;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * 开灯类型状态
 *
 * <AUTHOR>
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum SwitchTypeEnum {
    /**
     * 开关类型状态
     */
    UNKNOWN("未知"),
    HEAD_LIGHT("大灯"),
    TURN_LEFT("左转灯"),
    TURN_RIGHT("右转灯"),
    WARNING_FLASH("双闪灯"),
    ;

    /**
     * 标题描述
     */
    private final String title;
}
