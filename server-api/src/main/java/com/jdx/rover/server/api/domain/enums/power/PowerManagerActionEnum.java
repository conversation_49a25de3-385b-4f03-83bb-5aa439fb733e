/*
 * Copyright (c) 2021-2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.server.api.domain.enums.power;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum PowerManagerActionEnum {
  // errorLevel
  UNKNOWN_ACTION("UNKNOWN_ACTION", "未知"),
  POWER_OFF("POWER_OFF", "远程下电"),
  REBOOT("REBOOT", "断电重启"),
  RESET("RESET", "远程复位"),
  ;

  /**
   * 值
   */
  private final String value;
  /**
   * 标题描述
   */
  private final String title;

  public static PowerManagerActionEnum of(final String value) {
    for (PowerManagerActionEnum itemEnum : PowerManagerActionEnum.values()) {
      if (itemEnum.value.equals(value)) {
        return itemEnum;
      }
    }
    return null;
  }
}
