/*
 * Copyright (c) 2025 www.jd.com All rights reserved.
 */

package com.jdx.rover.server.api.domain.vo;

import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2025/8/7 13:49
 * @description 下发车辆限速指令
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString(callSuper = true)
public class MaxVelocityCommandVO extends RemoteCommandVO {

    /**
     * 限速值
     */
    @NotNull(message = "限速值不能为空")
    private Double maxVelocity;
}
