/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.server.api.service.command;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.server.api.domain.vo.PassNoSignalIntersectionCommandVO;
import com.jdx.rover.server.api.domain.vo.RemoteCommandVO;
import com.jdx.rover.server.api.domain.vo.RemoteControlCommandVO;
import com.jdx.rover.server.api.domain.vo.RemoteResetAbnormalCommandVO;
import com.jdx.rover.server.api.domain.vo.RemoteTrafficLightCommandVO;
import com.jdx.rover.server.api.domain.vo.SwitchLightCommandVO;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <p>
 * This is api interface for mini monitor vehicle command.
 * </p>
 *
 * <p>
 * <strong>Thread Safety: </strong> This class is mutable and not thread safe. But it will be used
 * as entity so it'll not cause any thread safe problem.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
public interface RemoteCommandApi {

  /**
   * <p>
   * Post emergency stop command.
   * </p>
   * 
   */
  @PostMapping("/server/stream/command/emergency_stop")
  HttpResult publishEmergencyStopCommand(RemoteCommandVO remoteCommandVo);

  /**
   * <p>
   * Post as arrived command.
   * </p>
   * 
   */
  @PostMapping("/server/stream/command/as_arrived")
  HttpResult publishAsArrivedCommand(RemoteCommandVO remoteCommandVo);

  /**
   * <p>
   * Post recovery command.
   * </p>
   * 
   */
  @PostMapping("/server/stream/command/recovery")
  HttpResult publishRecoveryCommand(RemoteCommandVO remoteCommandVo);

    /**
   * <p>
   * Post restart command.
   * </p>
   *
   */
  @PostMapping("/server/stream/command/restart")
  public HttpResult publishRestartCommand(RemoteCommandVO remoteCommandVo);

  /**
   * <p>
   * Post remote control command.
   * </p>
   * 
   */
  @PostMapping("/server/stream/command/remote_control")
  HttpResult publishRemoteControlCommand(RemoteControlCommandVO remoteControlCommandVo);

  /**
   * <p>
   * Publish reset abnormal command.
   * </p>
   *
   */
  @PostMapping("/server/stream/command/reset_abnormal")
  HttpResult publishResetAbnormalCommand(RemoteResetAbnormalCommandVO remoteResetAbnormalCommandVo);

  /**
   * <p>
   * Publish control traffic light command.
   * </p>
   *
   */
  @PostMapping("/server/stream/command/control_traffic_light")
  HttpResult publishControlTrafficLightCommand(RemoteTrafficLightCommandVO remoteTrafficLightCommandVo);

  /**
   * <p>
   * Publish control lamp command.
   * </p>
   */
  @PostMapping("/server/stream/command/control_lamp")
  public HttpResult publishControlLampCommand(RemoteCommandVO remoteCommandVO);

  /**
   * <p>
   * Publish relieves button stop command.
   * </p>
   */
  @PostMapping("/server/stream/command/relieve_button_stop")
  public HttpResult publishRelieveButtonStopCommand(RemoteCommandVO remoteCommandVO);

  /**
   * 通过无信号路口
   */
  @PostMapping("/server/stream/command/pass_no_signal_intersection")
  HttpResult passNoSignalIntersection(@RequestBody PassNoSignalIntersectionCommandVO passNoSignalIntersectionCommandVO) ;

  /**
   * 切换地图有图模式
   */
  @PostMapping("/server/stream/command/runHaveMap")
  HttpResult runHaveMap(@RequestBody RemoteCommandVO remoteCommandVO);

  /**
   * 切换地图无图模式
   */
  @PostMapping("/server/stream/command/runNoMap")
  HttpResult runNoMap(@RequestBody RemoteCommandVO remoteCommandVO);

  /**
   * 通过无信号路口
   */
  @PostMapping("/server/stream/command/switch_light")
  HttpResult switchLight(@RequestBody SwitchLightCommandVO switchLightCommandVO);
}
