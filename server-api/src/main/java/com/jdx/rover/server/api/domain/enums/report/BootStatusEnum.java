/*
 * Copyright (c) 2020-2022 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.server.api.domain.enums.report;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * 启动状态
 *
 * <AUTHOR>
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum BootStatusEnum {
    /**
     * 启动状态
     */
    WAIT_START("等待启动"),
    STARTING("启动中"),
    START_FAILED("启动失败"),
    START_SUCCESS("启动成功"),
    SHUTTING_DOWN("关机中"),
    SHUT_DOWN_SUCCESS("关机成功"),
    ;

    /**
     * 标题描述
     */
    private final String title;
}
