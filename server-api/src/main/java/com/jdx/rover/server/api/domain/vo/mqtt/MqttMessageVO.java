/*
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.server.api.domain.vo.mqtt;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * mqtt消息类
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MqttMessageVO<T> {
  /**
   * 主题(必填)
   */
  @NotBlank(message = "topic主题不能为空")
  private String topic;

  /**
   * 消息质量,默认1,至少送到一次(选填)
   *
   * @see com.jdx.rover.server.api.domain.enums.mqtt.MqttQosEnum
   */
  private Integer qos;

  /**
   * 是否保留,默认false(选填)
   */
  private Boolean retained;

  /**
   * 消息实体,只支持byte[],String
   * 不支持POJO实体对象,需要转化成json对象或者byte[]
   * 不支持protobuf,需要转化成byte[]
   */
  private T message;

  /**
   * mqtt broker服务器ID,不存在默认为jdxmqtt
   */
  private String mqttBrokerId;
}
