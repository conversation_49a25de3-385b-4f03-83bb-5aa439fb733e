/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.server.api.domain.dto.guardian;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 车辆异常明细
 *
 * <AUTHOR>
 */
@Data
public class VehicleAbnormalDetailDTO implements Serializable {
  private static final long serialVersionUID = 1L;
  /**
   * 模块名称
   */
  private String moduleName;

  /**
   * 错误码
   */
  private String errorCode;

  /**
   * 错误级别
   */
  private String errorLevel;

  /**
   * 错误消息
   */
  private String errorMsg;

  /**
   * 开始时间
   */
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  private Date startTime;

  /**
   * 结束时间
   */
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  private Date endTime;
}
