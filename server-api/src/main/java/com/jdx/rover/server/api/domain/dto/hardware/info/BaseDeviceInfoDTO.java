package com.jdx.rover.server.api.domain.dto.hardware.info;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 基础设备信息
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/8/21
 */
@Data
public class BaseDeviceInfoDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 类型
     */
    private String type;

    /**
     * SN号
     */
    private String sn;

    /**
     * 设备版本
     */
    private String version;

    /**
     * 设备唯一id,同类设备从1开始
     */
    private Integer deviceId;

    /**
     * 记录时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    private Date recordTime;
}
