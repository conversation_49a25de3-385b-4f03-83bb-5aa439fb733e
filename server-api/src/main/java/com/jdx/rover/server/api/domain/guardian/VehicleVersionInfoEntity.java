/*
 * Copyright (c) 2021-2021 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.server.api.domain.guardian;

import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * This is a view object class for vehicle version save request.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class VehicleVersionInfoEntity implements Serializable {
  private static final long serialVersionUID = 1L;

  /**
   * <p>
   * Represents the state of vehicle. The default value is null. It's
   * changeable.
   * </p>
   */
  private String moduleName;

  /**
   * <p>
   * Represents the module serialId. The default value is null. It's changeable.
   * </p>
   */
  private String serialId;

  /**
   * <p>
   * Represents the module current version. The default value is null. It's changeable.
   * </p>
   */
  private String curVersion;

  /**
   * <p>
   * Represents the module ota version. The default value is null. It's changeable.
   * </p>
   */
  private String otaVersion;

  /**
   * <p>
   * Represents the module key contains name and serialId. The default value is null. It's changeable.
   * </p>
   */
  public String getKey() {
    return String.format("%s-%s", moduleName, serialId);
  }

}
