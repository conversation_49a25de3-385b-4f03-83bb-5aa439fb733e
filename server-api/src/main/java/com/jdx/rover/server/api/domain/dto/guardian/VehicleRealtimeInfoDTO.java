/*
 * Copyright (c) 2021-2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.server.api.domain.dto.guardian;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * 车辆实时信息
 *
 * <AUTHOR> shiling
 * @version 1.0
 */
@Data
public class VehicleRealtimeInfoDTO implements Serializable {
  private static final long serialVersionUID = 1L;

  /**
   * 车辆名称
   */
  private String vehicleName;

  /**
   * 记录时间
   */
  private Date recordTime;

  /**
   * 速度
   */
  private Double speed;

  /**
   * 电量
   */
  private Double power;

  /**
   * 朝向
   */
  private Double heading;

  /**
   * 纬度
   */
  private Double lat;

  /**
   * 经度
   */
  private Double lon;

  /**
   * 当前站点完成里程
   */
  private Double currentStopFinishedMileage;

  /**
   * 去往下一个停靠点剩余里程
   */
  private Double mileageToNextStop;

  /**
   * 当前停靠点索引
   */
  private Integer currentStopIndex;

  /**
   * 系统状态
   */
  private String systemState;

  /**
   * 车辆模式
   */
  private String vehicleState;

  /**
   * 零偏状态
   */
  private Double steerZero;

  /**
   * 使能信息
   */
  private VehicleRealtimeDrivableInfoDTO drivableDirection;

  /**
   * 胎压
   */
  private List<VehicleRealtimeWheelInfoDTO> wheelInfo;
}
