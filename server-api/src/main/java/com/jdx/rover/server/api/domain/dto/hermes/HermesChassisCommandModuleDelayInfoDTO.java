/*
 * Copyright (c) 2022-2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.server.api.domain.dto.hermes;

import com.jdx.rover.server.api.domain.enums.ChassisCommandStateEnum;
import lombok.Data;

/**
 * 遥控指令各模块延迟响应
 * <AUTHOR>
 */
@Data
public class HermesChassisCommandModuleDelayInfoDTO {
  /**
   * <p>
   * Represents the name of module. It's changeable.
   * </p>
   */
  private String moduleName;

  /**
   * <p>
   * Represents the command receive time. The default value is 0. It's changeable.
   * </p>
   */
  private Long receiveTime;

  /**
   * <p>
   * Represents the command transit time. The default value is 0. It's changeable.
   * </p>
   */
  private Long transitTime;
}
