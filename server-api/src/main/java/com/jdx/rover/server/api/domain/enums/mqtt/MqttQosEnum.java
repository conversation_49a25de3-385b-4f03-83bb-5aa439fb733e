/*
 * Copyright (c) 2020-2022 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.server.api.domain.enums.mqtt;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * mqtt消息质量
 *
 * <AUTHOR>
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum MqttQosEnum {
  AT_MOST_ONCE(0, "至多一次"),
  AT_LEAST_ONCE(1, "至少一次"),
  ONLY_ONCE(2, "有且只有一次"),
  ;

  /**
   * 值
   */
  private Integer value;

  /**
   * 标题描述
   */
  private String title;
}
