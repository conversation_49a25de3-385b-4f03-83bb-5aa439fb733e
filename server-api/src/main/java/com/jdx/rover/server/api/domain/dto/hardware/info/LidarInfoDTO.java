package com.jdx.rover.server.api.domain.dto.hardware.info;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 激光雷达信息数据传输对象
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/8/21
 */
@Data
public class LidarInfoDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * PN号
     */
    private String pn;

    /**
     * SN号
     */
    private String sn;

    /**
     * 软件版本
     */
    private String softVersion;

    /**
     * 硬件版本
     */
    private String hardwareVersion;

    /**
     * 雷达位置
     */
    private String position;

    /**
     * 型号
     */
    private String type;

    /**
     * 设备唯一ID
     * <p>同类设备从1开始</p>
     */
    private Integer deviceId;

    /**
     * 记录时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    private Date recordTime;
}
