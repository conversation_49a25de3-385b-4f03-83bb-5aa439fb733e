/*
 * Copyright (c) 2021-2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.server.api.domain.enums.power;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum PowerManagerModuleEnum {
  // errorLevel
  UNKNOWN_BODY("UNKNOWN_BODY", "未知"),
  COMPUTE("COMPUTE", "compute端"),
  CONTROL("CONTROL", "control端"),
  CHASSIS("CHASSIS", "底盘"),
  ;

  /**
   * 值
   */
  private final String value;
  /**
   * 标题描述
   */
  private final String title;

  public static PowerManagerModuleEnum of(final String value) {
    for (PowerManagerModuleEnum itemEnum : PowerManagerModuleEnum.values()) {
      if (itemEnum.value.equals(value)) {
        return itemEnum;
      }
    }
    return null;
  }
}
