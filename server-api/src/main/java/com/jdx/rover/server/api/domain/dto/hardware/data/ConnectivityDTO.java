package com.jdx.rover.server.api.domain.dto.hardware.data;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 连通性信息数据传输对象
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/8/21
 */
@Builder
@Data
public class ConnectivityDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 车号
     */
    private String vehicleName;

    /**
     * 连通性类型
     * <p>0-未知，1-域控路由器，2-域控Web，3-域控雷达，4-域控安卓，5-安卓Web</p>
     */
    private Integer connectivityType;

    /**
     * 连接源
     */
    private String from;

    /**
     * 连接目标
     */
    private String to;

    /**
     * 延迟
     * <p>单位：毫秒</p>
     */
    private Float delay;

    /**
     * 丢包率
     * <p>单位：百分比，范围0-100</p>
     */
    private Float packetLossRate;

    /**
     * 记录时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    private Date recordTime;
}
