/*
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.server.api.service.mqtt;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.server.api.domain.vo.mqtt.MqttMessageVO;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

/**
 * <AUTHOR>
 */
public interface MqttSendApi {
  /**
   * 发送字符串mqtt消息
   *
   * @param mqttMessageVO
   */
  @PostMapping("/server/mqtt/send/string")
  HttpResult sendString(@RequestBody MqttMessageVO<String> mqttMessageVO);

  /**
   * 发送byte数组mqtt消息
   *
   * @param mqttMessageVO
   */
  @PostMapping("/server/mqtt/send/bytes")
  HttpResult sendBytes(@RequestBody @Valid MqttMessageVO<byte[]> mqttMessageVO);
}
