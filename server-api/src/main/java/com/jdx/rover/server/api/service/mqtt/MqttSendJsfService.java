/*
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.server.api.service.mqtt;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.server.api.domain.vo.mqtt.MqttMessageVO;

/**
 * 发送mqtt的jsf服务
 *
 * <AUTHOR>
 * @date 2024/10/12
 */
public interface MqttSendJsfService {

    /**
     * 发送字符串mqtt消息
     */
    HttpResult<Void> sendString(MqttMessageVO<String> mqttMessageVO);

    /**
     * 发送byte数组mqtt消息
     */
    HttpResult<Void> sendBytes(MqttMessageVO<byte[]> mqttMessageVO);
}
