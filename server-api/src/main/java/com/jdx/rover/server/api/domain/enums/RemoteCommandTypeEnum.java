/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.server.api.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <p>
 * This is supervisor remoteControl command type enum.
 * </p>
 *
 * <p>
 * <strong>remote control command type: </strong> enumeration of the class remote control
 * type.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Getter
@ToString
@AllArgsConstructor
public enum RemoteCommandTypeEnum {
  /**
   * <p>
   * The enumerate command types.
   * </p>
   */
  UNKNOWN("UNKNOWN"), NORMAL("NORMAL"), SUPER("SUPER"), RESET("RESET"), MOBILE("MOBILE");

  /**
   * <p>
   * The command type corresponding to the enumeration.
   * </p>
   */
  @Getter
  @Setter
  private String type;
}
