package com.jdx.rover.server.api.domain.dto.hardware.data;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 域控实时数据传输对象
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/8/21
 */
@Builder
@Data
public class DomainDataDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 车号
     */
    private String vehicleName;

    /**
     * SOC温度
     * <p>单位：摄氏度</p>
     */
    private Float socTemperature;

    /**
     * CPU负载率
     * <p>单位：百分比，范围0-100</p>
     */
    private Float cpuLoad;

    /**
     * 内存占用率
     * <p>单位：百分比，范围0-100</p>
     */
    private Float memoryUsageRate;

    /**
     * 零部件网络延迟
     * <p>单位：毫秒</p>
     */
    private Long networkDelay;

    /**
     * 硬盘使用率
     * <p>单位：百分比，范围0-100</p>
     */
    private Float diskUsageRate;

    /**
     * 硬盘温度
     * <p>单位：摄氏度</p>
     */
    private Float diskTemperature;

    /**
     * 设备唯一ID
     * <p>同类设备从1开始</p>
     */
    private Integer deviceId;

    /**
     * 记录时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    private Date recordTime;
}
