package com.jdx.rover.server.api.domain.dto.hardware.data;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 底盘实时数据传输对象
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/8/21
 */
@Data
@Builder
public class ChassisDataDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 车号
     */
    private String vehicleName;

    /**
     * 故障等级
     * <p>0-未知，1-正常运行，2-预警需检修，3-故障状态</p>
     */
    private Integer errorLevel;

    /**
     * 实际档位
     */
    private Float gear;

    /**
     * 车速
     * <p>单位：km/h</p>
     */
    private Float velocity;

    /**
     * 车辆工作模式
     */
    private Long workMode;

    /**
     * 12V小电池电压
     * <p>单位：V</p>
     */
    private Long smallBatteryVoltage;

    /**
     * 动力电池SOC
     * <p>单位：百分比，范围0-100</p>
     */
    private Float powerBatterySoc;

    /**
     * 动力电池SOH
     * <p>单位：百分比，范围0-100</p>
     */
    private Float powerBatterySoh;

    /**
     * 动力电池电压
     * <p>单位：V</p>
     */
    private Float powerBatteryVoltage;

    /**
     * 动力电池温度
     * <p>单位：摄氏度</p>
     */
    private Float powerBatteryTemperature;

    /**
     * 充电枪连接状态
     * <p>0-未连接，1-已连接</p>
     */
    private Float chargeState;

    /**
     * 左前轮胎压
     * <p>单位：kPa</p>
     */
    private Float frontLeftPressure;

    /**
     * 右前轮胎压
     * <p>单位：kPa</p>
     */
    private Float frontRightPressure;

    /**
     * 左后轮胎压
     * <p>单位：kPa</p>
     */
    private Float backLeftPressure;

    /**
     * 右后轮胎压
     * <p>单位：kPa</p>
     */
    private Float backRightPressure;

    /**
     * 记录时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    private Date recordTime;
}
