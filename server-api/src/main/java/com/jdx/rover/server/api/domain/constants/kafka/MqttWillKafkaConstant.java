/*
 * Copyright (c) 2021-2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.server.api.domain.constants.kafka;

/**
 * kafka消息主题
 *
 * <AUTHOR>
 */
public interface MqttWillKafkaConstant {
  /**
   * mqtt安卓遗嘱
   */
  String SERVER_MQTT_ANDROID_WILL = "mqtt_android_will";

  /**
   * mqtt 车端rover遗嘱
   */
  String SERVER_MQTT_ROVER_WILL = "mqtt_rover_will";

  /**
   * mqtt视频遗嘱
   */
  String SERVER_MQTT_VIDEO_WILL = "mqtt_video_will";

  /**
   * 平行驾驶大屏遗嘱
   */
  String DRIVE_SCREEN_WILL = "r_drive_screen_will";

  /**
   * 平行驾驶控制屏遗嘱
   */
  String DRIVE_CONTROL_PAD_WILL = "r_drive_controlPad_will";

  /**
   * 平行驾驶驾驶舱遗嘱
   */
  String DRIVE_COCKPIT_WILL = "r_drive_cockpit_will";

  /**
   * 小程序遗嘱
   */
  String DRIVE_MINIMONITOR_WILL = "r_drive_miniMonitor_will";

  /**
   * 监控遗嘱
   */
  String DRIVE_MONITOR_WILL = "r_drive_monitor_will";

  /**
   * mqtt PDU遗嘱
   */
  String PDU_WILL = "r_pdu_will";
}
