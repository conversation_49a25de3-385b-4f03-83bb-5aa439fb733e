/*
 * Copyright (c) 2021-2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.server.api.domain.dto.obu;

import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 上报OBU数据
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/11/19
 */
@Data
public class VehicleReportDataDTO implements Serializable {
  /**
   * 序列化id
   */
  private static final long serialVersionUID = 1L;

  /**
   * 车辆ID
   */
  private String id;

  /**
   * 车辆编号
   */
  private String vin;

  /**
   * 记录时间
   */
  private Long timestamp;

  /**
   * 实时行驶数据
   */
  private VehicleDriverDataDTO driverData;

}
