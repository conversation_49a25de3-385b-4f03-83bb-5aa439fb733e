package com.jdx.rover.server.api.domain.dto.hardware.data;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * GNSS实时数据传输对象
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/8/21
 */
@Data
public class GnssDataDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 车号
     */
    private String vehicleName;

    /**
     * 在线状态
     * <p>0-未知，1-离线，2-在线</p>
     */
    private Integer onlineStatus;

    /**
     * RTK状态
     * <p>例如：FIXED, FLOAT, SINGLE, NONE</p>
     */
    private String rtkState;

    /**
     * 卫星数量
     */
    private Integer satelliteNumber;

    /**
     * HDOP值 (水平精度因子)
     * <p>值越小定位精度越高，通常小于1表示精度很高</p>
     */
    private Float hdop;

    /**
     * SNR值 (信噪比)
     * <p>单位：dB，值越大信号质量越好</p>
     */
    private Float snr;

    /**
     * 朝向
     * <p>单位：度，范围0-360，0表示正北，90表示正东</p>
     */
    private Double heading;

    /**
     * 加密后经纬度位置
     * <p>计算公式=aes(经度,纬度)</p>
     */
    private String position;

    /**
     * 经度
     */
    private Double longitude;

    /**
     * 纬度
     */
    private Double latitude;

    /**
     * 记录时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    private Date recordTime;
}
