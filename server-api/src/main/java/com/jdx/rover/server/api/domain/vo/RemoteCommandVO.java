/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.server.api.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jdx.rover.server.api.domain.enums.RemoteCommandTypeEnum;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <p>
 * This is a view object for remote command.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class RemoteCommandVO {

  /**
   * <p>
   * Represents the vehicle's name.
   * </p>
   */
  @NotBlank
  private String vehicleName;

  /**
   * <p>
   * Represents the receive timeStamp of operate request.
   * </p>
   */
  @NotNull
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  private Date receiveTimeStamp;

  /**
   * <p>
   * Represents the transit timeStamp of operate request.
   * </p>
   */
  @NotNull
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  private Date transitTimeStamp;

  /**
   * <p>
   * Represents the command type. It's changeable.
   * </p>
   */
  private RemoteCommandTypeEnum commandType;

  /**
   * 请求ID
   */
  private Long requestId;

  /**
   * 是否需要响应
   */
  private Boolean needResponse;
}
