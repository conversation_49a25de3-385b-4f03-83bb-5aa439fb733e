/*
 * Copyright (c) 2022-2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.server.api.domain.dto.guardian;

import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class GuardianConnectDTO {
  /**
   * 车辆名称
   */
  private String vehicleName;

  /**
   * guardian连接状态
   */
  private String guardianConnectStatus;

  /**
   * 记录时间
   */
  private Date recordTime;
}
