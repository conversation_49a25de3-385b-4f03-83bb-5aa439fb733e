/*
 * Copyright (c) 2021-2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.server.api.domain.enums.power;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum PowerManagerAckEnum {
  RECEIVED("RECEIVED", "已接收"),
  RUNNING("RUNNING", "正在执行"),
  SUCCESS("SUCCESS", "执行成功"),
  FAIL("FAIL", "执行失败"),
  ;

  /**
   * 值
   */
  private final String value;
  /**
   * 标题描述
   */
  private final String title;

  public static PowerManagerAckEnum of(final String value) {
    for (PowerManagerAckEnum itemEnum : PowerManagerAckEnum.values()) {
      if (itemEnum.value.equals(value)) {
        return itemEnum;
      }
    }
    return null;
  }
}
