/*
 * Copyright (c) 2021-2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.server.api.domain.dto.guardian;

import java.io.Serializable;

import lombok.Data;

/**
 * 规划路径点
 *
 * <AUTHOR> shiling
 * @version 1.0
 */
@Data
public class VehiclePncNavigationRoutingPositionDTO implements Serializable {
  private static final long serialVersionUID = 1L;

  /**
   * 经度
   */
  private Double lon;

  /**
   * 纬度
   */
  private Double lat;
}
