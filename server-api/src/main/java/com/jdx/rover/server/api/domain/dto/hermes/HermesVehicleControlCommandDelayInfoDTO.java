/*
 * Copyright (c) 2022-2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.server.api.domain.dto.hermes;

import com.jdx.rover.server.api.domain.enums.ChassisCommandStateEnum;
import lombok.Data;

import java.util.List;

/**
 * 遥控指令延迟响应
 * <AUTHOR>
 */
@Data
public class HermesVehicleControlCommandDelayInfoDTO {
  /**
   * <p>
   * 指令Id
   * </p>
   */
  private Integer id;

  /**
   * <p>
   * 车号
   * </p>
   */
  private String vehicleName;

  /**
   * <p>
   * Represents the state of command request. The default value is OK. It's changeable.
   * </p>
   */
  private ChassisCommandStateEnum state;

  /**
   * <p>
   * 指令延迟信息
   * </p>
   */
  private List<HermesChassisCommandModuleDelayInfoDTO> delayInfo;

  /**
   * <p>
   *当前车辆速度
   * </p>
   */
  private Double speed;

  /**
   * <p>
   * 当前车辆朝向
   * </p>
   */
  private Double heading;

  /**
   * <p>
   * 当前车轮转向
   * </p>
   */
  private Double frontWheelAngle;
}
