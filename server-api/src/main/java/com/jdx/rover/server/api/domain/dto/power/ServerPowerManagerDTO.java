package com.jdx.rover.server.api.domain.dto.power;

import lombok.Data;

/**
 * 电源管理DTO
 */
@Data
public class ServerPowerManagerDTO {
  /**
   * 车辆名称
   */
  private String vehicleName;

  /**
   * 请求ID
   */
  private String requestId;

  /**
   * 模块名称,见PowerManagerModuleEnum枚举
   */
  private String moduleName;

  /**
   * 操作名称
   */
  private String action;

  /**
   * 确认
   */
  private String ack;

  /**
   * 确认信息
   */
  private String ackInfo;
}
