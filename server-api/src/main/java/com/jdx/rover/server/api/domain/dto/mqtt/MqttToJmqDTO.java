/*
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.server.api.domain.dto.mqtt;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * mqtt转发jmq
 *
 * <AUTHOR>
 * @date 2024-09-20
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class MqttToJmqDTO<T> {
    /**
     * mqtt主题
     */
    private String topic;

    /**
     * 业务数据
     */
    private T data;
}
