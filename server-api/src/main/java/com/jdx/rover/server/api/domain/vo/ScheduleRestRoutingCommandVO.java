/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.server.api.domain.vo;

import javax.validation.constraints.NotNull;

import lombok.Data;
import lombok.ToString;

/**
 * <p>
 * This is a view object for schedule reset routing command.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Data
@ToString(callSuper=true)
public class ScheduleRestRoutingCommandVO extends RemoteCommandVO {

  /**
   * <p>
   * Represents the routing id.
   * </p>
   */
  @NotNull
  private Integer routingId;

}
