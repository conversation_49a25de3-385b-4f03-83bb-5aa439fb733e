/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.server.api.domain.enums;

import com.jdx.rover.server.api.domain.guardian.VehicleOtaInfoEntity;
import com.jdx.rover.server.api.domain.guardian.VehicleServiceInfoEntity;
import com.jdx.rover.server.api.domain.guardian.VehicleVersionInfoEntity;
import com.jdx.rover.server.api.domain.guardian.VehicleVersionInfoInterface;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

import java.util.function.Supplier;

/**
 * 客户端类型.
 *
 * <AUTHOR>
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum OtaMoudleEnum {
  ROVER("rover", "rover", "Rover", VehicleOtaInfoEntity::new),
  ROVERCONF("rover-conf", "rover-conf", "RoverConf", VehicleOtaInfoEntity::new),
  MAP("jdmap", "map", "地图", VehicleOtaInfoEntity::new),
  VIDEO("hawk", "video", "视频", VehicleServiceInfoEntity::new),
  ANDROID("caterpillar", "android", "安卓", VehicleServiceInfoEntity::new);

  private String value;

  private String name;

  private String description;

  private final Supplier<VehicleVersionInfoInterface> constructor;

  public static OtaMoudleEnum of(final String value) {
    for (OtaMoudleEnum itemEnum : OtaMoudleEnum.values()) {
      if (itemEnum.value.equals(value)) {
        return itemEnum;
      }
    }
    return null;
  }

}
