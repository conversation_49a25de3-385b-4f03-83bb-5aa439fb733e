/*
 * Copyright (c) 2023 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.server.api.domain.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 无保护左转入参对象
 *
 * <AUTHOR>
 * @date 2023/3/24
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString(callSuper = true)
public class SwitchLightCommandVO extends RemoteCommandVO {

    /**
     * 开关灯状态
     */
    private String switchLight;


    /**
     * 开关灯类型
     */
    private String switchType;

}
