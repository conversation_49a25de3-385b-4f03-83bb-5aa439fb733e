package com.jdx.rover.server.api.jsf.service.vehicle;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.server.api.domain.dto.guardian.ModuleVersionInfoDTO;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleLauchStateInfoDTO;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleLocationAndPowerDTO;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleLocationDTO;
import com.jdx.rover.server.api.domain.dto.guardian.VehiclePncInfoDTO;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleRealtimeWheelInfoDTO;
import com.jdx.rover.server.api.domain.dto.vehicle.GridDataDTO;
import com.jdx.rover.server.api.domain.vo.vehicle.GridVO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface ServerVehicleService {

    /**
     * 获取车辆实时位置
     */
    HttpResult<VehicleLocationDTO> getVehicleLocation(String vehicleName);

    /**
     * 获取车辆实时位置和电量信息
     */
    HttpResult<VehicleLocationAndPowerDTO> getLocationAndPower(String vehicleName);

    /**
     * 获取车辆实时胎压信息
     */
    HttpResult<List<VehicleRealtimeWheelInfoDTO>> getWheelInfo(String vehicleName);

    /**
     * 获取车辆初始化进度信息
     */
    HttpResult<VehicleLauchStateInfoDTO> getVehicleStartInitStateInfo(String vehicleName);

    /**
     * 获取车辆当前版本信息
     */
    HttpResult<Map<String, ModuleVersionInfoDTO>> getVehicleOtaVersionInfo(String vehicleName);

    /**
     * 获取车辆实PNC信息
     */
    HttpResult<VehiclePncInfoDTO> getVehiclePnc(String vehicleName);

    /**
     * 获取车辆实格口信息
     */
    HttpResult<GridDataDTO> getGridStatus(GridVO gridVO);
}