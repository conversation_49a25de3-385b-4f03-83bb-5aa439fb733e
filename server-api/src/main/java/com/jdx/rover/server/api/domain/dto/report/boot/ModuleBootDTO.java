package com.jdx.rover.server.api.domain.dto.report.boot;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 模块启动信息
 *
 * <AUTHOR>
 * @date 2023/5/9
 */
@Data
public class ModuleBootDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 启动次数
     */
    private Integer bootCount;

    /**
     * 模块启动状态
     */
    private List<ModuleBootDetailDTO> moduleBootDetailList;
}
