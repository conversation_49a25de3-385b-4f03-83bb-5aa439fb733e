/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.server.api.domain.dto.guardian;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.Map;

/**
 * 车辆异常
 *
 * <AUTHOR>
 */
@Data
public class VehicleAbnormalDTO implements Serializable {
  private static final long serialVersionUID = 1L;
  /**
   * 车辆名称
   */
  private String vehicleName;

  /**
   * 记录时间
   */
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  private Date recordTime;

  /**
   * 异常明细
   */
  private Map<String, VehicleAbnormalDetailDTO> abnormalMap;
}
