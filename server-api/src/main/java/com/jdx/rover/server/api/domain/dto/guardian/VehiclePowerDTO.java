/*
 * Copyright (c) 2021-2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.server.api.domain.dto.guardian;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 电量消息
 *
 * <AUTHOR>
 */
@Data
public class VehiclePowerDTO implements Serializable {
  private static final long serialVersionUID = 1L;

  /**
   * 车号
   */
  private String vehicleName;

  /**
   * 电量
   */
  private Double power;

  /**
   * 记录时间
   */
  private Date recordTime;
}
