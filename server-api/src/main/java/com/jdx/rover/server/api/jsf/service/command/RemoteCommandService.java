package com.jdx.rover.server.api.jsf.service.command;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.server.api.domain.vo.PassNoSignalIntersectionCommandVO;
import com.jdx.rover.server.api.domain.vo.RemoteCommandVO;
import com.jdx.rover.server.api.domain.vo.RemoteControlCommandVO;
import com.jdx.rover.server.api.domain.vo.RemoteResetAbnormalCommandVO;
import com.jdx.rover.server.api.domain.vo.RemoteTrafficLightCommandVO;
import com.jdx.rover.server.api.domain.vo.MaxVelocityCommandVO;
import com.jdx.rover.server.api.domain.vo.SwitchLightCommandVO;
import com.jdx.rover.server.api.domain.vo.SwitchVehicleModeCommandVO;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * @description: RemoteCommandService
 * @author: wangguotai
 * @create: 2024-09-26 21:38
 **/
public interface RemoteCommandService {

    /**
     * 急停
     */
    HttpResult<Void> publishEmergencyStopCommand(@NotNull(message = "请求参数不能为空") @Valid RemoteCommandVO remoteCommandVo);

    /**
     * 视同到达
     */
    HttpResult<Void> publishAsArrivedCommand(@NotNull(message = "请求参数不能为空") @Valid RemoteCommandVO remoteCommandVo);

    /**
     * 恢复
     */
    HttpResult<Void> publishRecoveryCommand(@NotNull(message = "请求参数不能为空") @Valid RemoteCommandVO remoteCommandVo);

    /**
     * 重启
     */
    HttpResult<Void> publishRestartCommand(@NotNull(message = "请求参数不能为空") @Valid RemoteCommandVO remoteCommandVo);

    /**
     * 远程控制
     */
    HttpResult<Void> publishRemoteControlCommand(@NotNull(message = "请求参数不能为空") @Valid RemoteControlCommandVO remoteControlCommandVo);

    /**
     * 重置异常
     */
    HttpResult<Void> publishResetAbnormalCommand(@NotNull(message = "请求参数不能为空") @Valid RemoteResetAbnormalCommandVO remoteResetAbnormalCommandVo);

    /**
     * 红绿灯控制
     */
    HttpResult<Void> publishControlTrafficLightCommand(@NotNull(message = "请求参数不能为空") @Valid RemoteTrafficLightCommandVO remoteTrafficLightCommandVo);

    /**
     * 大灯控制
     */
    HttpResult<Void> publishControlLampCommand(@NotNull(message = "请求参数不能为空") @Valid RemoteCommandVO remoteCommandVO);

    /**
     * 解除急停
     */
    HttpResult<Void> publishRelieveButtonStopCommand(@NotNull(message = "请求参数不能为空") @Valid RemoteCommandVO remoteCommandVO);

    /**
     * 通过无信号路口
     */
    HttpResult<Void> passNoSignalIntersection(@NotNull(message = "请求参数不能为空") @Valid PassNoSignalIntersectionCommandVO passNoSignalIntersectionCommandVO);

    /**
     * 切换地图有图模式
     */
    HttpResult<Void> runHaveMap(@NotNull(message = "请求参数不能为空") @Valid RemoteCommandVO remoteCommandVO);

    /**
     * 切换地图无图模式
     */
    HttpResult<Void> runNoMap(@NotNull(message = "请求参数不能为空") @Valid RemoteCommandVO remoteCommandVO);

    /**
     * 灯光控制
     */
    HttpResult<Void> switchLight(@NotNull(message = "请求参数不能为空") @Valid SwitchLightCommandVO switchLightCommandVO);

    /**
     * 开启地图采集模式
     *
     * @param remoteCommandVO remoteCommandVO
     * @return 指令下发结果
     */
    HttpResult<Void> openMapCollection(@NotNull(message = "请求参数不能为空") @Valid RemoteCommandVO remoteCommandVO);

    /**
     * 切换车辆标定模式
     *
     * @param switchVehicleModeCommandVo switchVehicleModeCommandVo
     * @return 指令下发结果
     */
    HttpResult<Void> switchVehicleMode(@NotNull(message = "请求参数不能为空") @Valid SwitchVehicleModeCommandVO switchVehicleModeCommandVo);

    /**
     * 下发车辆限速指令
     *
     * @param maxVelocityCommandVO speedLimitCommandVO
     * @return 指令下发结果
     */
    HttpResult<Void> publishMaxVelocity(@NotNull(message = "请求参数不能为空") @Valid MaxVelocityCommandVO maxVelocityCommandVO);
}