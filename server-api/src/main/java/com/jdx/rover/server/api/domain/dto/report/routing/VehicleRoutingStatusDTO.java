package com.jdx.rover.server.api.domain.dto.report.routing;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * 启动详情
 *
 * <AUTHOR>
 * @date 2023/5/9
 */
@Data
public class VehicleRoutingStatusDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 车辆名称
     */
    private String vehicleName;

    /**
     * 硬件启动ID
     */
    private Long runtimeUuid;

    /**
     * 结果类型
     */
    private String resultCodeType;

    /**
     * 结果消息
     */
    private String resultCodeMsg;

    /**
     * 路由ID
     */
    private Integer routingId;

    /**
     * 规划里程,单位米
     */
    private Double globalMileage;

    /**
     * 规划停靠点列表
     */
    private List<StopResult> stopResultList;

    /**
     * 当前停靠点索引
     */
    private Integer currentStopIndex;

    /**
     * 距离下一个停靠点里程
     */
    private Double mileageToNextStop;

    /**
     * naviRefreshId
     */
    private Integer naviRefreshId;

    /**
     * 序列号
     */
    private Integer naviSequenceNum;

    /**
     * 任务状态
     */
    private String missionStatus;

    /**
     * 上报时间：单位纳秒（用于心跳计算运行时长）
     */
    private Long reportTime;

    @Data
    public static class ResultCode {
        /**
         * 纬度
         */
        private String type;

        /**
         * 经度
         */
        private String msg;
    }

    @Data
    public static class StopResult {
        /**
         * 停靠点ID
         */
        private Integer stopId;

        /**
         * 路由里程
         */
        private Double routingMileage;

        /**
         * 路由规划点
         */
        private List<RoutingPosition> routingPositionList;
    }

    @Data
    public static class RoutingPosition {
        /**
         * 纬度
         */
        private Double longitude;

        /**
         * 经度
         */
        private Double latitude;
    }

    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    @ToString
    public enum Type {
        UNKNOWN_TYPE,
        OK,
        RECEIVE_NAVIGATION_COMMAND,
        RECEIVE_MOTION_COMMAND,
        PROCESSING,
        FAILED,
        CANCELLED,
        ;
    }

    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    @ToString
    public enum Msg {
        UNKNOWN_MSG,
        PROCESSING_SMOTHING,
        PROCESSING_ROUTE,
        FAILED_START_TOO_FAY_AWAY_FROM_LANE,
        FAILED_END_TOO_FAY_AWAY_FROM_LANE,
        ;
    }

    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    @ToString
    public enum MissionStatus {
        UNKNOWN,
        RUNNING,
        PAUSE,
        CANCELLED,
        SMOOTHING,
        NAVIGATING,
        AS_ARRIVED,
        ;;
    }
}
