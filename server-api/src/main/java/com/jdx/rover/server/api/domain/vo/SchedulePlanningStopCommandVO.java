/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.server.api.domain.vo;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <p>
 * This is a view object for schedule planning routing command.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class SchedulePlanningStopCommandVO {

  /**
   * <p>
   * Represents the routing stop id.
   * </p>
   */
  @NotNull
  private Integer stopId;

  /**
   * <p>
   * Represents the stop index.
   * </p>
   */
  @NotNull
  private Integer index;


  /**
   * <p>
   * Represents the stop lat.
   * </p>
   */
  @NotNull
  private Double lat;

  /**
   * <p>
   * Represents the stop lon.
   * </p>
   */
  @NotNull
  private Double lon;

  /**
   * <p>
   * Represents the stop heading.
   * </p>
   */
  @NotNull
  private Double heading;

  /**
   * <p>
   * Represents the stop enable.
   * </p>
   */
  private List<SchedulePlanningSpotCommandVO> spot;

}
