/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.server.api.domain.vo;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 电源管理VO
 * 
 * <AUTHOR>
 */
@Data
public class PowerManagerCommandVO {
  /**
   * 车辆名称
   */
  @NotBlank
  private String vehicleName;

  /**
   * 模块名称,见PowerManagerModuleEnum枚举
   */
  @NotBlank
  private String moduleName;

  /**
   * 操作名称
   */
  @NotBlank
  private String action;

  /**
   * 精确到纳秒的时间戳
   */
  @NotNull
  private Long requestTime;
}
