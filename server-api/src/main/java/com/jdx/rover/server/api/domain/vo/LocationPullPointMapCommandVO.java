/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.server.api.domain.vo;

import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotNull;

/**
 * <p>
 * 请求车端定位模块拉取激光点云地图帧
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Data
@ToString(callSuper=true)
public class LocationPullPointMapCommandVO extends RemoteCommandVO {

  /**
   * <p>
   * 是否需要上传
   * </p>
   */
  @NotNull
  private Boolean need_upload;

}
