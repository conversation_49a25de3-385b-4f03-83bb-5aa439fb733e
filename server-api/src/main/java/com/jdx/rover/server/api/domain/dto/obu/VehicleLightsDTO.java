/*
 * Copyright (c) 2021-2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.server.api.domain.dto.obu;

import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 上报OBU车辆灯光状态数据
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/11/19
 */
@Data
public class VehicleLightsDTO implements Serializable {
    /**
     * 用于实现序列化的版本号。
     */
    private static final long serialVersionUID = 1L;

    /**
     * 此字段为true时，以下灯态均无效
     */
    private Boolean invalidLight;
    /**
     * 近光灯
     */
    private Boolean lowBeam;
    /**
     * 远光灯
     */
    private Boolean highBeam;
    /**
     * 左转灯
     */
    private Boolean turnLeft;
    /**
     * 右转灯
     */
    private Boolean turnRight;
    /**
     * 双闪灯
     */
    private Boolean warningFlash;
    /**
     * 自动灯光控制
     */
    private Boolean autoLightControl;
    /**
     * 白天行车灯
     */
    private Boolean dayRunning;
    /**
     * 雾灯_前
     */
    private Boolean fogFront;
    /**
     * 停车灯
     */
    private Boolean stop;
    /**
     * 喇叭
     */
    private Boolean horn;
    /**
     * 雾灯_后
     */
    private Boolean fogBack;
    /**
     * 刹车灯
     */
    private Boolean brake;
    /**
     * 倒车灯
     */
    private Boolean reversing;

}
