/*
 * Copyright (c) 2021-2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.server.api.domain.dto.guardian;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 告警事件
 *
 * <AUTHOR> shiling
 * @version 1.0
 */
@Data
public class VehicleAlarmEventDTO implements Serializable {
  private static final long serialVersionUID = 1L;

  /**
   * 报警类型
   */
  private String type;

  /**
   * 上报时间
   */
  private Date reportTime;

  /**
   * 错误代码
   */
  private String errorCode;

  /**
   * 错误级别
   */
  private String errorLevel;

  /**
   * 错误消息
   */
  private String errorMessage;

  /**
   * 结束时间
   */
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  private Date endTime;

  /**
   * 硬件启动ID
   */
  private String runtimeUuid;
}
