package com.jdx.rover.server.api.domain.dto.hardware.status;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * SIM卡状态信息数据传输对象
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/8/21
 */
@Data
public class SimStatusDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 在线状态
     * <p>0-未知，1-离线，2-在线</p>
     */
    private Integer onlineStatus;

    /**
     * 上线时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    private Date onlineTime;

    /**
     * 离线时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    private Date offlineTime;

    /**
     * 是否主卡
     */
    private Boolean mainCard;

    /**
     * 设备唯一ID
     * <p>同类设备从1开始</p>
     */
    private Integer deviceId;

    /**
     * 记录时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    private Date recordTime;
}
