/*
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.server.api.domain.dto.ws;

import lombok.Data;

/**
 * 响应头
 *
 * <AUTHOR>
 */
@Data
public class WsResponseHeader {
  /**
   * 车辆名称
   */
  private String vehicleName;

  /**
   * 请求ID,同一辆车唯一
   * 13位毫秒时间戳+6位随机数字
   *
   * @see com.jdx.rover.common.utils.request.RequestIdUtils getRequestId()
   */
  private Long requestId;

  /**
   * 请求unix时间戳
   */
  private Long requestTime;

  /**
   * 响应unix时间戳
   */
  private Long responseTime;

  /**
   * 消息状态(RUNNING,SUCCESS,FAIL)
   */
  private String messageState;

  /**
   * 失败信息
   */
  private String failInfo;
}
