/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.server.api.domain.vo;

import lombok.Data;
import lombok.ToString;

/**
 * <p>
 * This is a view object for schedule go next command.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Data
@ToString(callSuper=true)
public class ScheduleGoNextStopCommandVO extends RemoteCommandVO {

  /**
   * <p>
   * Represents the routing id.
   * </p>
   */
  private Integer routingId;

  /**
   * <p>
   * Represents the next index.
   * </p>
   */
  private Integer nextIndex;

}
