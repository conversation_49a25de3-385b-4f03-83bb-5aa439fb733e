package com.jdx.rover.server.api.domain.dto.vehicle;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 格口状态DTO
 *
 * <AUTHOR>
 */
@Data
public class GridDataDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 车辆名称
     */
    private String vehicleName;
    /**
     * 记录时间
     */
    private Date recordTime;
    /**
     * 格口列表
     */
    private List<GridDTO> gridList;
}
