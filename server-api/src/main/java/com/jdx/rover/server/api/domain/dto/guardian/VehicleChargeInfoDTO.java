/*
 * Copyright (c) 2021-2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.server.api.domain.dto.guardian;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 车辆电量信息
 *
 * <AUTHOR> shiling
 * @version 1.0
 */
@Data
public class VehicleChargeInfoDTO implements Serializable {
  private static final long serialVersionUID = 1L;

  /**
   * 车辆名称
   */
  private String vehicleName;

  /**
   * 电量
   */
  private Double power;

  /**
   * 充电状态
   */
  private String chargeState;

  /**
   * 车辆状态
   */
  private String vehicleState;

}
