/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.server.api.domain.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * 客户端类型.
 *
 * <AUTHOR>
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
@Deprecated
public enum ServiceMoudleEnum {

  VIDEO("hawk", "视频"),
  ANDROID("caterpillar", "安卓");

  private String value;

  /**
   * 中文名
   */
  private String name;

  public static ServiceMoudleEnum of(final String value) {
    for (ServiceMoudleEnum itemEnum : ServiceMoudleEnum.values()) {
      if (itemEnum.value.equals(value)) {
        return itemEnum;
      }
    }
    return null;
  }

}