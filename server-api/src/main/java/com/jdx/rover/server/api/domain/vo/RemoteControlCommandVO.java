/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.server.api.domain.vo;

import com.jdx.rover.server.api.domain.enums.RemoteCommandTypeEnum;

import lombok.Data;
import lombok.ToString;

/**
 * <p>
 * This is a view object for remote control command.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Data
@ToString(callSuper=true)
public class RemoteControlCommandVO extends RemoteCommandVO {

   /**
   * <p>
   * Represents the id of request. The default value is 0. It's changeable.
   * </p>
   */
  private Integer id;

  /**
   * <p>
   * Represents the target velocity. The default value is 0.0. It's changeable.
   * </p>
   */
  private Double targetVelocity;

  /**
   * <p>
   * Represents the target angle. The default value is 0.0. It's changeable.
   * </p>
   */
  private Double targetAngle;

  /**
   * <p>
   * Represents the name of module. It's changeable.
   * </p>
   */
  private String moduleName;

}
