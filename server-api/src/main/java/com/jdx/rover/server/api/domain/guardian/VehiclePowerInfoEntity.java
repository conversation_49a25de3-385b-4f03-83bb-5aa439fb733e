/*
 * Copyright (c) 2021-2021 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.server.api.domain.guardian;

import com.jdx.rover.server.api.domain.enums.guardian.ChargeStateEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * This is a view object class for vehicle ota info save request.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class VehiclePowerInfoEntity implements Serializable {
  private static final long serialVersionUID = 1L;

  /**
   * <p>
   * Represents the state of charge. The default value is null. It's
   * changeable.
   * </p>
   */
  private String chargeState;

  /**
   * <p>
   * Represents the cur power. The default value is null. It's changeable.
   * </p>
   */
  private Double power;

}
