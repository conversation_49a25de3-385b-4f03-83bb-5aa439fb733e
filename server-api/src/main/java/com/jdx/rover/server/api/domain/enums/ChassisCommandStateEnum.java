package com.jdx.rover.server.api.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <p>
 * This is supervisor remoteControl command type enum.
 * </p>
 *
 * <p>
 * <strong>remote control command type: </strong> enumeration of the class remote control
 * type.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Getter
@ToString
@AllArgsConstructor
public enum ChassisCommandStateEnum {
  /**
   * <p>
   * The enumerate command types.
   * </p>
   */
  OK(0), TIME_EXCEPTION(1);

  /**
   * <p>
   * The command state corresponding to the enumeration.
   * </p>
   */
  @Getter
  @Setter
  private Integer state;
}
