/*
 * Copyright (c) 2022-2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.server.api.domain.dto.status;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.ToString;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class ChangeStatusDTO {
    /**
     * 车辆名称
     */
    private String vehicleName;

    /**
     * 旧值
     */
    private String oldValue;

    /**
     * 新值
     */
    private String newValue;

    /**
     * 变化类型
     */
    private String changeType;

    /**
     * 记录时间
     */
    private Date recordTime;


    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    @ToString
    public enum ChangeTypeEnum {
        /**
         * 变化类型
         */
        RUN_MAP_STATE("RUN_MAP_STATE", "运行地图状态"),
        GPS_SIGNAL("GPS_SIGNAL", "GPS信号"),
        SCENE_SIGNAL("SCENE_SIGNAL", "定位置信度"),
        STEER_ZERO("STEER_ZERO", "车辆零偏值"),
        VEHICLE_STATE("VEHICLE_STATE", "车辆模式"),
        MAX_VELOCITY("MAX_VELOCITY", "车辆最大速度"),
        ;

        /**
         * 值
         */
        private final String value;

        /**
         * 标题描述
         */
        private final String title;

        public static ChangeTypeEnum of(final String value) {
            for (ChangeTypeEnum itemEnum : ChangeTypeEnum.values()) {
                if (itemEnum.value.equals(value)) {
                    return itemEnum;
                }
            }
            return null;
        }
    }
}
