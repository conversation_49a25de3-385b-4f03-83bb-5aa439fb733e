/*
 * Copyright (c) 2021-2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.server.api.domain.dto.localview;

import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 车辆规划红绿灯数据
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/11/19
 */
@Data
public class PlanningTrafficLightDTO implements Serializable {
    /**
     * 用于实现序列化的版本号。
     */
    private static final long serialVersionUID = 1L;

    /**
     * 红绿灯id
     */
    private String id;

    /**
     * 红绿灯来源(PERCEPTION感知/SUPERVISOR监控一键通过红绿灯)
     */
    private String source;

}
