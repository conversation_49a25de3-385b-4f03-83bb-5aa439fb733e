/*
 * Copyright (c) 2023 JD.com, Inc. All Rights Reserved.
 */
package com.jdx.rover.server.api.domain.enums.guardian;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * 通过无保护路口枚举
 *
 * <AUTHOR>
 * @date 2023/3/27
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum NoSignalIntersectionStatusEnum {
  // errorLevel
  NONE("NONE", "无"),
  READY("READY", "准备就绪"),
  RUNNING("RUNNING", "正在执行"),
  DONE("DONE", "完成"),
  ;

  /**
   * 报警类型
   */
  private final String value;
  /**
   * 标题描述
   */
  private final String title;

  public static NoSignalIntersectionStatusEnum of(final String value) {
    for (NoSignalIntersectionStatusEnum itemEnum : NoSignalIntersectionStatusEnum.values()) {
      if (itemEnum.value.equals(value)) {
        return itemEnum;
      }
    }
    return null;
  }
}
