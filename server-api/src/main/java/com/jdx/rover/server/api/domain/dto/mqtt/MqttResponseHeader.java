/*
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.server.api.domain.dto.mqtt;

import lombok.Data;

/**
 * 响应头
 *
 * <AUTHOR>
 */
@Data
public class MqttResponseHeader {
  /**
   * 车辆名称(弃用,改成clientName,兼容各种客户端)
   */
  private String vehicleName;

  /**
   * 客户端名称
   */
  private String clientName;

  /**
   * 发送用户名
   */
  private String sendName;

  /**
   * 接收用户名
   */
  private String receiveName;

  /**
   * 消息类型
   */
  private String messageType;

  /**
   * 请求ID,同一辆车唯一
   * 13位毫秒时间戳+6位随机数字
   *
   * @see com.jdx.rover.common.utils.request.RequestIdUtils getRequestId()
   */
  private Long requestId;

  /**
   * 请求unix时间戳
   */
  private Long requestTime;

  /**
   * 响应unix时间戳
   */
  private Long responseTime;

  /**
   * 消息状态(RUNNING,SUCCESS,FAIL)
   */
  private String messageState;

  /**
   * 失败信息
   */
  private String failInfo;
}
