/*
 * Copyright (c) 2021-2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.server.api.domain.dto.guardian;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import lombok.Data;

/**
 * <p>
 * This is a view object class for vehicle pnc info save request.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class VehiclePncInfoDTO implements Serializable {
  private static final long serialVersionUID = 1L;

  /**
   * 记录时间
   */
  private Date recordTime;

  /**
   * 车辆名称
   */
  private String vehicleName;

  /**
   * 规划任务类型
   */
  private String type;

  /**
   * 路径重新规划标志
   */
  private Integer naviRefreshId;

  /**
   * 规划总距离
   */
  private Double globalMileage;

  /**
   * 十字路口状态
   */
  private String intersectionStatus;

  /**
   * 红绿灯
   */
  private List<VehiclePncTrafficLightInfoDTO> trafficLightInfo;

  /**
   * 规划停靠点
   */
  private List<VehiclePncNavigationStopInfoDTO> navigationStop;

  /**
   * 通过无信号路口
   */
  private PassNoSignalIntersectionDTO passNoSignalIntersection;

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    VehiclePncInfoDTO that = (VehiclePncInfoDTO) o;
    return Objects.equals(vehicleName, that.vehicleName) &&
            Objects.equals(type, that.type) &&
            Objects.equals(naviRefreshId, that.naviRefreshId) &&
            Objects.equals(globalMileage, that.globalMileage) &&
            Objects.equals(intersectionStatus, that.intersectionStatus) &&
            Objects.equals(trafficLightInfo, that.trafficLightInfo) &&
            Objects.equals(passNoSignalIntersection, that.passNoSignalIntersection);
  }

  @Override
  public int hashCode() {
    return Objects.hash(vehicleName, naviRefreshId, globalMileage, intersectionStatus, trafficLightInfo);
  }
}
