/*
 * Copyright (c) 2023 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.server.api.domain.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 切换模式(正常、标定)
 *
 * <AUTHOR>
 * @date 2025/5/20
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString(callSuper = true)
public class SwitchVehicleModeCommandVO extends RemoteCommandVO {

    /**
     * 模式
     */
    private String switchMode;


}
