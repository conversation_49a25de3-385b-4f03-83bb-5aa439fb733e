/*
 * Copyright (c) 2020-2022 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.server.api.domain.enums.webterminal;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

import java.util.HashMap;
import java.util.Map;

/**
 * web terminal消息质量
 *
 * <AUTHOR>
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum WebTerminalMessageTypeEnum {
  UNKNOWN_INFO("UNKNOWN_INFO", "--"),
  HEARTBEAT	("HEARTBEAT", "心跳"),
  OPEN_SESSION("OPEN_SESSION", "创建会话"),
  CLOSE_SESSION("CLOSE_SESSION", "关闭会话"),
  NEW_TERMINAL("NEW_TERMINAL", "新建终端"),
  COMMAND_DATA("COMMAND_DATA", "指令内容"),
  CLOSE_TERMINAL("CLOSE_TERMINAL", "关闭终端"),
  SERVER_TO_VEHICLE_FILE("SERVER_TO_VEHICLE_FILE", "服务端发送文件到车端"),
  VEHICLE_TO_SERVER_FILE("VEHICLE_TO_SERVER_FILE", "车端发送文件到服务端"),
  ;

  private static Map<String, WebTerminalMessageTypeEnum> valueMap = new HashMap<>();

  static {
    for (WebTerminalMessageTypeEnum itemEnum : WebTerminalMessageTypeEnum.values()) {
      valueMap.put(itemEnum.value, itemEnum);
    }
  }

  /**
   * 值
   */
  private String value;

  /**
   * 标题描述
   */
  private String title;

  public static WebTerminalMessageTypeEnum of(final String value) {
    return valueMap.get(value);
  }
}
