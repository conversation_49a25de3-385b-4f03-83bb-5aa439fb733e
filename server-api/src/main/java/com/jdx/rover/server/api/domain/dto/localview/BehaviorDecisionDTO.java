/*
 * Copyright (c) 2021-2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.server.api.domain.dto.localview;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 车辆行为决策数据
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/11/19
 */
@Data
public class BehaviorDecisionDTO implements Serializable {
    /**
     * 用于实现序列化的版本号。
     */
    private static final long serialVersionUID = 1L;

    /**
     * 行为类型
     */
    private String type;

    /**
     * 行为状态
     */
    private String state;

    /**
     * 决策原因
     */
    private String reason;

    /**
     * 调试信息
     */
    private String debugInfo;

    /**
     * 障碍物列表
     */
    private List<String> interactiveObstacleId;

}
