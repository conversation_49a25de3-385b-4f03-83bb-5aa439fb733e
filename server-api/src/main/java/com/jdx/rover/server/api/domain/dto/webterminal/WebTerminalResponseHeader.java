/*
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.server.api.domain.dto.webterminal;

import com.jdx.rover.server.api.domain.dto.ws.WsResponseHeader;
import com.jdx.rover.server.api.domain.vo.webterminal.WebTerminalRequestHeader;
import lombok.Data;

/**
 * 响应头
 *
 * <AUTHOR>
 */
@Data
public class WebTerminalResponseHeader extends WsResponseHeader {
  /**
   * 终端类型
   */
  private String terminalName;


  public WebTerminalResponseHeader() {

  }
  public WebTerminalResponseHeader(WebTerminalRequestHeader requestHeader) {
    this.setTerminalName(requestHeader.getTerminalName());
    this.setVehicleName(requestHeader.getVehicleName());
    this.setRequestId(requestHeader.getRequestId());
    this.setRequestTime(requestHeader.getRequestTime());
  }
}
