/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.server.api.domain.vo;

import java.util.List;
import java.util.Map;

import javax.validation.constraints.NotNull;

import lombok.Data;
import lombok.ToString;

/**
 * <p>
 * This is a view object for schedule planning routing command.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Data
@ToString(callSuper=true)
public class SchedulePlanningRouteCommandVO extends RemoteCommandVO {

  /**
   * <p>
   * Represents the routing path.
   * </p>
   */
  @NotNull
  private List<SchedulePlanningStopCommandVO> routingPath;

}
