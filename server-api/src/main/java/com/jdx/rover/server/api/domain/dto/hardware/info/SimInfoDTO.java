package com.jdx.rover.server.api.domain.dto.hardware.info;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * SIM卡信息数据传输对象
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/8/21
 */
@Data
public class SimInfoDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 运营商
     * <p>0-未知，1-移动，2-联通，3-电信，4-广电</p>
     */
    private Integer operator;

    /**
     * IMSI/IMEI
     */
    private String imsi;

    /**
     * 模组信息
     */
    private String moduleInfo;

    /**
     * ICCID
     */
    private String iccid;

    /**
     * 是否为PDU
     * <p>true-是，false-否</p>
     */
    private Boolean pdu;

    /**
     * 设备唯一ID
     * <p>同类设备从1开始</p>
     */
    private Integer deviceId;

    /**
     * 记录时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    private Date recordTime;
}
