/*
 * Copyright (c) 2021-2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.server.api.domain.dto.obu;

import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 上报OBU行驶数据
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/11/19
 */
@Data
public class VehicleDriverDataDTO implements Serializable {
    /**
     * 用于实现序列化的版本号。
     */
    private static final long serialVersionUID = 1L;

    /**
      * 车辆编号，8位，为空时缺省
     */
    private String vehicleId;

    /**
     * GNSS时间戳
     */
    private Long timestampGnss;

    /**
     * 车辆经度，默认东经；[0,180]
     */
    private Double longitude;

    /**
     * 车辆纬度，默认北纬；[0,90]
     */
    private Double latitude;

    /**
     * 车辆当前运行模式；
     * 1.人工驾驶；2：自动驾驶；3.云端驾驶（云端计算单元控车）； 4：（不启用）
     * 5.脱离（非 AD 行程自动结束下的接管）；6.远程驾驶（非现场人工驾驶）；
     * 7.未处于任何驾驶模式 8.其他未定义状态，255.缺省
     */
    private Integer driveMode;

    /*
     * 车灯状态
     */
    private VehicleLightsDTO lights;

    /**
     * 行驶车道
     */
    private String laneType;

}
