/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.server.api.domain.vo;

import lombok.Data;
import lombok.ToString;

/**
 * <p>
 * 人工定位车辆位姿
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Data
@ToString(callSuper=true)
public class LocationManualVehiclePoseCommandVO extends RemoteCommandVO {

  /**
   * <p>
   * x位姿
   * </p>
   */
  private Double x;

  /**
   * <p>
   * y位姿
   * </p>
   */
  private Double y;

  /**
   * <p>
   * z位姿
   * </p>
   */
  private Double z;

  /**
   * <p>
   * row位姿
   * </p>
   */
  private Double roll;

  /**
   * <p>
   * yaw位姿
   * </p>
   */
  private Double yaw;

  /**
   * <p>
   * pitch位姿
   * </p>
   */
  private Double pitch;
  
  

}
