/*
 * Copyright (c) 2021-2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.server.api.domain.constants;

/**
 * kafka消息主题
 *
 * <AUTHOR>
 */
public interface KafkaTopicConstant {
  /**
   * server的guardian连接状态
   */
  String SERVER_GUARDIAN_CONNECT = "server_guardian_connect";

  /**
   * server的Guardian导航信息
   */
  String SERVER_GUARDIAN_PNC = "server_guardian_pnc";

  /**
   * server的Guardian报警信息
   */
  String SERVER_GUARDIAN_ALARM = "server_guardian_alarm";

  /**
   * server的Guardian电量信息
   */
  String SERVER_GUARDIAN_POWER = "server_guardian_power";

  /**
   * server的Guardian车辆实时信息
   */
  String SERVER_GUARDIAN_REALTIME = "server_guardian_realtime";

  /**
   * server的Guardian车端异常信息
   */
  String SERVER_GUARDIAN_ABNORMAL = "server_guardian_abnormal";

  /**
   * server的Guardian车端版本信息
   */
  String SERVER_GUARDIAN_VERSION = "server_guardian_ota_version";

  /**
   * server的校验Guardian数据错误
   */
  String SERVER_GUARDIAN_DATA_ERROR = "server_guardian_check_error";

  /**
   * server的车辆状态信息
   */
  String SERVER_GUARDIAN_VEHICLE_STATE = "server_guardian_vehicle_state";

  /**
   * server的3D信息
   */
  String SERVER_LOCAL_VIEW = "server_local_view_";

  /**
   * server的降频3D信息，1HZ
   */
  String SERVER_DECREASE_LOCAL_VIEW = "server_decrease_local_view";

  /**
   * 电源管理
   */
  String SERVER_POWER_MANAGER = "server_power_manager";

  /**
   * mqtt安卓遗嘱
   */
  String SERVER_MQTT_ANDROID_WILL = "mqtt_android_will";

  /**
   * mqtt 车端rover遗嘱
   */
  String SERVER_MQTT_ROVER_WILL = "mqtt_rover_will";

  /**
   * mqtt视频遗嘱
   */
  String SERVER_MQTT_VIDEO_WILL = "mqtt_video_will";

  /**
   * mqtt安卓指令
   */
  String SERVER_MQTT_ANDROID_COMMAND = "mqtt_android_command";

  /**
   * mqtt回复安卓指令
   */
  String SERVER_MQTT_REPLY_ANDROID_COMMAND = "mqtt_reply_android_command";

  /**
   * bus通道中obu数据
   */
  String SERVER_BUS_OBU = "server_bus_obu";

  /**
   * bus通道中feature数据
   */
  String SERVER_BUS_MONITOR_FEATURE = "server_bus_monitor_feature";

  /**
   * hermes应答消息
   */
  String SERVER_HERMES_REPLY = "server_hermes_reply";

  /**
   * mqtt安卓消息
   */
  String SERVER_MQTT_ANDROID_INFO = "mqtt_android_info";

  /**
   * mqtt回复安卓消息
   */
  String SERVER_MQTT_REPLY_ANDROID_INFO = "mqtt_reply_android_info";

  /**
   * web terminal 上行指令内容
   */
  String SERVER_WEB_TERMINAL_COMMAND_UP = "server_web_terminal_command_up";
  /**
   * web terminal 下行指令内容
   */
  String SERVER_WEB_TERMINAL_COMMAND_DOWN = "server_web_terminal_command_down";
  /**
   * report通道中boot数据
   */
  String SERVER_REPORT_BOOT = "server_report_boot";

  /**
   * report通道中驾驶模式数据
   */
  String SERVER_REPORT_DRIVE_MODE = "server_report_drive_mode";

  /**
   * report通道中异常数据
   */
  String SERVER_REPORT_ABNORMAL = "server_report_abnormal";

  /**
   * report通道中路由状态数据(不含规划路径)
   */
  String SERVER_REPORT_ROUTING_STATUS = "server_report_routing_status";

  /**
   * report通道中路由全部数据(含规划路径)
   */
  String SERVER_REPORT_ROUTING_ALL = "server_report_routing_all";

  /**
   * 端云车辆状态变化
   */
  String SERVER_VEHICLE_CHANGE_STATUS = "server_vehicle_change_status";

  /**
   * mqtt PDU指令
   */
  String PDU_COMMAND = "r_pdu_command";

  /**
   * mqtt回复PDU指令
   */
  String REPLY_PDU_COMMAND = "r_reply_pdu_command";
}
