/***************************************************************************
 *
 * Copyright (c) 2020 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.server.api.domain.enums.guardian;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * 十字路口状态
 *
 * <AUTHOR>
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum IntersectionStatusEnum {
  IN_INTERSECTION("IN_INTERSECTION", "在十字路口"),
  NEAR_INTERSECTION("NEAR_INTERSECTION", "靠近十字路口"),
  FAR_FROM_INTERSECTION("FAR_FROM_INTERSECTION", "远离十字路口"),
  ;

  private final String value;

  private final String title;
}
