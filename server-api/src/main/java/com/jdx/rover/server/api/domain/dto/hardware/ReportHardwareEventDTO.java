/*
 * Copyright (c) 2022-2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.server.api.domain.dto.hardware;

import lombok.*;

import java.util.Date;

/**
 * 车辆硬件类事件上报
 *
 * <AUTHOR>
 * @date 2025/07/15
 */
@Data
public class ReportHardwareEventDTO {
    /**
     * 车辆名称
     */
    private String vehicleName;

    /**
     * 事件详细内容
     */
    private String eventData;

    /**
     * 事件类型
     */
    private String eventType;

    /**
     * 记录时间
     */
    private Date recordTime;


    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    @ToString
    public enum EventTypeEnum {
        /**
         * 变化类型
         */
        DC_STALK_EVENT("DC_STALK_EVENT", "数采拨杆"),
        ;

        /**
         * 值
         */
        private final String value;

        /**
         * 标题描述
         */
        private final String title;

        public static EventTypeEnum of(final String value) {
            for (EventTypeEnum itemEnum : EventTypeEnum.values()) {
                if (itemEnum.value.equals(value)) {
                    return itemEnum;
                }
            }
            return null;
        }
    }
}
