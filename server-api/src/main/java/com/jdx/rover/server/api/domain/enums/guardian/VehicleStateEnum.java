/***************************************************************************
 *
 * Copyright (c) 2020 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.server.api.domain.enums.guardian;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * 车辆模式,从车端上报的底盘状态和启动状态聚合而成
 *
 * <AUTHOR>
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum VehicleStateEnum {
  UNKNOWN("UNKNOWN", ""),
  INITIALIZATION("INITIALIZATION", "初始化"),
  READY("READY", "无任务待机"),
  AUTO_DRIVE("AUTO_DRIVE", "自动驾驶"),
  ABNORMAL_STOP("ABNORMAL_STOP", "异常停车"),
  REMOTE_CONTROL("REMOTE_CONTROL", "远程控制"),
  LOCAL_CONTROL("LOCAL_CONTROL", "手柄控制"),
  GUARDIAN_TAKE_OVER("GUARDIAN_TAKE_OVER", "安全接管"),
  EMERGENCY_STOP("EMERGENCY_STOP", "车端拍停"),
  CHASSIS_FAULT("CHASSIS_FAULT", "底盘故障停车"),
  XBOX_CTRL("XBOX_CTRL", "XBOX控制"),
  CHASSIS_STANDBY("CHASSIS_STANDBY", "底盘待机"),
  BUMPER_STOP("BUMPER_STOP", "防撞停车"),
  REMOTE_SUPER_CTRL("REMOTE_SUPER_CTRL", "强制遥控"),
  REMOTE_MOBILE_CTRL("REMOTE_MOBILE_CTRL", "移动端遥控"),
  GUARDIAN_EMERGENCY("GUARDIAN_EMERGENCY", "安全急停"),
  GUARDIAN_COLLISION("GUARDIAN_COLLISION", "Guardian碰撞停车"),
  REMOTE_JOYSTICK_CTRL("REMOTE_JOYSTICK_CTRL", "平行驾驶"),
  REMOTE_JOYSTICK_SUPER_CTRL("REMOTE_JOYSTICK_SUPER_CTRL", "强制平行驾驶"),
  ;

  /**
   * 车辆模式
   */
  private String vehicleState;

  /**
   * 车辆模式名称
   */
  private String vehicleStateName;
}
