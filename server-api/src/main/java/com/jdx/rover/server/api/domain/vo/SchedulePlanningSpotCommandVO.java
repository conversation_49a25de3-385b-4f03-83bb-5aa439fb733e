/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.server.api.domain.vo;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <p>
 * This is a view object for schedule planning spot command.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class SchedulePlanningSpotCommandVO {

  /**
   * <p>
   * Represents the spot lat.
   * </p>
   */
  @NotNull
  private Double lat;

  /**
   * <p>
   * Represents the spot lon.
   * </p>
   */
  @NotNull
  private Double lon;

  /**
   * <p>
   * Represents the spot heading.
   * </p>
   */
  private Double heading;

}
