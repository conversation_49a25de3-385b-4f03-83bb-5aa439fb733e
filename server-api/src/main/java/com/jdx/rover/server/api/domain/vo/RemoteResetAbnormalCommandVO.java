/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.server.api.domain.vo;

import com.jdx.rover.server.api.domain.enums.RemoteCommandTypeEnum;

import lombok.Data;
import lombok.ToString;

/**
 * <p>
 * This is a view object for reset abnormal command.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Data
@ToString(callSuper=true)
public class RemoteResetAbnormalCommandVO extends RemoteCommandVO {

   /**
   * <p>
   * Represents the id of request. The default value is 0. It's changeable.
   * </p>
   */
  private Integer id;

  /**
   * <p>
   * Represents the command type. It's changeable.
   * </p>
   */
  private RemoteCommandTypeEnum commandType;

  /**
   * <p>
   * Represents the name of module. It's changeable.
   * </p>
   */
  private String moduleName;

}
