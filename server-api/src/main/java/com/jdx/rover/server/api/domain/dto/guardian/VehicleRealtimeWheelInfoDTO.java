/*
 * Copyright (c) 2021-2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.server.api.domain.dto.guardian;

import java.io.Serializable;
import lombok.Data;

/**
 * 胎压信息
 *
 * <AUTHOR> shiling
 * @version 1.0
 */
@Data
public class VehicleRealtimeWheelInfoDTO implements Serializable {
  private static final long serialVersionUID = 1L;

  /**
   * 车轮位置
   */
  private String wheelPosition;

  /**
   * 胎压状态（高 超高 低 超低）
   */
  private String pressureStatus;

  /**
   * 胎压值
   */
  private Integer pressure;
}
