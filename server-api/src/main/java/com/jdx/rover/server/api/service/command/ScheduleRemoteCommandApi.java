/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.server.api.service.command;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.server.api.domain.vo.ScheduleGoNextStopCommandVO;
import com.jdx.rover.server.api.domain.vo.SchedulePlanningRouteCommandVO;
import com.jdx.rover.server.api.domain.vo.ScheduleRestRoutingCommandVO;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <p>
 * This is api interface for schedule command.
 * </p>
 *
 * <p>
 * <strong>Thread Safety: </strong> This class is mutable and not thread safe. But it will be used
 * as entity so it'll not cause any thread safe problem.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
public interface ScheduleRemoteCommandApi {

  /**
   * <p>
   * Post go next stop command.
   * </p>
   *
   */
  @GetMapping("/server/stream/schedule_command/hermes_check")
  HttpResult checkHermesLink(@RequestParam(value = "vehicleName") String vehicleName);

  /**
   * <p>
   * Post go next stop command.
   * </p>
   * 
   */
  @PostMapping("/server/stream/schedule_command/go_next")
  HttpResult publishGoNextStopCommand(@RequestBody ScheduleGoNextStopCommandVO goNextStopCommand);


  /**
   * <p>
   * Post reset routing command.
   * </p>
   * 
   */
  @PostMapping("/server/stream/schedule_command/reset_route")
  HttpResult publishResetRoutingCommand(@RequestBody ScheduleRestRoutingCommandVO resetRoutingCommand);

  /**
   * <p>
   * Post reset routing command.
   * </p>
   * 
   */
  @PostMapping("/server/stream/schedule_command/plan_route")
  HttpResult publishPlanningRoutingCommand(@RequestBody SchedulePlanningRouteCommandVO planningRouteCommand);

}
