package com.jdx.rover.server.api.jsf.service.command;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.server.api.domain.vo.ScheduleGoNextStopCommandVO;
import com.jdx.rover.server.api.domain.vo.SchedulePlanningRouteCommandVO;
import com.jdx.rover.server.api.domain.vo.ScheduleRestRoutingCommandVO;

/**
 * ScheduleRemoteCommandService
 */
public interface ScheduleRemoteCommandService {

    /**
     * <p>
     * Post go next stop command.
     * </p>
     */
    HttpResult<Void> checkHermesLink(String vehicleName);

    /**
     * <p>
     * Post go next stop command.
     * </p>
     */
    HttpResult<Void> publishGoNextStopCommand(ScheduleGoNextStopCommandVO goNextStopCommand);

    /**
     * <p>
     * Post reset routing command.
     * </p>
     */
    HttpResult<Void> publishResetRoutingCommand(ScheduleRestRoutingCommandVO resetRoutingCommand);

    /**
     * <p>
     * Post reset routing command.
     * </p>
     */
    HttpResult<Void> publishPlanningRoutingCommand(SchedulePlanningRouteCommandVO planningRouteCommand);
}