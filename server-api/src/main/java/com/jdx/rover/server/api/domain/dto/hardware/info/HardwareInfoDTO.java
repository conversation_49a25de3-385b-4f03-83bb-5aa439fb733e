package com.jdx.rover.server.api.domain.dto.hardware.info;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 硬件基本信息数据传输对象
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/8/21
 */
@Data
public class HardwareInfoDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 车号
     */
    private String vehicleName;

    /**
     * 路由器基础信息
     */
    private BaseDeviceInfoDTO routerInfo;
    /**
     * sim卡基础信息(含pdu基础信息)
     */
    private List<SimInfoDTO> simInfoList;
    /**
     * 组合惯导基础信息
     */
    private List<BaseDeviceInfoDTO> gnssInfoList;
    /**
     * 域控基础信息
     */
    private List<BaseDeviceInfoDTO> domainInfoList;
    /**
     * 雷达基础信息
     */
    private List<LidarInfoDTO> lidarInfoList;
    /**
     * 相机基础信息
     */
    private List<CameraInfoDTO> cameraInfoList;
    /**
     * 底盘基础信息
     */
    private BaseDeviceInfoDTO chassisInfo;
    /**
     * 安卓基础信息
     */
    private BaseDeviceInfoDTO androidInfo;

    /**
     * 记录时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    private Date recordTime;
}
