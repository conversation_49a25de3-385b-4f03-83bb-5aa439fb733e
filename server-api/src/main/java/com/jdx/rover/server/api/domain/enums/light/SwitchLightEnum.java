/*
 * Copyright (c) 2020-2022 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.server.api.domain.enums.light;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * 开关灯状态
 *
 * <AUTHOR>
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum SwitchLightEnum {
    /**
     * 开关灯状态
     */
    UNKNOWN("未知"),
    TURN_ON("开灯"),
    TURN_OFF("关灯"),
    ;

    /**
     * 标题描述
     */
    private final String title;
}
