package com.jdx.rover.server.api.domain.dto.report.boot;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 启动详情
 *
 * <AUTHOR>
 * @date 2023/5/9
 */
@Data
public class NodeBootDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 节点名称
     */
    private String nodeName;

    /**
     * 节点IP
     */
    private String ip;

    /**
     * 启动开始时间：单位纳秒
     */
    private Long startTime;

    /**
     * 启动结束时长：单位纳秒
     */
    private Long endTime;

    /**
     * 启动状态
     */
    private String bootStatus;

    /**
     * 模块启动状态
     */
    private List<ModuleBootDTO> moduleBootList;

    /**
     * 硬件模块状态
     */
    private ModuleBootDTO hardwareModule;
}
