package com.jdx.rover.server.api.domain.dto.report.drivemode;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 启动详情
 *
 * <AUTHOR>
 * @date 2023/5/9
 */
@Data
public class VehicleDriveModeDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 车辆名称
     */
    private String vehicleName;

    /**
     * 硬件启动ID
     */
    private Long runtimeUuid;

    /**
     * 驾驶模式
     */
    private String driveMode;

    /**
     * 开始时间：单位纳秒
     */
    private Long startTime;

    /**
     * 结束时间：单位纳秒
     */
    private Long endTime;

    /**
     * 上报时间：单位纳秒（用于心跳计算运行时长）
     */
    private Long reportTime;

    /**
     * 节点启动状态
     */
    private List<Point> pointList;

    @Data
    public static class Point {
        /**
         * 纬度
         */
        private Double longitude;

        /**
         * 经度
         */
        private Double latitude;
    }
}
