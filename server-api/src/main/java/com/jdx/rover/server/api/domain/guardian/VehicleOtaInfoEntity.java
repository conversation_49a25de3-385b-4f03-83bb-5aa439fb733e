/*
 * Copyright (c) 2021-2021 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.server.api.domain.guardian;

import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * This is a view object class for vehicle ota info save request.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class VehicleOtaInfoEntity implements VehicleVersionInfoInterface {

  @Autowired
  public VehicleVersionInfoEntity convert(String moduleName, String curVersion, String otaVersion) {
    VehicleVersionInfoEntity otaInfoEntity = new VehicleVersionInfoEntity();
    otaInfoEntity.setModuleName(moduleName);
    otaInfoEntity.setCurVersion(curVersion);
    otaInfoEntity.setOtaVersion(otaVersion);
    return otaInfoEntity;
  }

}
