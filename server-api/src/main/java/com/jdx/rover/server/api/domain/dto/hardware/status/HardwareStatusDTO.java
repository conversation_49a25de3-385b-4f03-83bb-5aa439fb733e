package com.jdx.rover.server.api.domain.dto.hardware.status;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 硬件状态信息数据传输对象
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/8/21
 */
@Data
public class HardwareStatusDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 车号
     */
    private String vehicleName;

    /**
     * 域控启动信息数据传输对象
     */
    private List<DomainBootDTO> domainBootList;

    /**
     * 激光雷达状态信息数据传输对象
     */
    private List<LidarStatusDTO> lidarStatusList;

    /**
     * SIM卡状态信息数据传输对象
     */
    private List<SimStatusDTO> simStatusList;

    /**
     * 记录时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    private Date recordTime;
}
