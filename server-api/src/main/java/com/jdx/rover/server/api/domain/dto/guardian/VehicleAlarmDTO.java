/*
 * Copyright (c) 2021-2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.server.api.domain.dto.guardian;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * <p>
 * This is a view object class for vehicle alarm info save request.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class VehicleAlarmDTO implements Serializable {
  private static final long serialVersionUID = 1L;

  /**
   * 车号
   */
  private String vehicleName;

  /**
   * 记录时间
   */
  private Date recordTime;

  /**
   * 报警事件列表
   */
  private List<VehicleAlarmEventDTO> alarmEventList;

}
