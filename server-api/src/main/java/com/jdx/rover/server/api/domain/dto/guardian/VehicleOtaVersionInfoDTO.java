/*
 * Copyright (c) 2021-2021 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.server.api.domain.dto.guardian;

import com.jdx.rover.server.api.domain.guardian.VehicleVersionInfoEntity;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * This is a view object class for vehicle version info save request.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class VehicleOtaVersionInfoDTO implements Serializable {
  private static final long serialVersionUID = 1L;

  /**
   * <p>
   * Represents the name of bundle. The default value is null. It's
   * changeable.
   * </p>
   */
  private String vehicleName;

  /**
   * <p>
   * Represents the version list. The default value is null. It's changeable.
   * </p>
   */
  private List<VehicleVersionInfoEntity> version;

}
