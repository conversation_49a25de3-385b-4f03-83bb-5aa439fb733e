package com.jdx.rover.server.api.domain.dto.report.abnormal;

import lombok.Data;

import java.io.Serializable;

/**
 * 异常详情
 *
 * <AUTHOR>
 * @date 2023/5/9
 */
@Data
public class VehicleAbnormalDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 车辆名称
     */
    private String vehicleName;

    /**
     * 硬件启动ID
     */
    private Long runtimeUuid;

    /**
     * 模块名称
     */
    private String moduleName;

    /**
     * 错误码
     */
    private String errorCode;

    /**
     * 错误级别
     */
    private String errorLevel;

    /**
     * 错误消息
     */
    private String errorMsg;

    /**
     * 开始时间：单位纳秒
     */
    private Long startTime;

    /**
     * 结束时间：单位纳秒
     */
    private Long endTime;

    /**
     * 上报时间：单位纳秒（用于心跳计算运行时长）
     */
    private Long reportTime;
}
