/*
 * Copyright (c) 2021-2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.server.api.domain.enums.obu;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * 车辆行驶道路类型
 * <AUTHOR>
 * @date 2024/11/19
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum LaneTypeEnum {
  /**
   * 非机动车道
   */
  BYCICLE("BYCICLE", "非机动车道"),
  /**
   * 机动车道
   */
  DRIVING("DRIVING", "机动车道"),
  /**
   * 人行道
   */
  SIDEWALK("SIDEWALK", "人行道"),
  /**
   * 城市轨道
   */
  URBAN_RAIL_TRANSIT("URBAN_RAIL_TRANSIT", "城市轨道"),
  ;

  /**
   * 值
   */
  private final String value;
  /**
   * 标题描述
   */
  private final String title;

  /**
   * 道路类型
   */
  public static LaneTypeEnum of(final String value) {
    for (LaneTypeEnum itemEnum : LaneTypeEnum.values()) {
      if (itemEnum.value.equals(value)) {
        return itemEnum;
      }
    }
    return null;
  }
}
