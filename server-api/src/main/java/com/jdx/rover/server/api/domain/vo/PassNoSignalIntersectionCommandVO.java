/*
 * Copyright (c) 2023 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.server.api.domain.vo;

import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * 无保护左转入参对象
 *
 * <AUTHOR>
 * @date 2023/3/24
 */
@Data
@ToString(callSuper = true)
public class PassNoSignalIntersectionCommandVO extends RemoteCommandVO {

    /**
     * 左转线段ID
     */
    private List<String> groupLaneIdList;


    /**
     * 是否通过
     */
    private Boolean passThrough;

}
