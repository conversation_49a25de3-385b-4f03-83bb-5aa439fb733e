/*
 * Copyright (c) 2021-2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.server.api.domain.dto.guardian;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 车辆实时位置和电量信息
 *
 * <AUTHOR>
 */
@Data
public class VehicleLocationAndPowerDTO implements Serializable {
  private static final long serialVersionUID = 1L;

  /**
   * 车辆名称
   */
  private String vehicleName;

  /**
   * 纬度
   */
  private Double lat;

  /**
   * 经度
   */
  private Double lon;

  /**
   * 朝向
   */
  private Double heading;

  /**
   * 电量
   */
  private Double power;

  /**
   * 记录时间
   */
  private Date recordTime;
}
