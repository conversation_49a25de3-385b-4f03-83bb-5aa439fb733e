/*
 * Copyright (c) 2021-2021 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.server.api.domain.guardian;

import com.jdx.rover.server.api.domain.enums.OtaMoudleEnum;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <p>
 * This is a view object class for vehicle ota info save request.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class VehicleServiceInfoEntity implements VehicleVersionInfoInterface {

  @Autowired
  public VehicleVersionInfoEntity convert(String moduleName, String curVersion, String otaVersion) {
    VehicleVersionInfoEntity otaInfoEntity = new VehicleVersionInfoEntity();
    otaInfoEntity.setModuleName(moduleName);
    if (curVersion.contains("[") && curVersion.contains("]")) {
      String version = curVersion.substring(curVersion.indexOf("[") + 1, curVersion.indexOf("]"));
      otaInfoEntity.setCurVersion(version);
      otaInfoEntity.setOtaVersion(version);
    }
    return otaInfoEntity;
  }

}
