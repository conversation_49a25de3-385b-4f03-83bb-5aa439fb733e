package com.jdx.rover.server.api.jsf.service.command;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.server.api.domain.vo.LocationManualVehiclePoseCommandVO;
import com.jdx.rover.server.api.domain.vo.LocationPullPointMapCommandVO;

/**
 * LocationRemoteCommandService
 */
public interface LocationRemoteCommandService {

    /**
     * 摄取激光点云快照
     */
    HttpResult<Void> publishGeneratePointMapCommand(LocationPullPointMapCommandVO pullPointMapCommandVo);

    /**
     * 推送人工定位位姿
     */
    HttpResult<Void> publishResetLocationPoseCommand(LocationManualVehiclePoseCommandVO manualVehiclePoseCommandVo);
}