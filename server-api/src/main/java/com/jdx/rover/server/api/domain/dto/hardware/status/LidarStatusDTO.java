package com.jdx.rover.server.api.domain.dto.hardware.status;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 激光雷达状态信息数据传输对象
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/8/21
 */
@Data
public class LidarStatusDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 待机模式
     */
    private String standbyMode;

    /**
     * 时钟同步状态
     */
    private String clockStatus;

    /**
     * 雷达位置
     */
    private String position;

    /**
     * 设备唯一ID
     * <p>同类设备从1开始</p>
     */
    private Integer deviceId;

    /**
     * 记录时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    private Date recordTime;
}
