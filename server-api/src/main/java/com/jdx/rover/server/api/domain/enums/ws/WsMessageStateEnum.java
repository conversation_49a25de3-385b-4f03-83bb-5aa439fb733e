/*
 * Copyright (c) 2020-2022 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.server.api.domain.enums.ws;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * mqtt消息质量
 *
 * <AUTHOR>
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum WsMessageStateEnum {
  INIT("INIT", "初始化"),
  RECEIVED	("RECEIVED", "已接收"),
  RUNNING("RECEIVED", "执行中"),
  SUCCESS("SUCCESS", "成功"),
  FAIL("FAIL", "失败"),
  ;

  /**
   * 值
   */
  private String value;

  /**
   * 标题描述
   */
  private String title;
}
