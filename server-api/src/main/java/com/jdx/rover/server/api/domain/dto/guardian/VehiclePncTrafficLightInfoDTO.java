/*
 * Copyright (c) 2021-2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.server.api.domain.dto.guardian;

import java.io.Serializable;

import lombok.Data;

/**
 * <p>
 * This is a view object class for vehicle pnc info save request.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class VehiclePncTrafficLightInfoDTO implements Serializable {
  private static final long serialVersionUID = 1L;

  /**
   * <p>
   * The id for the traffic light. The default value is null. It's changeable.
   * </p>
   */
  private String id;

  /**
   * <p>
   * The source for the traffic light. The default value is null. It's changeable.
   * </p>
   */
  private String trafficLightSource;

}
