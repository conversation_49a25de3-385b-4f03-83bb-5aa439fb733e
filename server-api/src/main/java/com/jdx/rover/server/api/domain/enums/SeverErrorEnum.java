/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.server.api.domain.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * 错误码枚举
 * A开头 客户端错误
 * B开头 服务端错误
 * C开头 第三方错误
 * 01 主数据 02 server
 * 01 guardian 02 Hermes
 *
 * <AUTHOR>
 * @version 1.0
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum SeverErrorEnum {
  // OK
  OK("00000", "正常"),

  // A开头,客户端错误
  GUARDIAN_ERROR("A020100", "guardian信息错误"),
  GUARDIAN_DRIVABLE_DIRECTION("A020110", "guardian使能信息缺失"),
  GUARDIAN_DRIVABLE_DIRECTION_FRONT("A020111", "guardian使能信息缺失"),
  GUARDIAN_DRIVABLE_DIRECTION_BACK("A020112", "guardian使能信息缺失"),
  GUARDIAN_DRIVABLE_DIRECTION_LEFT("A020113", "guardian使能信息缺失"),
  GUARDIAN_DRIVABLE_DIRECTION_RIGHT("A020114", "guardian使能信息缺失"),
  GUARDIAN_CHASSIS_STATUS("A020120", "guardian底盘信息缺失"),
  GUARDIAN_CHASSIS_DRIVING_MODE("A020121", "guardian行驶模式缺失"),
  GUARDIAN_ABSOLUTE_COORDINATE("A020122", "guardian位置信息缺失"),
  GUARDIAN_ABSOLUTE_COORDINATE_X("A020123", "guardian位置信息x坐标缺失"),
  GUARDIAN_ABSOLUTE_COORDINATE_Y("A020124", "guardian位置信息y坐标缺失"),

  // B开头,服务端错误
  ERROR_GET_PROVIDER_URL("B021001", "服务端错误获取远程调用URL"),

  // C开头,三方调用错误
  ERROR_CALL_SERVICE("C021001", "服务端远程调用服务异常"),
  ERROR_CALL_UPLOAD("C021002", "服务端远程调用上传异常"),
  ERROR_CALL_DOWNLOAD("C021003", "服务端远程调用下载异常"),

  ERROR_HERMES_ABSENT("C021004", "车端HERMES连接不存在"),
  ERROR_POWER_MANAGER_ABSENT("C021005", "车端电源管理连接不存在"),
  ;

  /**
   * 状态码
   */
  private String code;

  /**
   * 提示消息
   */
  private String message;
}