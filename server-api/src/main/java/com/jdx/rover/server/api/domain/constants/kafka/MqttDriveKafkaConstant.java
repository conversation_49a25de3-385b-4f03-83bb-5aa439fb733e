/*
 * Copyright (c) 2021-2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.server.api.domain.constants.kafka;

/**
 * 平行驾驶kafka消息主题
 *
 * <AUTHOR>
 */
public interface MqttDriveKafkaConstant {
    /**
     * 平行驾驶接管响应指令
     */
    String REPLY_R_DRIVE_CONTROL_CONNECT = "reply_r_drive_control_connect_server";

    /**
     * 平行驾驶车端状态信息群发
     */
    String DRIVE_VEHICLE_GROUP_INFO = "r_drive_vehicle_group_info";

    /**
     * 平行驾驶驾驶舱指令
     */
    String DRIVE_COCKPIT_COMMAND = "r_drive_cockpit_command";

    /**
     * ota升级进度
     */
    String OTA_PROGRESS = "mqtt_ota_progress";
    /**
     * ota转静默下载
     */
    String OTA_UPDATER_DELAY_REPLY = "mqtt_ota_updater_delay_reply";
}
