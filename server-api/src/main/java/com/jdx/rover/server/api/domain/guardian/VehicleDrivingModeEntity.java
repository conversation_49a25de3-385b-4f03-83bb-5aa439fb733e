/*
 * Copyright (c) 2021-2021 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.server.api.domain.guardian;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;

/**
 * <p>
 * This is a view object class for vehicle driving mode info save request.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class VehicleDrivingModeEntity implements Serializable {
  private static final long serialVersionUID = 1L;

  /**
   * <p>
   * Represents the record time for save current entity. The default value is null. It's
   * changeable.
   * </p>
   */
  private Date recordTime;

  /**
   * <p>
   * Represents the driving mode state for vehicle. The default value is null. It's
   * changeable.
   * </p>
   */
  private String vehicleState;

  /**
   * <p>
   * Represents the vehicle state msg for vehicle under init mode. The default value is null. It's
   * changeable.
   * </p>
   */
  private String vehicleStateMessage;

}
