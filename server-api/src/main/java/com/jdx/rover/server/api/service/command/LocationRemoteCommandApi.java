/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.server.api.service.command;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.server.api.domain.vo.LocationManualVehiclePoseCommandVO;
import com.jdx.rover.server.api.domain.vo.LocationPullPointMapCommandVO;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <p>
 * 定位模块交互指令
 * </p>
 *
 *
 * <AUTHOR>
 * @version 1.0
 */
public interface LocationRemoteCommandApi {

  /**
   * <p>
   * 摄取激光点云快照
   * </p>
   *
   */
  @PostMapping("/server/stream/location_command/get_point_map")
  HttpResult publishGeneratePointMapCommand(@RequestBody LocationPullPointMapCommandVO pullPointMapCommandVo);

  /**
   * <p>
   * 推送人工定位位姿
   * </p>
   * 
   */
  @PostMapping("/server/stream/location_command/reset_location_pose")
  HttpResult publishResetLocationPoseCommand(@RequestBody LocationManualVehiclePoseCommandVO manualVehiclePoseCommandVo);


}
