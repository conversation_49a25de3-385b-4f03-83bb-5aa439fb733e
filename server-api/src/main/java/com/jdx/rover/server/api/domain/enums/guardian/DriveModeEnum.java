/*
 * Copyright (c) 2021-2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.server.api.domain.enums.guardian;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * 驾驶模式
 *
 * <AUTHOR>
 * @version 1.0
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum DriveModeEnum {

    UNKNOWN("UNKNOWN", "UNKNOWN"),
    INITIALIZATION("INITIALIZATION", "初始化"),
    DRIVEMODE_ENGAGED_INIT("READY", "DRIVEMODE_ENGAGED_INIT"),
    DRIVEMODE_ENGAGED_AUTO_CTRL("AUTO_DRIVE", "自动驾驶"),
    DRIVEMODE_TAKEOVER_REMOTE_CTRL("REMOTE_CONTROL", "远程遥控"),
    DRIVEMODE_ENGAGED_GUARDIAN_NORMAL("ABNORMAL_STOP", "异常停车"),
    DRIVEMODE_TAKEOVER_XBOX_CTRL("XBOX_CTRL", "XBOX控制"),
    DRIVEMODE_TAKEOVER_CHASSIS_CTRL("LOCAL_CONTROL", "本地控制"),
    DRIVEMODE_TAKEOVER_EMERGENCY_STOP("EMERGENCY_STOP", "急停"),
    DRIVEMODE_ENGAGED_GUARDIAN_EMERGENCY("GUARDIAN_EMERGENCY", "安全接管"),
    DRIVEMODE_ENGAGED_GUARDIAN_FORBID_DRIVING("GUARDIAN_TAKE_OVER", "DRIVEMODE_ENGAGED_GUARDIAN_FORBID_DRIVING"),
    DRIVEMODE_ENGAGED_CHASSIS_FAULT("CHASSIS_FAULT", "底盘故障"),
    DRIVEMODE_ENGAGED_CHASSIS_STANDBY("CHASSIS_STANDBY", "DRIVEMODE_ENGAGED_CHASSIS_STANDBY"),
    DRIVEMODE_ENGAGED_CHASSIS_BUMPER("BUMPER_STOP", "碰撞"),
    DRIVEMODE_TAKEOVER_REMOTE_SUPER_CTRL("REMOTE_SUPER_CTRL", "强制远程遥控"),
    DRIVEMODE_TAKEOVER_REMOTE_MOBILE_CTRL("REMOTE_MOBILE_CTRL", "移动端遥控"),
    DRIVEMODE_ENGAGED_GUARDIAN_COLLISION("GUARDIAN_COLLISION", "guardian碰撞"),
    DRIVEMODE_TAKEOVER_REMOTE_JOYSTICK_CTRL("REMOTE_JOYSTICK_CTRL", "平行驾驶"),
    DRIVEMODE_TAKEOVER_REMOTE_JOYSTICK_SUPER_CTRL("REMOTE_JOYSTICK_SUPER_CTRL", "强制平行驾驶"),
    ;

    /**
     * 车辆模式
     */
    private String vehicleState;

    /**
     * 车辆模式名称
     */
    private String vehicleStateName;
}
