/*
 * Copyright (c) 2021-2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.server.api.domain.dto.guardian;

import java.io.Serializable;
import java.util.List;

import lombok.Data;

/**
 * 规划停靠点
 *
 * <AUTHOR> shiling
 * @version 1.0
 */
@Data
public class VehiclePncNavigationStopInfoDTO implements Serializable {
  private static final long serialVersionUID = 1L;

  /**
   * 停靠点ID
   */
  private Integer stopId;

  /**
   * 规划里程
   */
  private Double routingMileage;

  /**
   * 规划路径
   */
  private List<VehiclePncNavigationRoutingPositionDTO> routing;
}
