/***************************************************************************
 *
 * Copyright (c) 2020 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.server.api.domain.enums.guardian;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * 充电状态
 *
 * <AUTHOR>
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum ChargeStateEnum {
  UNKNOWN("UNKNOWN", ""),
  DISCHARGING("DISCHARGING", "未充电"),
  CHARGING("CHARGING", "充电中"),
  CHARGE_OK("CHARGE_OK", "充电完成")
  ;

  /**
   * 充电状态
   */
  private String chargeState;

  /**
   * 充电状态名称
   */
  private String chargeStateName;
}
