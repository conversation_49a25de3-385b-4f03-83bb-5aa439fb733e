<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright (c) 2021 www.jd.com All rights reserved.
  ~ 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
  -->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.jdx.rover</groupId>
    <artifactId>rover-server-api</artifactId>
    <version>1.1.27-BETA-SNAPSHOT</version>
    <properties>
        <jdk.version>1.8</jdk.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <maven.compiler.encoding>UTF-8</maven.compiler.encoding>
        <maven.compiler.compilerVersion>1.8</maven.compiler.compilerVersion>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.jdx.rover</groupId>
            <artifactId>rover-common</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.22</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
            <version>5.3.12</version>
        </dependency>
    </dependencies>

    <scm>
        <!--release包需要放入的nexus或者其他maven release包的仓库url地址-->
        <url>http://artifactory.jd.com/libs-releases-local/</url>
        <!--connection, developerConnection: 都是连接字符串，其中后者是具有write权限的scm连接 -->
        <!--需要打包项目的git地址-->
        <developerConnection>scm:git:*****************:jdx_rover/rover-server.git</developerConnection>
        <tag>HEAD</tag>
    </scm>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-release-plugin</artifactId>
                <version>3.0.1</version>
                <configuration>
                    <tagNameFormat>v@{project.version}</tagNameFormat>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>