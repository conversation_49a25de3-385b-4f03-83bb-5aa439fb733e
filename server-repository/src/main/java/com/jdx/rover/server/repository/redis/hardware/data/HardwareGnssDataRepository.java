/*
 * Copyright (c) 2023 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.server.repository.redis.hardware.data;

import com.jdx.rover.local.cache.autoconfigure.annotation.LocalCacheEvict;
import com.jdx.rover.server.api.domain.dto.hardware.data.GnssDataDTO;
import com.jdx.rover.server.repository.redis.RedissonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

/**
 * GNSS实时数据
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class HardwareGnssDataRepository {
    /**
     * 已发送kafka的车辆开机
     */
    private static final String CACHE_NAME = "server:hardware:gnssData:";

    /**
     * 读取
     */
    @Cacheable(value = {CACHE_NAME}, unless = "#result == null", key = "#vehicleName")
    public GnssDataDTO get(String vehicleName) {
        GnssDataDTO result = RedissonUtils.getObject(getKey(vehicleName));
        log.info("GnssDataDTO本地缓存={}", result);
        return result;
    }

    /**
     * 设置
     */
    @LocalCacheEvict(value = CACHE_NAME, key = "#dto.vehicleName")
    public void set(GnssDataDTO dto) {
        RedissonUtils.setObject(getKey(dto.getVehicleName()), dto);
    }

    /**
     * 获取redis key
     *
     * @param vehicleName 车号
     */
    public static String getKey(String vehicleName) {
        return CACHE_NAME + vehicleName;
    }
}
