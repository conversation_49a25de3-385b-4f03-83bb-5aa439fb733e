/*
 * Copyright (c) 2023 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.server.repository.redis.hardware;

import com.jdx.rover.local.cache.autoconfigure.annotation.LocalCacheEvict;
import com.jdx.rover.server.api.domain.dto.hardware.info.HardwareInfoDTO;
import com.jdx.rover.server.repository.redis.RedissonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

/**
 * 硬件基本信息
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class HardwareInfoRepository {
    /**
     * 已发送kafka的车辆开机
     */
    private static final String CACHE_NAME = "server:hardware:info:";

    /**
     * 读取
     */
    @Cacheable(value = {CACHE_NAME}, unless = "#result == null", key = "#vehicleName")
    public HardwareInfoDTO get(String vehicleName) {
        HardwareInfoDTO result = RedissonUtils.getObject(getKey(vehicleName));
        log.info("HardwareInfoDTO本地缓存={}", result);
        return result;
    }

    /**
     * 设置
     */
    @LocalCacheEvict(value = CACHE_NAME, key = "#dto.vehicleName")
    public void set(HardwareInfoDTO dto) {
        RedissonUtils.setObject(getKey(dto.getVehicleName()), dto);
    }

    /**
     * 获取redis key
     *
     * @param vehicleName 车号
     */
    public static String getKey(String vehicleName) {
        return CACHE_NAME + vehicleName;
    }
}
