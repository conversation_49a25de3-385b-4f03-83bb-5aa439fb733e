/*
 * Copyright (c) 2021-2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.server.repository.feign.vehicle;

import com.jdx.rover.metadata.api.service.vehicle.VehicleMonitorApi;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * 车辆客户端
 *
 * <AUTHOR>
 */
//@FeignClient(contextId = "metadata-vehicle",value = "rover-metadata",url = "127.0.0.1:8001")
@FeignClient(contextId = "metadata-vehicle",value = "metadata-web")
public interface VehicleApiClient extends VehicleMonitorApi {
}
