/*
 * Copyright (c) 2021-2021 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.server.repository.redis.guardian;

import com.jdx.rover.common.utils.ParameterCheckUtility;
import com.jdx.rover.server.api.domain.guardian.VehicleStateInfoEntity;
import com.jdx.rover.server.repository.redis.BaseValueRepository;
import java.util.Date;
import org.springframework.stereotype.Repository;

/**
 * <p>
 * This is a vehicle state info repository which contains operations on
 * redis.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Repository
public class VehicleStateInfoRepository extends BaseValueRepository<VehicleStateInfoEntity> {

  /**
   * <p>
   * The redis cache key.
   * </p>
   */
  static final String VEHICLE_STATE_INFO = "vehicle_state_info_%s";

  /**
   * <p>
   * Set vehicle state info cache.
   * </p>
   * 
   * @param vehicleName the name of vehicle
   * @param arrivalTime the arrival time of guardian info
   * @param time        time (seconds)
   * @throws IllegalArgumentException if the argument is invalid.
   */
  public void saveValue(String vehicleName, VehicleStateInfoEntity stateInfoEntity) {
    ParameterCheckUtility.checkNotNullNorEmptyAfterTrimming(vehicleName, "vehicleName");
    ParameterCheckUtility.checkNotNull(stateInfoEntity, "stateInfoEntity");
    String key = String.format(VEHICLE_STATE_INFO, vehicleName);
    save(key, stateInfoEntity);
  }

  /**
   * <p>
   * Get vehicle state info cache.
   * </p>
   * 
   * @param vehicleName the name of vehicle
   * @throws IllegalArgumentException if the argument is invalid.
   */
  public VehicleStateInfoEntity getByVehicleName(String vehicleName) {
    ParameterCheckUtility.checkNotNullNorEmptyAfterTrimming(vehicleName, "vehicleName");
    String key = String.format(VEHICLE_STATE_INFO, vehicleName);
    return getByKey(key);
  }


}
