/*
 * Copyright (c) 2023 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.server.repository.shadow;

import com.jdx.rover.shadow.boot.starter.constants.EventNoEnum;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * 注
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/7/20
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum ShadowEventEnum {
    SERVER_VEHICLE_GUARDIAN_WS_STATE(EventNoEnum.SERVER_VEHICLE_GUARDIAN_WS_STATE.getEventNo(), "server_ws_connect_"),
    SERVER_VEHICLE_REPORT_WS_STATE(EventNoEnum.SERVER_VEHICLE_REPORT_WS_STATE.getEventNo(), "server_ws_connect_"),
    SERVER_VEHICLE_BUS_WS_STATE(EventNoEnum.SERVER_VEHICLE_BUS_WS_STATE.getEventNo(), "server_ws_connect_"),
    SERVER_VEHICLE_LOCAL_VIEW_WS_STATE(EventNoEnum.SERVER_VEHICLE_LOCAL_VIEW_WS_STATE.getEventNo(), "server_ws_connect_"),
    SERVER_VEHICLE_HERMES_WS_STATE(EventNoEnum.SERVER_VEHICLE_HERMES_WS_STATE.getEventNo(), "server_ws_connect_"),
    SERVER_VEHICLE_POWER_WS_STATE(EventNoEnum.SERVER_VEHICLE_POWER_WS_STATE.getEventNo(), "server_ws_connect_"),
    SERVER_VEHICLE_WEB_TERMINAL_WS_STATE(EventNoEnum.SERVER_VEHICLE_WEB_TERMINAL_WS_STATE.getEventNo(), "server_ws_connect_"),
    SERVER_ANDROID_MQTT_STATE(EventNoEnum.SERVER_ANDROID_MQTT_STATE.getEventNo(), "server_mqtt_connect_"),
    SERVER_VIDEO_MQTT_STATE(EventNoEnum.SERVER_VIDEO_MQTT_STATE.getEventNo(), "server_mqtt_connect_"),
    SERVER_REMOTE_DRIVE_SCREEN_MQTT_STATE(EventNoEnum.SERVER_REMOTE_DRIVE_SCREEN_MQTT_STATE.getEventNo(), "server_mqtt_connect_"),
    SERVER_REMOTE_DRIVE_CONTROL_PAD_MQTT_STATE(EventNoEnum.SERVER_REMOTE_DRIVE_CONTROL_PAD_MQTT_STATE.getEventNo(), "server_mqtt_connect_"),
    SERVER_REMOTE_DRIVE_VEHICLE_MQTT_STATE(EventNoEnum.SERVER_REMOTE_DRIVE_VEHICLE_MQTT_STATE.getEventNo(), "server_mqtt_connect_"),
    SERVER_REMOTE_DRIVE_COCKPIT_MQTT_STATE(EventNoEnum.SERVER_REMOTE_DRIVE_COCKPIT_MQTT_STATE.getEventNo(), "server_mqtt_connect_"),
    SERVER_PDU_MQTT_STATE(EventNoEnum.SERVER_PDU_MQTT_STATE.getEventNo(), "server_mqtt_connect_"),
    SERVER_VEHICLE_ROVER_BOOT_START(EventNoEnum.SERVER_VEHICLE_ROVER_BOOT_START.getEventNo(), null),
    SERVER_VEHICLE_ROVER_BOOT_SUCCESS(EventNoEnum.SERVER_VEHICLE_ROVER_BOOT_SUCCESS.getEventNo(), null),
    SERVER_VEHICLE_ROVER_BOOT_FAIL(EventNoEnum.SERVER_VEHICLE_ROVER_BOOT_FAIL.getEventNo(), null),
    SERVER_VEHICLE_ROVER_HALFWAY_BOOT_START(EventNoEnum.SERVER_VEHICLE_ROVER_HALFWAY_BOOT_START.getEventNo(), null),
    SERVER_VEHICLE_ROVER_HALFWAY_BOOT_SUCCESS(EventNoEnum.SERVER_VEHICLE_ROVER_HALFWAY_BOOT_SUCCESS.getEventNo(), null),
    SERVER_VEHICLE_ROVER_HALFWAY_BOOT_FAIL(EventNoEnum.SERVER_VEHICLE_ROVER_HALFWAY_BOOT_FAIL.getEventNo(), null),
    ;

    /**
     * 影子事件
     */
    private final String value;
    /**
     * 父级traceId前缀
     */
    private final String parentTraceIdPrefix;
}
