/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.server.repository.redis.guardian;

import com.jdx.rover.server.api.domain.dto.report.abnormal.VehicleAbnormalDTO;
import com.jdx.rover.server.domain.enums.RedisCacheEnum;
import com.jdx.rover.server.repository.redis.RedissonUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RMap;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 车辆异常
 *
 * <AUTHOR>
 * @version 1.0
 */
@Repository
public class ReportAbnormalRepository {
    /**
     * 读取异常
     *
     * @param vehicleName 车辆名称
     */
    public Map<String, VehicleAbnormalDTO> get(String vehicleName) {
        String key = RedisCacheEnum.REPORT_ABNORMAL_HASH.getKey(vehicleName);
        RMap<String, VehicleAbnormalDTO> rMap = RedissonUtils.getMap(key);
        Map<String, VehicleAbnormalDTO> exceptionLogMap = rMap.readAllMap();
        return exceptionLogMap;
    }

    /**
     * 读取所有异常
     */
    public Map<String, Map<String, VehicleAbnormalDTO>> getAll() {
        Map<String, Map<String, VehicleAbnormalDTO>> result = new HashMap<>();
        Iterable<String> iterable = RedissonUtils.getRedissonClient().getKeys().getKeysByPattern(RedisCacheEnum.REPORT_ABNORMAL_HASH.getValue() + "*", 1000);
        for (String key : iterable) {
            RMap<String, VehicleAbnormalDTO> rMap = RedissonUtils.getMap(key);
            Map<String, VehicleAbnormalDTO> exceptionLogMap = rMap.readAllMap();
            String vehicleName = StringUtils.removeStart(key, RedisCacheEnum.REPORT_ABNORMAL_HASH.getValue());
            result.put(vehicleName, exceptionLogMap);
        }
        return result;
    }

    /**
     * 保存
     *
     * @param vehicleName
     * @param exceptionLogMap
     */
    public void save(String vehicleName, Map<String, VehicleAbnormalDTO> exceptionLogMap) {
        String key = RedisCacheEnum.REPORT_ABNORMAL_HASH.getKey(vehicleName);
        RedissonUtils.getMap(key).putAll(exceptionLogMap);
    }


    /**
     * 通过对象保存
     *
     * @param list
     */
    public void save(List<VehicleAbnormalDTO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        Map<String, VehicleAbnormalDTO> abnormalMap = list.stream().collect(Collectors.toMap(
                ReportAbnormalRepository::getHashKeyByDTO,
                Function.identity(),
                (key1, key2) -> key1
        ));
        String vehicleName = list.get(0).getVehicleName();
        String key = RedisCacheEnum.REPORT_ABNORMAL_HASH.getKey(vehicleName);
        RedissonUtils.getMap(key).putAll(abnormalMap);
    }

    /**
     * 通过错误码删除
     *
     * @param vehicleName
     * @param errorCode
     */
    public VehicleAbnormalDTO remove(String vehicleName, String errorCode, String moduleName) {
        String key = RedisCacheEnum.REPORT_ABNORMAL_HASH.getKey(vehicleName);
        RMap<String, VehicleAbnormalDTO> rMap = RedissonUtils.getMap(key);
        String hashKey = getHashKey(errorCode, moduleName);
        VehicleAbnormalDTO result = rMap.remove(hashKey);
        return result;
    }

    /**
     * 通过对象删除
     *
     * @param list
     */
    public long remove(List<VehicleAbnormalDTO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return 0;
        }
        String vehicleName = list.get(0).getVehicleName();
        String key = RedisCacheEnum.REPORT_ABNORMAL_HASH.getKey(vehicleName);
        RMap<String, VehicleAbnormalDTO> rMap = RedissonUtils.getMap(key);
        List<String> hashKeyList = list.stream().map(ReportAbnormalRepository::getHashKeyByDTO)
                .collect(Collectors.toList());
        long result = rMap.fastRemove(hashKeyList.toArray(new String[0]));
        return result;
    }

    /**
     * 通过错误码和模块名称获取hash key
     *
     * @param errorCode
     * @param moduleName
     * @return
     */
    public static String getHashKey(String errorCode, String moduleName) {
        return errorCode + ":" + moduleName;
    }

    /**
     * 通过对象获取hash key
     *
     * @param dto
     * @return
     */
    public static String getHashKeyByDTO(VehicleAbnormalDTO dto) {
        return getHashKey(dto.getErrorCode(), dto.getModuleName());
    }
}
