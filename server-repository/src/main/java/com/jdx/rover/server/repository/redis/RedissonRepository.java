/*
 * Copyright (c) 2021-2021 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.server.repository.redis;

import java.util.concurrent.TimeUnit;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RBucket;
import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
 * Redisson工具类(依赖注入版本)
 *
 * <AUTHOR>
 */
@Repository
@Slf4j
public class RedissonRepository {
  @Autowired
  private RedissonClient redissonClient;

  /**
   * 获取Bucket
   */
  public <T> RBucket<T> getRBucket(String key) {
    RBucket<T> bucket = redissonClient.getBucket(key);
    return bucket;
  }

  /**
   * 保存数据
   */
  public <T> void setObject(String key, T object) {
    RBucket<T> bucket = getRBucket(key);
    bucket.set(object);
  }

  /**
   * 保存数据（失效时间）
   */
  public <T> void setObject(String key, T object, long expire) {
    RBucket<T> bucket = getRBucket(key);
    bucket.set(object);
    bucket.expire(expire, TimeUnit.SECONDS);
  }

  /**
   * 获取数据
   */
  public <T> T getObject(String key) {
    RBucket<T> bucket = getRBucket(key);
    return bucket.get();
  }

  /**
   * 删除数据
   */
  public <T> boolean deleteObject(String key) {
    RBucket<T> bucket = getRBucket(key);
    return bucket.delete();
  }

  /**
   * 保存map数据
   */
  public <K, V> void setMapObject(String key, K mapKey, V mapObject) {
    RMap<K, V> map = getMap(key);
    map.put(mapKey, mapObject);
  }

  /**
   * 获保存map数据（失效时间）
   */
  public <K, V> void setMapObject(String key, K mapKey, V mapObject, long expire) {
    RMap<K, V> map = getMap(key);
    map.put(mapKey, mapObject);
    map.expire(expire, TimeUnit.SECONDS);
  }

  /**
   * 获取RMAP
   */
  public <K, V> RMap<K, V> getMap(String key) {
    RMap<K, V> map = redissonClient.getMap(key);
    return map;
  }

  /**
   * 获取map数据
   */
  public <K, V> V getMapObject(String key, K mapKey) {
    RMap<K, V> map = getMap(key);
    return map.get(mapKey);
  }

  /**
   * 删除map数据
   */
  public <K, V> boolean deleteMap(String key) {
    RMap<K, V> map = getMap(key);
    return map.delete();
  }

  /**
   * 获取RAtomicLong
   */
  public RAtomicLong getAtomicLong(String key) {
    RAtomicLong rAtomicLong = redissonClient.getAtomicLong(key);
    return rAtomicLong;
  }

  /**
   * 自增原子long
   */
  public void setAtomicLongNum(String key, long num, long expire) {
    try {
      RAtomicLong rAtomicLong = getAtomicLong(key);
      if (rAtomicLong.isExists()) {
        rAtomicLong.addAndGet(num);
      } else {
        rAtomicLong.set(num);
        rAtomicLong.expire(expire, TimeUnit.SECONDS);
      }
    } catch (Exception e) {
      log.error("redisson setAtomicLongNum error!key={}", key, e);
    }
  }

  /**
   * 获取原子long
   */
  public long getAtomicLongNum(String key) {
    RAtomicLong bucket = getAtomicLong(key);
    return bucket.get();
  }

  public boolean tryLock(String key) {
    try {
      return redissonClient.getLock(key).tryLock();
    } catch (Exception e) {
      log.error("redisson tryLock error!key={}", key, e);
    }
    return false;
  }

  public void unLock(String key) {
    try {
      redissonClient.getLock(key).unlock();
    } catch (Exception e) {
      log.error("redisson unlock error!key={}", key, e);
    }
  }
}
