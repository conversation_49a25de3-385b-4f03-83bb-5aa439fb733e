/*
 * Copyright (c) 2021-2021 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.server.repository.redis.client;

import com.jdx.rover.server.domain.entity.Client;
import com.jdx.rover.server.repository.redis.BaseValueRepository;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 */
@Repository
public class ClientRepository extends BaseValueRepository<Client> {
  public static String createEndpointKey(final Client client) {
    StringBuilder sb = new StringBuilder();
    sb.append(client.getName());
    sb.append('@');
    sb.append(client.getType());
    return sb.toString();
  }

  public static String createEndpointKey(String name, String type) {
    StringBuilder sb = new StringBuilder();
    sb.append(name);
    sb.append('@');
    sb.append(type);
    return sb.toString();
  }
}
