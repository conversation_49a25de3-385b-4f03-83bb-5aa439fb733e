/*
 * Copyright (c) 2021-2021 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.server.repository.redis.guardian;

import com.jdx.rover.common.utils.ParameterCheckUtility;
import com.jdx.rover.local.cache.autoconfigure.annotation.LocalCacheEvict;
import com.jdx.rover.server.api.domain.guardian.VehicleVersionInfoEntity;
import com.jdx.rover.server.domain.constants.LocalCacheConstant;
import com.jdx.rover.server.domain.enums.RedisCacheEnum;
import com.jdx.rover.server.repository.redis.RedissonRepository;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Repository;

import java.util.Map;

/**
 * <p>
 * This is a vehicle service info repository which contains operations on
 * redis.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Repository
@Slf4j
public class VehicleVersionInfoRepository {

  @Autowired
  private RedissonRepository redissonRepository;

  /**
   * <p>
   * Set vehicle state info cache.
   * </p>
   * 
   * @param vehicleName the name of vehicle
   * @throws IllegalArgumentException if the argument is invalid.
   */
  @LocalCacheEvict(value = LocalCacheConstant.SERVER_VERSION, key = "#vehicleName")
  public void saveValue(String vehicleName, Map<String, VehicleVersionInfoEntity> versionInfoEntityMap) {
    ParameterCheckUtility.checkNotNullNorEmptyAfterTrimming(vehicleName, "vehicleName");
    ParameterCheckUtility.checkNotNull(versionInfoEntityMap, "versionInfoEntityMap");
    String key = RedisCacheEnum.GUARDIAN_VERSION_INFO.getValue() + vehicleName;
    redissonRepository.getMap(key).putAll(versionInfoEntityMap);
  }

  /**
   * <p>
   * Get vehicle version info cache.
   * </p>
   * 
   * @param vehicleName the name of vehicle
   * @throws IllegalArgumentException if the argument is invalid.
   */
  @Cacheable(value = {LocalCacheConstant.SERVER_VERSION}, unless = "#result == null" , key = "#vehicleName")
  public Map<String, VehicleVersionInfoEntity> getByVehicleName(String vehicleName) {
    ParameterCheckUtility.checkNotNullNorEmptyAfterTrimming(vehicleName, "vehicleName");
    String key = RedisCacheEnum.GUARDIAN_VERSION_INFO.getValue() + vehicleName;
    RMap<String, VehicleVersionInfoEntity> rMap = redissonRepository.getMap(key);
    Map<String, VehicleVersionInfoEntity> result = rMap.readAllMap();
    log.info("VehicleVersionInfoRepository本地缓存={}", result);
    return result;
  }
}
