/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.server.repository.redis.command;

import com.jdx.rover.server.domain.entity.VehicleRemoteCommandEntity;
import com.jdx.rover.server.domain.enums.RedisCacheEnum;
import com.jdx.rover.server.repository.redis.RedissonUtils;
import org.redisson.api.RMap;
import org.springframework.stereotype.Repository;

import java.util.Map;

/**
 * 车辆远程指令
 *
 * <AUTHOR>
 * @version 1.0
 */
@Repository
public class VehicleRemoteCommandRepository {
  /**
   * 读取车辆指令
   *
   * @param vehicleName 车辆名称
   */
  public VehicleRemoteCommandEntity get(String vehicleName, String commandType) {
    String key = RedisCacheEnum.SERVER_COMMAND_HASH.getKey(vehicleName);
    RMap<String, VehicleRemoteCommandEntity> rMap = RedissonUtils.getMap(key);
    VehicleRemoteCommandEntity remoteCommand = rMap.get(commandType);
    return remoteCommand;
  }

  /**
   * 保存
   *
   * @param vehicleName
   * @param commandVo
   */
  public void save(String vehicleName, VehicleRemoteCommandEntity commandVo) {
    String key = RedisCacheEnum.SERVER_COMMAND_HASH.getKey(vehicleName);
    RedissonUtils.setMapObject(key, commandVo.getCommandType(), commandVo);
  }

  /**
   * 通过对象获取hash map
   *
   * @param vehicleName
   * @return
   */
  public Map<String,VehicleRemoteCommandEntity> getAll(String vehicleName) {
    String key = RedisCacheEnum.SERVER_COMMAND_HASH.getKey(vehicleName);
    RMap<String, VehicleRemoteCommandEntity> rMap = RedissonUtils.getMap(key);
    Map<String, VehicleRemoteCommandEntity> remoteCommandMap = rMap.readAllMap();
    return remoteCommandMap;
  }
}
