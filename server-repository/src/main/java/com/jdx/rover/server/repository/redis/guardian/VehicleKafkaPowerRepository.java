/*
 * Copyright (c) 2021-2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.server.repository.redis.guardian;

import com.jdx.rover.local.cache.autoconfigure.annotation.LocalCacheEvict;
import com.jdx.rover.server.api.domain.guardian.VehiclePowerInfoEntity;
import com.jdx.rover.server.domain.constants.LocalCacheConstant;
import com.jdx.rover.server.repository.redis.RedissonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Repository;

/**
 * 车辆电量
 *
 * <AUTHOR>
 * @version 1.0
 */
@Repository
@Slf4j
public class VehicleKafkaPowerRepository {
  /**
   * 已发送kafka的车辆电量
   */
  private static final String SERVER_KAFKA_POWER = "server_kafka_power_%s";

  /**
   * 读取车辆电量信息
   *
   * @param vehicleName 车辆名称
   */
  @Cacheable(value = {LocalCacheConstant.SERVER_KAFKA_POWER}, unless = "#result == null" , key = "#vehicleName")
  public VehiclePowerInfoEntity getValue(String vehicleName) {
    String key = String.format(SERVER_KAFKA_POWER, vehicleName);
    VehiclePowerInfoEntity powerInfoEntity = RedissonUtils.getObject(key);
    log.info("VehicleKafkaPowerRepository本地缓存={}", powerInfoEntity);
    return powerInfoEntity;
  }

  /**
   * 设置电量信息
   *
   * @param vehicleName 车辆名称
   * @param power       电量
   */
  @LocalCacheEvict(value = LocalCacheConstant.SERVER_KAFKA_POWER, key = "#vehicleName")
  public void saveValue(String vehicleName, VehiclePowerInfoEntity power) {
    String key = String.format(SERVER_KAFKA_POWER, vehicleName);
    RedissonUtils.setObject(key, power, 60 * 60 * 2);
  }
}
