/*
 * Copyright (c) 2023 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.server.repository.redis.report;

import com.jdx.rover.local.cache.autoconfigure.annotation.LocalCacheEvict;
import com.jdx.rover.server.domain.dto.vehicle.ReportBootDTO;
import com.jdx.rover.server.repository.redis.RedissonUtils;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RMap;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

/**
 * 开机信息
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class ReportBootRepository {
    /**
     * 已发送kafka的车辆开机
     */
    private static final String CACHE_NAME = "server:report:boot:map";

    /**
     * 读取车辆开机信息
     *
     * @param vehicleName 车辆名称
     */
    @Cacheable(value = {CACHE_NAME}, unless = "#result == null", key = "#vehicleName")
    public ReportBootDTO get(String vehicleName) {
        RMap<String, ReportBootDTO> rMap = RedissonUtils.getMap(CACHE_NAME);
        ReportBootDTO result = rMap.get(vehicleName);
        log.info("ReportBootDTO本地缓存={}", result);
        return result;
    }

    /**
     * 设置开机信息
     *
     * @param dto 开机
     */

    @LocalCacheEvict(value = CACHE_NAME, key = "#dto.vehicleName")
    public boolean put(ReportBootDTO dto) {
        RMap<String, ReportBootDTO> rMap = RedissonUtils.getMap(CACHE_NAME);
        boolean result = rMap.fastPut(dto.getVehicleName(), dto);
        return result;
    }
}
