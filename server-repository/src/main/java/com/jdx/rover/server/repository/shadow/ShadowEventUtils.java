/*
 * Copyright (c) 2023 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.server.repository.shadow;

import cn.hutool.core.date.DateUtil;
import com.jdx.rover.common.utils.request.RequestIdUtils;
import com.jdx.rover.shadow.boot.starter.vo.EventRecordVO;

import java.util.Date;
import java.util.Objects;

/**
 * 影子事件工具类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/7/20
 */
public class ShadowEventUtils {
    /**
     * 构建连接事件
     *
     * @param vehicleName
     * @param shadowEventEnum
     * @param connectState
     * @return
     */
    public static final EventRecordVO buildConnectEvent(String vehicleName, ShadowEventEnum shadowEventEnum, String connectState) {
        EventRecordVO eventRecordVO = buildCommonEvent(vehicleName, shadowEventEnum);
        eventRecordVO.setBodyArray(new String[]{vehicleName, connectState});
        return eventRecordVO;
    }

    /**
     * 构建公共影子事件
     *
     * @param vehicleName
     * @param shadowEventEnum
     * @return
     */
    public static final EventRecordVO buildCommonEvent(String vehicleName, ShadowEventEnum shadowEventEnum) {
        EventRecordVO eventRecordVO = new EventRecordVO();
        Date now = new Date();
        if (!Objects.isNull(shadowEventEnum.getParentTraceIdPrefix())) {
            eventRecordVO.setParentTraceId(shadowEventEnum.getParentTraceIdPrefix() + vehicleName + "_" + DateUtil.formatDate(now));
        }
        eventRecordVO.setTraceId(RequestIdUtils.getRequestId());
        eventRecordVO.setVehicleName(vehicleName);
        eventRecordVO.setReportTime(now);
        eventRecordVO.setEventNo(shadowEventEnum.getValue());
        eventRecordVO.setBodyArray(new String[]{vehicleName, "断开"});
        return eventRecordVO;
    }
}
