/*
 * Copyright (c) 2021-2021 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.server.repository.redis.guardian;

import com.jdx.rover.server.api.domain.dto.guardian.VehicleAlarmEventDTO;
import com.jdx.rover.server.domain.enums.RedisCacheEnum;
import com.jdx.rover.server.repository.redis.RedissonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RMap;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 告警缓存
 *
 * <AUTHOR> shiling
 * @version 1.0
 */
@Repository
@Slf4j
public class VehicleAlarmInfoRepository {
    /**
     * 读取
     *
     * @param vehicleName 车辆名称
     */
    public Map<String, VehicleAlarmEventDTO> get(String vehicleName) {
        String key = RedisCacheEnum.SERVER_ALARM_HASH.getKey(vehicleName);
        RMap<String, VehicleAlarmEventDTO> rMap = RedissonUtils.getMap(key);
        Map<String, VehicleAlarmEventDTO> alarmMap = rMap.readAllMap();
        return alarmMap;
    }

    /**
     * 读取所有告警
     */
    public Map<String, Map<String, VehicleAlarmEventDTO>> getAll() {
        Map<String, Map<String, VehicleAlarmEventDTO>> result = new HashMap<>();
        Iterable<String> iterable = RedissonUtils.getRedissonClient().getKeys().getKeysByPattern(RedisCacheEnum.SERVER_ALARM_HASH.getValue() + "*", 1000);
        for (String key : iterable) {
            RMap<String, VehicleAlarmEventDTO> rMap = RedissonUtils.getMap(key);
            Map<String, VehicleAlarmEventDTO> alarmMap = rMap.readAllMap();
            String vehicleName = StringUtils.removeStart(key, RedisCacheEnum.SERVER_ALARM_HASH.getValue());
            result.put(vehicleName, alarmMap);
        }
        return result;
    }

    /**
     * 保存
     *
     * @param vehicleName
     * @param alarmMap
     */
    public void save(String vehicleName, Map<String, VehicleAlarmEventDTO> alarmMap) {
        String key = RedisCacheEnum.SERVER_ALARM_HASH.getKey(vehicleName);
        RedissonUtils.getMap(key).putAll(alarmMap);
    }

    /**
     * 通过错误码删除
     *
     * @param vehicleName
     * @param category
     */
    public long remove(String vehicleName, String category) {
        String key = RedisCacheEnum.SERVER_ALARM_HASH.getKey(vehicleName);
        RMap<String, VehicleAlarmEventDTO> rMap = RedissonUtils.getMap(key);
        return rMap.fastRemove(category);
    }

    /**
     * 通过错误码删除
     *
     * @param vehicleName
     * @param categoryList
     */
    public long remove(String vehicleName, List<String> categoryList) {
        String key = RedisCacheEnum.SERVER_ALARM_HASH.getKey(vehicleName);
        RMap<String, VehicleAlarmEventDTO> rMap = RedissonUtils.getMap(key);
        return rMap.fastRemove(categoryList.toArray(new String[0]));
    }
}