/*
 * Copyright (c) 2023 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.server.repository.redis.guardian;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.lang.func.Func1;
import cn.hutool.core.lang.func.LambdaUtil;
import com.jdx.rover.local.cache.autoconfigure.annotation.LocalCacheEvict;
import com.jdx.rover.server.common.utils.ParamMap;
import com.jdx.rover.server.domain.dto.vehicle.VehicleStatusDTO;
import com.jdx.rover.server.repository.redis.RedissonUtils;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RMap;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 实时信息
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class VehicleStatusRepository {

    private static final String CACHE_NAME = "vehicle:status:map:";
    private static final String FIELD_RECORD_TIME = "recordTime";

    /**
     * 通过车辆名称获取实时信息
     *
     * @param vehicleName
     * @return
     */
    @Cacheable(value = CACHE_NAME, key = "#vehicleName")
    public VehicleStatusDTO get(String vehicleName) {
        String redisKey = getKey(vehicleName);
        RMap<String, String> rMap = RedissonUtils.getMap(redisKey);
        Map<String, String> mapValue = rMap.readAllMap();
        VehicleStatusDTO result = BeanUtil.mapToBean(mapValue, VehicleStatusDTO.class, false, CopyOptions.create());
        result.setVehicleName(vehicleName);
        log.info("VehicleStatusRepository本地缓存={}", result);
        return result;
    }

    /**
     * 设置状态值
     *
     * @param vehicleName
     */
    @LocalCacheEvict(value = CACHE_NAME, key = "#vehicleName")
    public void putMapValue(String vehicleName, Func1<VehicleStatusDTO, ?> func, String value) {
        Map<String, String> paramMap = new ParamMap<VehicleStatusDTO>().addProperty(func, value).toMap();
        putAllMapObject(vehicleName, paramMap);
    }

    /**
     * 移除状态值
     *
     * @param vehicleName
     */
    @LocalCacheEvict(value = CACHE_NAME, key = "#vehicleName")
    public long fastRemoveMapKey(String vehicleName, Func1<VehicleStatusDTO, ?>... func) {
        String redisKey = getKey(vehicleName);
        List<String> fieldNameList = Arrays.stream(func).map(LambdaUtil::getFieldName).collect(Collectors.toList());
        RMap<String, String> rMap = RedissonUtils.getMap(redisKey);
        return rMap.fastRemove(fieldNameList.toArray(new String[0]));
    }

    /**
     * 保存map数据
     */
    @LocalCacheEvict(value = CACHE_NAME, key = "#vehicleName")
    public void putAllMapObject(String vehicleName, Map<String, String> mapObject) {
        mapObject.values().removeIf(Objects::isNull);
        if (Objects.isNull(mapObject.get(FIELD_RECORD_TIME))) {
            mapObject.put(FIELD_RECORD_TIME, String.valueOf(System.currentTimeMillis()));
        }
        String redisKey = getKey(vehicleName);
        RedissonUtils.putAllMapObject(redisKey, mapObject);
    }

    /**
     * 通过车辆名称删除实时信息
     *
     * @param vehicleName
     * @return
     */
    @LocalCacheEvict(value = CACHE_NAME, key = "#vehicleName")
    public boolean remove(String vehicleName) {
        String redisKey = getKey(vehicleName);
        boolean result = RedissonUtils.deleteObject(redisKey);
        return result;
    }

    /**
     * 通过车辆名称获取实时
     *
     * @param vehicleNameList
     * @return
     */
    public Map<String, VehicleStatusDTO> listMap(List<String> vehicleNameList) {
        Map<String, VehicleStatusDTO> result = new HashMap<>(vehicleNameList.size());
        for (String vehicleName : vehicleNameList) {
            VehicleStatusDTO dto = get(vehicleName);
            result.put(vehicleName, dto);
        }
        return result;
    }

    /**
     * 通过车辆名称获取实时
     *
     * @param vehicleNameList
     * @return
     */
    public List<VehicleStatusDTO> list(List<String> vehicleNameList) {
        Map<String, VehicleStatusDTO> map = listMap(vehicleNameList);
        List<VehicleStatusDTO> list = new ArrayList(map.values());
        return list;
    }

    /**
     * 获取redis key
     *
     * @param vehicleName
     * @return
     */
    public static String getKey(String vehicleName) {
        String redisKey = CACHE_NAME + vehicleName;
        return redisKey;
    }
}
