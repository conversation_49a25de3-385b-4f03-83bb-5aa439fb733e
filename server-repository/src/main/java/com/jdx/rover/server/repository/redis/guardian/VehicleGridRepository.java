/*
 * Copyright (c) 2021-2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.server.repository.redis.guardian;

import com.jdx.rover.local.cache.autoconfigure.annotation.LocalCacheEvict;
import com.jdx.rover.server.api.domain.dto.vehicle.GridDataDTO;
import com.jdx.rover.server.repository.redis.RedissonUtils;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RMap;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Repository;

/**
 * 车辆格口
 *
 * <AUTHOR>
 * @version 1.0
 */
@Repository
@Slf4j
public class VehicleGridRepository {
    /**
     * 已发送kafka的车辆格口
     */
    private static final String SERVER_VEHICLE_GRID_HASH = "server_vehicle_grid_hash";

    /**
     * 读取车辆格口信息
     *
     * @param vehicleName 车辆名称
     */
    @Cacheable(value = {"server_vehicle_grid"}, unless = "#result == null", key = "#vehicleName")
    public GridDataDTO get(String vehicleName) {
        RMap<String, GridDataDTO> rMap = RedissonUtils.getMap(SERVER_VEHICLE_GRID_HASH);
        GridDataDTO result = rMap.get(vehicleName);
        log.info("VehicleGrid本地缓存={}", result);
        return result;
    }

    /**
     * 设置格口信息
     *
     * @param dto 格口
     */

    @LocalCacheEvict(value = "server_vehicle_grid", key = "#dto.vehicleName")
    public void put(GridDataDTO dto) {
        RMap<String, GridDataDTO> rMap = RedissonUtils.getMap(SERVER_VEHICLE_GRID_HASH);
        rMap.fastPut(dto.getVehicleName(), dto);
    }
}
