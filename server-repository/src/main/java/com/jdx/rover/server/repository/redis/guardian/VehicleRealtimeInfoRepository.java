/*
 * Copyright (c) 2021-2021 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.server.repository.redis.guardian;

import com.jdx.rover.server.api.domain.constants.KafkaTopicConstant;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleRealtimeInfoDTO;
import com.jdx.rover.server.repository.redis.RedissonUtils;
import org.springframework.stereotype.Repository;

/**
 * 车辆实时信息
 *
 * <AUTHOR>
 * @version 1.0
 */
@Repository
public class VehicleRealtimeInfoRepository {
    public void put(VehicleRealtimeInfoDTO dto) {
        String redisKey = KafkaTopicConstant.SERVER_GUARDIAN_REALTIME + "_" + dto.getVehicleName();
        RedissonUtils.setObject(redisKey, dto);
    }

    public VehicleRealtimeInfoDTO get(String vehicleName) {
        String redisKey = KafkaTopicConstant.SERVER_GUARDIAN_REALTIME + "_" + vehicleName;
        VehicleRealtimeInfoDTO result = RedissonUtils.getObject(redisKey);
        return result;
    }
}
