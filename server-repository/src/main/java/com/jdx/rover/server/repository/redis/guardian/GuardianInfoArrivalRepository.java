/*
 * Copyright (c) 2021-2021 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.server.repository.redis.guardian;

import com.jdx.rover.server.repository.redis.BaseValueRepository;
import java.util.Date;
import java.util.concurrent.TimeUnit;

import com.jdx.rover.common.utils.ParameterCheckUtility;

import org.springframework.stereotype.Repository;

/**
 * <p>
 * This is a guardian info arrival repository which contains operations on
 * redis.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Repository
public class GuardianInfoArrivalRepository extends BaseValueRepository<Date> {

  /**
   * <p>
   * The redis cache key.
   * </p>
   */
  static final String GUARDIAN_INFO_ARRIVAL = "guardian_arrival_info_%s";

  /**
   * <p>
   * Set guardian info arrival cache.
   * </p>
   * 
   * @param vehicleName the name of vehicle
   * @param arrivalTime the arrival time of guardian info
   * @param time        time (seconds)
   * @throws IllegalArgumentException if the argument is invalid.
   */
  public void save(String vehicleName, Date arrivalTime) {
    ParameterCheckUtility.checkNotNullNorEmptyAfterTrimming(vehicleName, "vehicleName");
    ParameterCheckUtility.checkNotNull(arrivalTime, "arrivalTime");
    String key = String.format(GUARDIAN_INFO_ARRIVAL, vehicleName);
    save(key, arrivalTime, 10L, TimeUnit.SECONDS);
  }
}
