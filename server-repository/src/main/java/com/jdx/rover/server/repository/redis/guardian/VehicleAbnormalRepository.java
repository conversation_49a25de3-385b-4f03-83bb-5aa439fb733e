/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.server.repository.redis.guardian;

import com.jdx.rover.server.api.domain.dto.guardian.VehicleAbnormalDetailDTO;
import com.jdx.rover.server.domain.enums.RedisCacheEnum;
import com.jdx.rover.server.repository.redis.RedissonUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RMap;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.Map;

/**
 * 车辆异常
 *
 * <AUTHOR>
 * @version 1.0
 */
@Repository
public class VehicleAbnormalRepository {
  /**
   * 读取异常
   *
   * @param vehicleName 车辆名称
   */
  public Map<String, VehicleAbnormalDetailDTO> get(String vehicleName) {
    String key = RedisCacheEnum.SERVER_ABNORMAL_HASH.getKey(vehicleName);
    RMap<String, VehicleAbnormalDetailDTO> rMap = RedissonUtils.getMap(key);
    Map<String, VehicleAbnormalDetailDTO> exceptionLogMap = rMap.readAllMap();
    return exceptionLogMap;
  }

  /**
   * 读取所有异常
   */
  public Map<String, Map<String, VehicleAbnormalDetailDTO>> getAll() {
    Map<String, Map<String, VehicleAbnormalDetailDTO>> result = new HashMap<>();
    Iterable<String> iterable = RedissonUtils.getRedissonClient().getKeys().getKeysByPattern(RedisCacheEnum.SERVER_ABNORMAL_HASH.getValue() + "*", 1000);
    for (String key : iterable) {
      RMap<String, VehicleAbnormalDetailDTO> rMap = RedissonUtils.getMap(key);
      Map<String, VehicleAbnormalDetailDTO> exceptionLogMap = rMap.readAllMap();
      String vehicleName = StringUtils.removeStart(key, RedisCacheEnum.SERVER_ABNORMAL_HASH.getValue());
      result.put(vehicleName, exceptionLogMap);
    }
    return result;
  }

  /**
   * 保存
   *
   * @param vehicleName
   * @param exceptionLogMap
   */
  public void save(String vehicleName, Map<String, VehicleAbnormalDetailDTO> exceptionLogMap) {
    String key = RedisCacheEnum.SERVER_ABNORMAL_HASH.getKey(vehicleName);
    RedissonUtils.getMap(key).putAll(exceptionLogMap);
  }

  /**
   * 通过错误码删除
   *
   * @param vehicleName
   * @param errorCode
   */
  public VehicleAbnormalDetailDTO remove(String vehicleName, String errorCode, String moduleName) {
    String key = RedisCacheEnum.SERVER_ABNORMAL_HASH.getKey(vehicleName);
    RMap<String, VehicleAbnormalDetailDTO> rMap = RedissonUtils.getMap(key);
    String hashKey = getHashKey(errorCode, moduleName);
    VehicleAbnormalDetailDTO result = rMap.remove(hashKey);
    return result;
  }

  /**
   * 通过错误码和模块名称获取hash key
   *
   * @param errorCode
   * @param moduleName
   * @return
   */
  public static String getHashKey(String errorCode, String moduleName) {
    return errorCode + ":" + moduleName;
  }

  /**
   * 通过对象获取hash key
   *
   * @param dto
   * @return
   */
  public static String getHashKeyByDTO(VehicleAbnormalDetailDTO dto) {
    return getHashKey(dto.getErrorCode(), dto.getModuleName());
  }
}
