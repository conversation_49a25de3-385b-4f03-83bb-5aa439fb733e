/*
 * Copyright (c) 2021-2021 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.server.repository.redis.guardian;

import com.jdx.rover.local.cache.autoconfigure.annotation.LocalCacheEvict;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleAlarmEventDTO;
import com.jdx.rover.server.domain.constants.LocalCacheConstant;
import com.jdx.rover.server.domain.enums.RedisCacheEnum;
import com.jdx.rover.server.repository.redis.RedissonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RMap;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 告警缓存
 *
 * <AUTHOR>
 * @version 1.0
 */
@Repository
@Slf4j
public class ReportAlarmRepository {
    /**
     * 读取
     *
     * @param vehicleName 车辆名称
     */

    @Cacheable(value = {LocalCacheConstant.REPORT_ALARM_HASH}, key = "#vehicleName")
    public Map<String, VehicleAlarmEventDTO> get(String vehicleName) {
        String key = RedisCacheEnum.REPORT_ALARM_HASH.getKey(vehicleName);
        RMap<String, VehicleAlarmEventDTO> rMap = RedissonUtils.getMap(key);
        Map<String, VehicleAlarmEventDTO> alarmMap = rMap.readAllMap();
        log.info("ReportAlarmRepository本地缓存vehicleName={},alarm={}", vehicleName, alarmMap);
        return alarmMap;
    }

    /**
     * 读取所有告警
     */
    public Map<String, Map<String, VehicleAlarmEventDTO>> getAll() {
        Map<String, Map<String, VehicleAlarmEventDTO>> result = new HashMap<>();
        Iterable<String> iterable = RedissonUtils.getRedissonClient().getKeys().getKeysByPattern(RedisCacheEnum.REPORT_ALARM_HASH.getValue() + "*", 1000);
        for (String key : iterable) {
            RMap<String, VehicleAlarmEventDTO> rMap = RedissonUtils.getMap(key);
            Map<String, VehicleAlarmEventDTO> alarmMap = rMap.readAllMap();
            String vehicleName = StringUtils.removeStart(key, RedisCacheEnum.REPORT_ALARM_HASH.getValue());
            result.put(vehicleName, alarmMap);
        }
        return result;
    }

    /**
     * 保存
     *
     * @param vehicleName
     * @param alarm
     */
    @LocalCacheEvict(value = LocalCacheConstant.REPORT_ALARM_HASH, key = "#vehicleName")
    public boolean save(String vehicleName, VehicleAlarmEventDTO alarm) {
        String key = RedisCacheEnum.REPORT_ALARM_HASH.getKey(vehicleName);
        boolean result = RedissonUtils.getMap(key).fastPut(alarm.getType(), alarm);
        return result;
    }

    /**
     * 保存
     *
     * @param vehicleName
     * @param alarmList
     */
    @LocalCacheEvict(value = LocalCacheConstant.REPORT_ALARM_HASH, key = "#vehicleName")
    public void save(String vehicleName, List<VehicleAlarmEventDTO> alarmList) {
        if (CollectionUtils.isEmpty(alarmList)) {
            return;
        }
        Map<String, VehicleAlarmEventDTO> alarmMap = alarmList.stream().collect(
                Collectors.toMap(VehicleAlarmEventDTO::getType, Function.identity(), (v1, v2) -> v1));
        String key = RedisCacheEnum.REPORT_ALARM_HASH.getKey(vehicleName);
        RedissonUtils.getMap(key).putAll(alarmMap);
    }

    /**
     * 保存
     *
     * @param vehicleName
     * @param alarmMap
     */
    @LocalCacheEvict(value = LocalCacheConstant.REPORT_ALARM_HASH, key = "#vehicleName")
    public void save(String vehicleName, Map<String, VehicleAlarmEventDTO> alarmMap) {
        String key = RedisCacheEnum.REPORT_ALARM_HASH.getKey(vehicleName);
        RedissonUtils.getMap(key).putAll(alarmMap);
    }

    /**
     * 通过错误码删除
     *
     * @param vehicleName
     * @param type
     */
    @LocalCacheEvict(value = LocalCacheConstant.REPORT_ALARM_HASH, key = "#vehicleName")
    public long remove(String vehicleName, String type) {
        String key = RedisCacheEnum.REPORT_ALARM_HASH.getKey(vehicleName);
        RMap<String, VehicleAlarmEventDTO> rMap = RedissonUtils.getMap(key);
        return rMap.fastRemove(type);
    }

    /**
     * 通过错误码删除
     *
     * @param vehicleName
     * @param typeList
     */
    @LocalCacheEvict(value = LocalCacheConstant.REPORT_ALARM_HASH, key = "#vehicleName")
    public long remove(String vehicleName, List<String> typeList) {
        if (CollectionUtils.isEmpty(typeList)) {
            return 0;
        }
        String key = RedisCacheEnum.REPORT_ALARM_HASH.getKey(vehicleName);
        RMap<String, VehicleAlarmEventDTO> rMap = RedissonUtils.getMap(key);
        return rMap.fastRemove(typeList.toArray(new String[0]));
    }
}