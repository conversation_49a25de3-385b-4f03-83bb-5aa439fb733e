/*
 * Copyright (c) 2023 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.server.repository.redis.hardware.data;

import com.jdx.rover.local.cache.autoconfigure.annotation.LocalCacheEvict;
import com.jdx.rover.server.api.domain.dto.hardware.data.DomainDataDTO;
import com.jdx.rover.server.repository.redis.RedissonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 域控实时数据
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class HardwareDomainDataRepository {
    /**
     * 已发送kafka的车辆开机
     */
    private static final String CACHE_NAME = "server:hardware:domainData:";

    /**
     * 读取
     */
    @Cacheable(value = {CACHE_NAME}, unless = "#result == null", key = "#vehicleName")
    public List<DomainDataDTO> get(String vehicleName) {
        List<DomainDataDTO> result = RedissonUtils.getObject(getKey(vehicleName));
        log.info("DomainDataDTO本地缓存={}", result);
        return result;
    }

    /**
     * 设置
     */
    @LocalCacheEvict(value = CACHE_NAME, key = "#vehicleName")
    public void set(String vehicleName, List<DomainDataDTO> dtoList) {
        RedissonUtils.setObject(getKey(vehicleName), dtoList);
    }

    /**
     * 获取redis key
     *
     * @param vehicleName 车号
     */
    public static String getKey(String vehicleName) {
        return CACHE_NAME + vehicleName;
    }
}
