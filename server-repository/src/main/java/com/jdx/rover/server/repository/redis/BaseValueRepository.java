/*
 * Copyright (c) 2021 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.server.repository.redis;

import com.jdx.rover.common.utils.ParameterCheckUtility;
import java.util.concurrent.TimeUnit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;

/**
 * <AUTHOR>
 */
public abstract class BaseValueRepository<T> {
  @Autowired
  private RedisTemplate<String, Object> redisTemplate;

  /**
   * Get object by key.
   */
  public T getByKey(String key) {
    ParameterCheckUtility.checkNotNullNorEmptyAfterTrimming(key, "key");
    return (T) redisTemplate.opsForValue().get(key);
  }

  /**
   * Delete a client connection.
   */
  public Boolean delete(String key) {
    ParameterCheckUtility.checkNotNullNorEmptyAfterTrimming(key, "key");
    return redisTemplate.delete(key);
  }

  /**
   * Create a new client connection.
   */
  public void save(String key, Object object) {
    ParameterCheckUtility.checkNotNullNorEmptyAfterTrimming(key, "key");
    redisTemplate.opsForValue().set(key, object);
  }

  /**
   * Create a new client connection.
   */
  public void save(String key, Object object, long time, TimeUnit timeUnit) {
    ParameterCheckUtility.checkNotNullNorEmptyAfterTrimming(key, "key");
    ParameterCheckUtility.checkNotNull(timeUnit, "timeUnit");
    redisTemplate.opsForValue().set(key, object, time, timeUnit);
  }

}
