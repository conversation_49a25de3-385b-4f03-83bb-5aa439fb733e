/*
 * Copyright (c) 2021-2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.server.repository.redis.guardian;

import com.jdx.rover.local.cache.autoconfigure.annotation.LocalCacheEvict;
import com.jdx.rover.server.api.domain.dto.guardian.VehiclePncInfoDTO;
import com.jdx.rover.server.domain.constants.LocalCacheConstant;
import com.jdx.rover.server.domain.enums.RedisCacheEnum;
import com.jdx.rover.server.repository.redis.RedissonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Repository;

/**
 * PNC查询
 *
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@Repository
public class VehiclePncRepository {
  /**
   * 读取车辆pnc信息
   *
   * @param vehicleName 车辆名称
   */
  @Cacheable(value = {LocalCacheConstant.SERVER_PNC}, unless = "#result == null", key = "#vehicleName")
  public VehiclePncInfoDTO get(String vehicleName) {
    VehiclePncInfoDTO result = RedissonUtils.getObject(RedisCacheEnum.SERVER_GUARDIAN_PNC.getKey(vehicleName));
    if (result != null) {
      log.info("本地缓存数据PNC:{}, {}, {}, {}", vehicleName, result.getType()
          , result.getNaviRefreshId(), result.getIntersectionStatus());
    }
    return result;
  }

  /**
   * 设置pnc信息
   *
   * @param dto pnc
   */
  @LocalCacheEvict(value = LocalCacheConstant.SERVER_PNC, key = "#dto.vehicleName")
  public void save(VehiclePncInfoDTO dto) {
    RedissonUtils.setObject(RedisCacheEnum.SERVER_GUARDIAN_PNC.getKey(dto.getVehicleName()), dto);
  }
}
