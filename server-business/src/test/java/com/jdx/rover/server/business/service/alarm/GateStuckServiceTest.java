package com.jdx.rover.server.business.service.alarm;

import jdx.rover.guardian.dto.LocalView;
import org.apache.commons.collections4.queue.CircularFifoQueue;
import org.junit.jupiter.api.Test;

public class GateStuckServiceTest {

    @Test
    public void test_isAddAlarm() {
        LocalView.LocalViewDTO.Builder builder = build();
        LocalView.BehaviorDecision.Builder behaviorDecision = LocalView.BehaviorDecision.newBuilder();
        behaviorDecision.setState(LocalView.BehaviorState.CROSS_GATE_HARD);
        builder.getPlanningBuilder().getDecisionBuilder().getBehaviorDecisionListBuilder().clear();
        builder.getPlanningBuilder().getDecisionBuilder().getBehaviorDecisionListBuilder().addBehaviorDecision(0, behaviorDecision);
        LocalView.LocalViewDTO dto = builder.build();

        CircularFifoQueue<VehicleStopTimeoutService.VehicleLocation> vehicleLocationQueue = new CircularFifoQueue<>();

//        boolean result = GateStuckService.isAddAlarm(dto, vehicleLocationQueue, "JD001");
//        System.out.println(result);
    }

    private LocalView.LocalViewDTO.Builder build() {
        LocalView.LocalViewDTO.Builder builder = LocalView.LocalViewDTO.newBuilder();

        LocalView.Planning.Builder planning = LocalView.Planning.newBuilder();
        LocalView.BehaviorDecisions.Builder behaviorDecisions = LocalView.BehaviorDecisions.newBuilder();

        LocalView.PlanningTrafficLight.Builder planningTrafficLight = LocalView.PlanningTrafficLight.newBuilder();
        planningTrafficLight.setId("1");
        planning.addInterestedLight(planningTrafficLight);

        LocalView.PlanningDecision.Builder decision = LocalView.PlanningDecision.newBuilder();

        LocalView.BehaviorDecision.Builder behaviorDecision = LocalView.BehaviorDecision.newBuilder();
        behaviorDecision.setState(LocalView.BehaviorState.CROSS_GATE_HARD);
        behaviorDecisions.addBehaviorDecision(0, behaviorDecision);
        decision.setBehaviorDecisionList(behaviorDecisions);
        decision.setStopTime(70);
        decision.setIsInIntersection(false);
        planning.setDecision(decision);

        builder.setPlanning(planning);

        LocalView.TrafficLight.Builder trafficLight = LocalView.TrafficLight.newBuilder();
        trafficLight.setLight(LocalView.TrafficLight.Light.RED);
        trafficLight.setId("1");
        builder.addTrafficLight(trafficLight);

        return builder;
    }
}
