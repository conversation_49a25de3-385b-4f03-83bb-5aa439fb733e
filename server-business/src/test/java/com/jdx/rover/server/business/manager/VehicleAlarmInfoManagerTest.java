//package com.jdx.rover.server.business.manager;
//
//import com.jdx.rover.server.api.domain.constants.KafkaTopicConstant;
//import com.jdx.rover.server.api.domain.dto.guardian.VehicleAlarmDTO;
//import com.jdx.rover.server.api.domain.dto.guardian.VehicleAlarmEventDTO;
//import com.jdx.rover.server.api.domain.enums.guardian.AlarmTypeEnum;
//import com.jdx.rover.server.business.service.GuardianService;
//import com.jdx.rover.server.business.service.GuardianServiceTest;
//import com.jdx.rover.server.domain.enums.GuardianErrorEnum;
//import com.jdx.rover.server.domain.enums.GuardianErrorLevelEnum;
//import com.jdx.rover.server.domain.enums.VehicleDriveModeEnum;
//import com.jdx.rover.server.domain.vo.guardian.GuardianInfoAddVo;
//import com.jdx.rover.server.repository.redis.RedissonUtils;
//import jdx.rover.guardian.data.dto.GuardianDataDto;
//import lombok.extern.slf4j.Slf4j;
//import org.assertj.core.api.Assertions;
//import org.junit.jupiter.api.Test;
//import org.junit.jupiter.api.extension.ExtendWith;
//import org.mockito.Mock;
//import org.mockito.junit.jupiter.MockitoExtension;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.kafka.core.KafkaTemplate;
//
//import java.lang.reflect.Method;
//import java.util.ArrayList;
//import java.util.Date;
//import java.util.List;
//
//import static org.assertj.core.api.Assertions.assertThat;
//
//@ExtendWith(MockitoExtension.class)
//@SpringBootTest
//@Slf4j
//class VehicleAlarmInfoManagerTest {
//  @Autowired
//  private VehicleAlarmInfoManager vehicleAlarmInfoManager;
//  @Autowired
//  private GuardianService guardianService;
//  @Mock
//  private KafkaTemplate<String, String> kafkaTemplate;
//
//  @Test
//  void buildVehicleAlarmInfoEntity() {
//  }
//
//  @Test
//  void handle() throws Exception {
//    vehicleAlarmInfoManager.handStopAlarmException();
//    GuardianDataDto.GuardianInfoDTO info = GuardianServiceTest.buildGuardianInfoDTO();
//    GuardianInfoAddVo vo = guardianService.convertGuardianInfoProtoToVo(info);
//
//    String redisKey = getRedisKey(vo.getVehicleName());
//    RedissonUtils.deleteObject(redisKey);
//    VehicleAlarmDTO vehicleAlarmDTO = RedissonUtils.getObject(redisKey);
//    assertThat(vehicleAlarmDTO).isNull();
//
//    // 胎压告警,但是不到FATA级别
//    vo.getVehicleExceptionLogSaveRequestList().get(0).setErrorCode(GuardianErrorEnum.ERROR_DRIVER_CHASSIS_WHEEL_ALARM.getErrorCode());
//    vo.getVehicleExceptionLogSaveRequestList().get(0).setErrorLevel("other");
//    VehicleAlarmDTO vehicleAlarmPressOther = vehicleAlarmInfoManager.handle(vo);
//    VehicleAlarmDTO vehicleAlarmDbPressOther = RedissonUtils.getObject(redisKey);
//    assertThat(vehicleAlarmPressOther).isNotNull();
//    assertThat(vehicleAlarmDbPressOther).isNotNull();
//    assertThat(vehicleAlarmDbPressOther.getAlarmEventList().get(0).getType()).isEqualTo(AlarmTypeEnum.VEHICLE_STOP_FATAL.getValue());
//
//
//    // ERROR级别胎压
//    vo.getVehicleExceptionLogSaveRequestList().get(0).setErrorLevel(GuardianErrorLevelEnum.ERROR.getErrorLevel());
//    VehicleAlarmDTO vehicleAlarmPressError = vehicleAlarmInfoManager.handle(vo);
//    VehicleAlarmDTO vehicleAlarmDbPressError = RedissonUtils.getObject(redisKey);
//    assertThat(vehicleAlarmPressError).isNotNull();
//    assertThat(vehicleAlarmDbPressError).isNotNull();
//    assertThat(vehicleAlarmPressError).isEqualTo(vehicleAlarmDbPressError);
//    assertThat(vehicleAlarmDbPressError.getAlarmEventList().get(1).getType()).isEqualTo(AlarmTypeEnum.LOW_PRESSURE.getValue());
//
//    // 电量低
//    vo.getVehicleExceptionLogSaveRequestList().get(1).setErrorCode(GuardianErrorEnum.LOW_BATTERY_ERROR_CODE_TYPE.getErrorCode());
//    VehicleAlarmDTO vehicleAlarmPower = vehicleAlarmInfoManager.handle(vo);
//    VehicleAlarmDTO vehicleAlarmDbPower = RedissonUtils.getObject(redisKey);
//    assertThat(vehicleAlarmPower).isNotNull();
//    assertThat(vehicleAlarmDbPower).isNotNull();
//    assertThat(vehicleAlarmPower).isEqualTo(vehicleAlarmDbPower);
//    assertThat(vehicleAlarmDbPower.getAlarmEventList().get(0).getType()).isEqualTo(AlarmTypeEnum.LOW_BATTERY.getValue());
//
//    {
//      // 遇阻停车
//      vo.getVehicleExceptionLogSaveRequestList().get(0).setErrorCode(GuardianErrorEnum.STUCK_ERROR_CODE_TYPE.getErrorCode());
//      VehicleAlarmDTO vehicleAlarm = vehicleAlarmInfoManager.handle(vo);
//      VehicleAlarmDTO vehicleAlarmDb = RedissonUtils.getObject(redisKey);
//      assertThat(vehicleAlarm).isNotNull();
//      assertThat(vehicleAlarmDb).isNotNull();
//      assertThat(vehicleAlarm).isEqualTo(vehicleAlarmDb);
//      assertThat(vehicleAlarmDb.getAlarmEventList().get(0).getType()).isEqualTo(GuardianErrorEnum.STUCK_ERROR_CODE_TYPE.getAlarmType());
//    }
//    {
//      // 安全接管停车
//      vo.getVehicleExceptionLogSaveRequestList().get(0).setErrorCode(GuardianErrorEnum.GUARD_EMERGENCY_STOP_ERROR_CODE_TYPE.getErrorCode());
//      VehicleAlarmDTO vehicleAlarm = vehicleAlarmInfoManager.handle(vo);
//      VehicleAlarmDTO vehicleAlarmDb = RedissonUtils.getObject(redisKey);
//      assertThat(vehicleAlarm).isNotNull();
//      assertThat(vehicleAlarmDb).isNotNull();
//      assertThat(vehicleAlarm).isEqualTo(vehicleAlarmDb);
//      assertThat(vehicleAlarmDb.getAlarmEventList().get(0).getType()).isEqualTo(GuardianErrorEnum.GUARD_EMERGENCY_STOP_ERROR_CODE_TYPE.getAlarmType());
//    }
//    {
//      // 安全接管停车
//      vo.getVehicleExceptionLogSaveRequestList().get(0).setErrorCode(GuardianErrorEnum.ERROR_PLANNING_PARKING_NO_SOLUTION_TYPE.getErrorCode());
//      VehicleAlarmDTO vehicleAlarm = vehicleAlarmInfoManager.handle(vo);
//      VehicleAlarmDTO vehicleAlarmDb = RedissonUtils.getObject(redisKey);
//      assertThat(vehicleAlarm).isNotNull();
//      assertThat(vehicleAlarmDb).isNotNull();
//      assertThat(vehicleAlarm).isEqualTo(vehicleAlarmDb);
//      assertThat(vehicleAlarmDb.getAlarmEventList().get(0).getType()).isEqualTo(GuardianErrorEnum.ERROR_PLANNING_PARKING_NO_SOLUTION_TYPE.getAlarmType());
//    }
//    {
//      vo.getVehicleExceptionLogSaveRequestList().get(0).setErrorCode(GuardianErrorEnum.ERROR_PLANNING_PARKING_INVALID_GOAL_TYPE.getErrorCode());
//      VehicleAlarmDTO vehicleAlarm = vehicleAlarmInfoManager.handle(vo);
//      VehicleAlarmDTO vehicleAlarmDb = RedissonUtils.getObject(redisKey);
//      assertThat(vehicleAlarm).isNotNull();
//      assertThat(vehicleAlarmDb).isNotNull();
//      assertThat(vehicleAlarm).isEqualTo(vehicleAlarmDb);
//      assertThat(vehicleAlarmDb.getAlarmEventList().get(0).getType()).isEqualTo(GuardianErrorEnum.ERROR_PLANNING_PARKING_INVALID_GOAL_TYPE.getAlarmType());
//    }
//    {
//      vo.getVehicleExceptionLogSaveRequestList().get(0).setErrorCode(GuardianErrorEnum.ERROR_PLANNING_LEFT_TURN_FOR_SIGNAL_NO_SOLUTION_TYPE.getErrorCode());
//      VehicleAlarmDTO vehicleAlarm = vehicleAlarmInfoManager.handle(vo);
//      VehicleAlarmDTO vehicleAlarmDb = RedissonUtils.getObject(redisKey);
//      assertThat(vehicleAlarm).isNotNull();
//      assertThat(vehicleAlarmDb).isNotNull();
//      assertThat(vehicleAlarm).isEqualTo(vehicleAlarmDb);
//      assertThat(vehicleAlarmDb.getAlarmEventList().get(0).getType()).isEqualTo(GuardianErrorEnum.ERROR_PLANNING_LEFT_TURN_FOR_SIGNAL_NO_SOLUTION_TYPE.getAlarmType());
//    }
//    {
//      vo.getVehicleExceptionLogSaveRequestList().get(0).setErrorCode(GuardianErrorEnum.ERROR_PLANNING_LEFT_TURN_FOR_SIGNAL_INVALID_GOAL_TYPE.getErrorCode());
//      VehicleAlarmDTO vehicleAlarm = vehicleAlarmInfoManager.handle(vo);
//      VehicleAlarmDTO vehicleAlarmDb = RedissonUtils.getObject(redisKey);
//      assertThat(vehicleAlarm).isNotNull();
//      assertThat(vehicleAlarmDb).isNotNull();
//      assertThat(vehicleAlarm).isEqualTo(vehicleAlarmDb);
//      assertThat(vehicleAlarmDb.getAlarmEventList().get(0).getType()).isEqualTo(GuardianErrorEnum.ERROR_PLANNING_LEFT_TURN_FOR_SIGNAL_INVALID_GOAL_TYPE.getAlarmType());
//    }
//    {
//      vo.getVehicleExceptionLogSaveRequestList().get(0).setErrorCode(GuardianErrorEnum.ERROR_PLANNING_TASK_TRAFFIC_LIGHT_DECIDER_WAIT_TOO_LONG_TYPE.getErrorCode());
//      VehicleAlarmDTO vehicleAlarm = vehicleAlarmInfoManager.handle(vo);
//      VehicleAlarmDTO vehicleAlarmDb = RedissonUtils.getObject(redisKey);
//      assertThat(vehicleAlarm).isNotNull();
//      assertThat(vehicleAlarmDb).isNotNull();
//      assertThat(vehicleAlarm).isEqualTo(vehicleAlarmDb);
//      assertThat(vehicleAlarmDb.getAlarmEventList().get(0).getType()).isEqualTo(GuardianErrorEnum.ERROR_PLANNING_TASK_TRAFFIC_LIGHT_DECIDER_WAIT_TOO_LONG_TYPE.getAlarmType());
//    }
//    {
//      vo.getVehicleExceptionLogSaveRequestList().get(0).setErrorCode(GuardianErrorEnum.ERROR_PLANNING_TASK_INTERSECTION_PARKING_DECIDER_WAIT_TOO_LONG_TYPE.getErrorCode());
//      VehicleAlarmDTO vehicleAlarm = vehicleAlarmInfoManager.handle(vo);
//      VehicleAlarmDTO vehicleAlarmDb = RedissonUtils.getObject(redisKey);
//      assertThat(vehicleAlarm).isNotNull();
//      assertThat(vehicleAlarmDb).isNotNull();
//      assertThat(vehicleAlarm).isEqualTo(vehicleAlarmDb);
//      assertThat(vehicleAlarmDb.getAlarmEventList().get(0).getType()).isEqualTo(GuardianErrorEnum.ERROR_PLANNING_TASK_INTERSECTION_PARKING_DECIDER_WAIT_TOO_LONG_TYPE.getAlarmType());
//    }
//    {
//      vo.getVehicleExceptionLogSaveRequestList().get(0).setErrorCode(GuardianErrorEnum.ERROR_DRIVER_CLASS_DATA_IS_ERROR.getErrorCode());
//      VehicleAlarmDTO vehicleAlarm = vehicleAlarmInfoManager.handle(vo);
//      VehicleAlarmDTO vehicleAlarmDb = RedissonUtils.getObject(redisKey);
//      assertThat(vehicleAlarm).isNotNull();
//      assertThat(vehicleAlarmDb).isNotNull();
//      assertThat(vehicleAlarm).isEqualTo(vehicleAlarmDb);
//      assertThat(vehicleAlarmDb.getAlarmEventList().get(0).getType()).isEqualTo(GuardianErrorEnum.ERROR_DRIVER_CLASS_DATA_IS_ERROR.getAlarmType());
//    }
//    {
//      vo.getVehicleExceptionLogSaveRequestList().get(0).setErrorCode(GuardianErrorEnum.ERROR_DRIVER_CLASS_DATA_IS_ERROR.getErrorCode());
//      VehicleAlarmDTO vehicleAlarm = vehicleAlarmInfoManager.handle(vo);
//      VehicleAlarmDTO vehicleAlarmDb = RedissonUtils.getObject(redisKey);
//      assertThat(vehicleAlarm).isNotNull();
//      assertThat(vehicleAlarmDb).isNotNull();
//      assertThat(vehicleAlarm).isEqualTo(vehicleAlarmDb);
//      assertThat(vehicleAlarmDb.getAlarmEventList().get(0).getType()).isEqualTo(GuardianErrorEnum.ERROR_DRIVER_CLASS_DATA_IS_ERROR.getAlarmType());
//    }
//    {
//      vo.getVehicleExceptionLogSaveRequestList().get(0).setErrorCode(GuardianErrorEnum.ERROR_DRIVER_CHASSIS_EMG_STOP_R_BUMPER.getErrorCode());
//      VehicleAlarmDTO vehicleAlarm = vehicleAlarmInfoManager.handle(vo);
//      VehicleAlarmDTO vehicleAlarmDb = RedissonUtils.getObject(redisKey);
//      assertThat(vehicleAlarm).isNotNull();
//      assertThat(vehicleAlarmDb).isNotNull();
//      assertThat(vehicleAlarm).isEqualTo(vehicleAlarmDb);
//      assertThat(vehicleAlarmDb.getAlarmEventList().get(0).getType()).isEqualTo(GuardianErrorEnum.ERROR_DRIVER_CHASSIS_EMG_STOP_R_BUMPER.getAlarmType());
//    }
//    {
//      vo.getVehicleExceptionLogSaveRequestList().get(0).setErrorCode(GuardianErrorEnum.ERROR_DRIVER_CHASSIS_EMG_STOP_F_BUMPER.getErrorCode());
//      VehicleAlarmDTO vehicleAlarm = vehicleAlarmInfoManager.handle(vo);
//      VehicleAlarmDTO vehicleAlarmDb = RedissonUtils.getObject(redisKey);
//      assertThat(vehicleAlarm).isNotNull();
//      assertThat(vehicleAlarmDb).isNotNull();
//      assertThat(vehicleAlarmDb.getAlarmEventList().get(0).getType()).isEqualTo(GuardianErrorEnum.ERROR_DRIVER_CHASSIS_EMG_STOP_F_BUMPER.getAlarmType());
//    }
//    {
//      vo.getVehicleExceptionLogSaveRequestList().get(0).setErrorCode(GuardianErrorEnum.ERROR_DRIVER_CHASSIS_STOP_BUMPER.getErrorCode());
//      VehicleAlarmDTO vehicleAlarm = vehicleAlarmInfoManager.handle(vo);
//      VehicleAlarmDTO vehicleAlarmDb = RedissonUtils.getObject(redisKey);
//      assertThat(vehicleAlarm).isNotNull();
//      assertThat(vehicleAlarmDb).isNotNull();
//      assertThat(vehicleAlarmDb.getAlarmEventList().get(0).getType()).isEqualTo(GuardianErrorEnum.ERROR_DRIVER_CHASSIS_STOP_BUMPER.getAlarmType());
//    }
//
//    vehicleAlarmInfoManager.handle(vo);
//
//    VehicleAlarmDTO vehicleAlarmDTO2 = RedissonUtils.getObject(redisKey);
//    assertThat(vehicleAlarmDTO2).isNotNull();
//  }
//
//  @Test
//  void handle2() throws Exception {
//    GuardianDataDto.GuardianInfoDTO info = GuardianServiceTest.buildGuardianInfoDTO();
//    GuardianInfoAddVo vo = guardianService.convertGuardianInfoProtoToVo(info);
//
//    String redisKey = getRedisKey(vo.getVehicleName());
//    RedissonUtils.deleteObject(redisKey);
//    VehicleAlarmDTO vehicleAlarmDTO = RedissonUtils.getObject(redisKey);
//    assertThat(vehicleAlarmDTO).isNull();
//
//    vehicleAlarmInfoManager.handle(vo);
//
//    VehicleAlarmDTO vehicleAlarmDTO2 = RedissonUtils.getObject(redisKey);
//    assertThat(vehicleAlarmDTO2).isNotNull();
//
//
//    // 上报按钮停车
//    vo.getVehicleStatusRequest().setChassisStatus(VehicleDriveModeEnum.EMERGENCY_STOP.getDriveMode());
//    VehicleAlarmDTO vehicleAlarmButtonStop = vehicleAlarmInfoManager.handle(vo);
//    VehicleAlarmDTO vehicleAlarmDbButtonStop = RedissonUtils.getObject(redisKey);
//    assertThat(vehicleAlarmButtonStop).isNotNull();
//    assertThat(vehicleAlarmDbButtonStop).isNotNull();
//    assertThat(vehicleAlarmButtonStop).isEqualTo(vehicleAlarmDbButtonStop);
//    assertThat(vehicleAlarmDbButtonStop.getAlarmEventList().get(0).getType()).isEqualTo(AlarmTypeEnum.VEHICLE_STOP_BUTTON.getValue());
//
//    // 测试前后使能false,上报安全接管停车
//    vo.getDrivableDirectionRequest().setEnableBack(false);
//    vo.getDrivableDirectionRequest().setEnableFront(false);
//    VehicleAlarmDTO vehicleAlarmDrivable = vehicleAlarmInfoManager.handle(vo);
//    VehicleAlarmDTO vehicleAlarmDbDrivable = RedissonUtils.getObject(redisKey);
//    assertThat(vehicleAlarmDrivable).isNotNull();
//    assertThat(vehicleAlarmDbDrivable).isNotNull();
//    assertThat(vehicleAlarmDrivable).isEqualTo(vehicleAlarmDbDrivable);
//    assertThat(vehicleAlarmDbDrivable.getAlarmEventList().get(0).getType()).isEqualTo(AlarmTypeEnum.VEHICLE_STOP_GUARDIAN.getValue());
//  }
//
//  @Test
//  void updateReportTime() throws Exception {
//    Method checkParameterMethod = vehicleAlarmInfoManager.getClass().getDeclaredMethod("updateReportTime", VehicleAlarmDTO.class, VehicleAlarmDTO.class);
//    checkParameterMethod.setAccessible(true);
//    checkParameterMethod.invoke(vehicleAlarmInfoManager, new VehicleAlarmDTO(), new VehicleAlarmDTO());
//
//    Date now = new Date();
//    VehicleAlarmDTO vehicleAlarmDTO = buildVehicleAlarmDTO(AlarmTypeEnum.VEHICLE_STOP_BUTTON.getValue(), now);
//    checkParameterMethod.invoke(vehicleAlarmInfoManager, vehicleAlarmDTO, new VehicleAlarmDTO());
//
//    VehicleAlarmDTO vehicleAlarmDb = buildVehicleAlarmDTO(AlarmTypeEnum.VEHICLE_STOP_BUTTON.getValue(), now);
//    checkParameterMethod.invoke(vehicleAlarmInfoManager, new VehicleAlarmDTO(), vehicleAlarmDb);
//
//    vehicleAlarmDTO.getAlarmEventList().get(0).setReportTime(new Date(now.getTime() + 10000));
//    checkParameterMethod.invoke(vehicleAlarmInfoManager, vehicleAlarmDTO, vehicleAlarmDb);
//    Assertions.assertThat(vehicleAlarmDTO.getAlarmEventList().get(0).getReportTime()).isCloseTo(now, 1000);
//
//    vehicleAlarmDTO.getAlarmEventList().get(0).setReportTime(new Date(now.getTime() - 10000));
//    Assertions.assertThat(vehicleAlarmDTO.getAlarmEventList().get(0).getReportTime()).isCloseTo(new Date(now.getTime() - 10000), 1000);
//  }
//
//  private VehicleAlarmDTO buildVehicleAlarmDTO(String alarmType, Date reportTime) {
//    VehicleAlarmDTO vehicleAlarmDTO = new VehicleAlarmDTO();
//    vehicleAlarmDTO.setVehicleName("JD0001");
//    vehicleAlarmDTO.setRecordTime(new Date());
//    List<VehicleAlarmEventDTO> alarmEventList = new ArrayList<>();
//    VehicleAlarmEventDTO vehicleAlarmEventDTO = new VehicleAlarmEventDTO();
//    vehicleAlarmEventDTO.setReportTime(reportTime);
//    vehicleAlarmEventDTO.setType(alarmType);
//    alarmEventList.add(vehicleAlarmEventDTO);
//    vehicleAlarmDTO.setAlarmEventList(alarmEventList);
//    return vehicleAlarmDTO;
//  }
//
//  private String getRedisKey(String vehicleName) {
//    String redisKey = KafkaTopicConstant.SERVER_GUARDIAN_ALARM + "_" + vehicleName;
//    return redisKey;
//  }
//}