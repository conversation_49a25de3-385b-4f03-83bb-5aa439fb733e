/*
 * Copyright (c) 2023 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.server.business.manager.report;

import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleAlarmEventDTO;
import com.jdx.rover.server.api.domain.dto.report.boot.VehicleBootDTO;
import com.jdx.rover.server.api.domain.enums.guardian.AlarmTypeEnum;
import com.jdx.rover.server.repository.redis.guardian.ReportAlarmRepository;
import lombok.RequiredArgsConstructor;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Map;
import java.util.stream.Collectors;

/**
 * 注
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/28
 */
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@SpringBootTest
class VehicleBootAlarmManagerTest {

    private final VehicleBootAlarmManager vehicleBootAlarmManager;

    private final ReportAlarmRepository reportAlarmRepository;

    @Test
    void handStopAlarm() {
        String bootBody = "{\"vehicleName\":\"ZBJ026\",\"runtimeUuid\":1706837159099606196,\"bootId\":55,\"startTime\":1706852517875245313,\"endTime\":0,\"allBootStatus\":\"STARTING\",\"reportTime\":1706852646404566667,\"nodeBootList\":[{\"nodeName\":\"Compute\",\"ip\":\"***********\",\"startTime\":1706852518725569800,\"endTime\":1706852644891569800,\"bootStatus\":\"STARTING\",\"moduleBootList\":[{\"bootCount\":1,\"moduleBootDetailList\":[{\"name\":\"DRIVERS_LIDAR_TOP\",\"startTime\":1706852518726719304,\"endTime\":1706852519772719304,\"bootStatus\":\"START_SUCCESS\",\"errorCode\":0,\"errorMsg\":\"\"},{\"name\":\"DRIVERS_LIDAR_FRONT\",\"startTime\":1706852519774875629,\"endTime\":1706852521188875629,\"bootStatus\":\"START_SUCCESS\",\"errorCode\":0,\"errorMsg\":\"\"},{\"name\":\"DRIVERS_LIDAR_RIGHT\",\"startTime\":1706852521191337457,\"endTime\":1706852522198337457,\"bootStatus\":\"START_SUCCESS\",\"errorCode\":0,\"errorMsg\":\"\"},{\"name\":\"DRIVERS_LIDAR_LEFT\",\"startTime\":1706852522200800383,\"endTime\":1706852523208800383,\"bootStatus\":\"START_SUCCESS\",\"errorCode\":0,\"errorMsg\":\"\"},{\"name\":\"DRIVERS_IMU\",\"startTime\":1706852523211117148,\"endTime\":1706852524236117148,\"bootStatus\":\"START_SUCCESS\",\"errorCode\":0,\"errorMsg\":\"\"},{\"name\":\"DRIVERS_GPS_RAC\",\"startTime\":1706852524238466019,\"endTime\":1706852526289466019,\"bootStatus\":\"START_SUCCESS\",\"errorCode\":0,\"errorMsg\":\"\"},{\"name\":\"MAP_INITIALIZATION\",\"startTime\":1706852526293159048,\"endTime\":1706852564195159048,\"bootStatus\":\"START_SUCCESS\",\"errorCode\":0,\"errorMsg\":\"\"},{\"name\":\"LOCALIZATION_IMU\",\"startTime\":1706852564199719464,\"endTime\":1706852565210719464,\"bootStatus\":\"START_SUCCESS\",\"errorCode\":0,\"errorMsg\":\"\"},{\"name\":\"LOCALIZATION_INIT\",\"startTime\":1706852565217133090,\"endTime\":1706852574854133090,\"bootStatus\":\"START_SUCCESS\",\"errorCode\":0,\"errorMsg\":\"loc_method : History\"},{\"name\":\"LOCALIZATION_LIDAR\",\"startTime\":1706852574856955695,\"endTime\":1706852644890955695,\"bootStatus\":\"STARTING\",\"errorCode\":-4201,\"errorMsg\":\"Lidar matching is not ready.\"},{\"name\":\"LOCALIZATION_EKF\",\"startTime\":9223372036854775807,\"endTime\":0,\"bootStatus\":\"WAIT_START\",\"errorCode\":0,\"errorMsg\":\"\"},{\"name\":\"LOCALIZATION_TRANSFORM\",\"startTime\":9223372036854775807,\"endTime\":0,\"bootStatus\":\"WAIT_START\",\"errorCode\":0,\"errorMsg\":\"\"},{\"name\":\"MAP_UPDATER\",\"startTime\":9223372036854775807,\"endTime\":0,\"bootStatus\":\"WAIT_START\",\"errorCode\":0,\"errorMsg\":\"\"},{\"name\":\"DRIVERS_CAMERA360_FRONT\",\"startTime\":9223372036854775807,\"endTime\":0,\"bootStatus\":\"WAIT_START\",\"errorCode\":0,\"errorMsg\":\"\"},{\"name\":\"DRIVERS_CAMERA360_REAR\",\"startTime\":9223372036854775807,\"endTime\":0,\"bootStatus\":\"WAIT_START\",\"errorCode\":0,\"errorMsg\":\"\"},{\"name\":\"DRIVERS_LIDAR_REAR\",\"startTime\":9223372036854775807,\"endTime\":0,\"bootStatus\":\"WAIT_START\",\"errorCode\":0,\"errorMsg\":\"\"},{\"name\":\"PERC2_CLOUD\",\"startTime\":9223372036854775807,\"endTime\":0,\"bootStatus\":\"WAIT_START\",\"errorCode\":0,\"errorMsg\":\"\"},{\"name\":\"PERC2_GEO\",\"startTime\":9223372036854775807,\"endTime\":0,\"bootStatus\":\"WAIT_START\",\"errorCode\":0,\"errorMsg\":\"\"},{\"name\":\"PERC2_IMAGE_F\",\"startTime\":9223372036854775807,\"endTime\":0,\"bootStatus\":\"WAIT_START\",\"errorCode\":0,\"errorMsg\":\"\"},{\"name\":\"PERC2_IMAGE_B\",\"startTime\":9223372036854775807,\"endTime\":0,\"bootStatus\":\"WAIT_START\",\"errorCode\":0,\"errorMsg\":\"\"},{\"name\":\"PERC2_SOD\",\"startTime\":9223372036854775807,\"endTime\":0,\"bootStatus\":\"WAIT_START\",\"errorCode\":0,\"errorMsg\":\"\"},{\"name\":\"PERC2_FUSION\",\"startTime\":9223372036854775807,\"endTime\":0,\"bootStatus\":\"WAIT_START\",\"errorCode\":0,\"errorMsg\":\"\"},{\"name\":\"UNIFORM\",\"startTime\":9223372036854775807,\"endTime\":0,\"bootStatus\":\"WAIT_START\",\"errorCode\":0,\"errorMsg\":\"\"},{\"name\":\"RECORDER_LITTLE_BAG\",\"startTime\":9223372036854775807,\"endTime\":0,\"bootStatus\":\"WAIT_START\",\"errorCode\":0,\"errorMsg\":\"\"}]}],\"hardwareModule\":{\"bootCount\":1,\"moduleBootDetailList\":[{\"name\":\"SELFCHECK_CONFIG\",\"startTime\":1706840807000000000,\"endTime\":1706840807050000000,\"bootStatus\":\"START_SUCCESS\",\"errorCode\":1013,\"errorMsg\":\"CODE_HARDWARE_CONFIG_SELECT_FINISH\"},{\"name\":\"MCU_RESPONSE\",\"startTime\":1706840807000000000,\"endTime\":1706840807030000000,\"bootStatus\":\"START_SUCCESS\",\"errorCode\":1004,\"errorMsg\":\"CODE_HARDWARE_GET_MCU_RESPONSE\"},{\"name\":\"MCU_UPDATE\",\"startTime\":1706840807000000000,\"endTime\":1706840807330000000,\"bootStatus\":\"START_SUCCESS\",\"errorCode\":1005,\"errorMsg\":\"CODE_HARDWARE_MCU_FIRMWARE_UPDATE_SUCCESS\"},{\"name\":\"NETWORK_CHECK\",\"startTime\":1706840807000000000,\"endTime\":1706840807030000000,\"bootStatus\":\"START_SUCCESS\",\"errorCode\":1201,\"errorMsg\":\"CODE_HARDWARE_CLOSE_NETWORK_CHECK\"},{\"name\":\"USB_CHECK\",\"startTime\":1706840807000000000,\"endTime\":1706840807330000000,\"bootStatus\":\"START_SUCCESS\",\"errorCode\":1305,\"errorMsg\":\"CODE_HARDWARE_USB_CHIP_NUM_CHECK_OK\"},{\"name\":\"PING_CHECK\",\"startTime\":1706840808000000000,\"endTime\":1706840808210000000,\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2019,\"errorMsg\":\"network check passed\"},{\"name\":\"CLOCK_SYNC\",\"startTime\":1706840825000000000,\"endTime\":1706840841970000000,\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2021,\"errorMsg\":\"clock sync success\"},{\"name\":\"DISK_CHECK\",\"startTime\":1706840825000000000,\"endTime\":1706840825180000000,\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2023,\"errorMsg\":\"xavier disk check passed\"},{\"name\":\"MOUNT_NFS\",\"startTime\":1706840826000000000,\"endTime\":1706840826000000000,\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2025,\"errorMsg\":\"mount nfs success\"},{\"name\":\"ROVER\",\"startTime\":1706852514000000000,\"endTime\":1706864170320000000,\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2018,\"errorMsg\":\"rover5 start finish!\"},{\"name\":\"PREPARE_ENGINE\",\"startTime\":1706840845000000000,\"endTime\":1706840859580000000,\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2017,\"errorMsg\":\"generate perception engine finish\"}]}},{\"nodeName\":\"Control\",\"ip\":\"***********\",\"startTime\":1706852517875245313,\"endTime\":1706852645390245313,\"bootStatus\":\"STARTING\",\"moduleBootList\":[{\"bootCount\":1,\"moduleBootDetailList\":[{\"name\":\"DRIVERS_CHASSIS_RX\",\"startTime\":1706852517876599899,\"endTime\":1706852518911599899,\"bootStatus\":\"START_SUCCESS\",\"errorCode\":0,\"errorMsg\":\"\"},{\"name\":\"DRIVERS_CHASSIS_TX\",\"startTime\":1706852518913101055,\"endTime\":1706852518924101055,\"bootStatus\":\"START_SUCCESS\",\"errorCode\":0,\"errorMsg\":\"\"},{\"name\":\"MAP_INITIALIZATION\",\"startTime\":1706852518925611050,\"endTime\":1706852547349611050,\"bootStatus\":\"START_SUCCESS\",\"errorCode\":0,\"errorMsg\":\"\"},{\"name\":\"LOCALIZATION_TRANSFORM\",\"startTime\":1706852547352559333,\"endTime\":1706852547363559333,\"bootStatus\":\"START_SUCCESS\",\"errorCode\":0,\"errorMsg\":\"\"},{\"name\":\"MAP_UPDATER\",\"startTime\":1706852547365561423,\"endTime\":1706852645390561423,\"bootStatus\":\"STARTING\",\"errorCode\":-2007,\"errorMsg\":\"Map updater is not ready.\"},{\"name\":\"DRIVERS_CAMERA_TRAFFIC_LEFT\",\"startTime\":9223372036854775807,\"endTime\":0,\"bootStatus\":\"WAIT_START\",\"errorCode\":0,\"errorMsg\":\"\"},{\"name\":\"DRIVERS_CAMERA_TRAFFIC_RIGHT\",\"startTime\":9223372036854775807,\"endTime\":0,\"bootStatus\":\"WAIT_START\",\"errorCode\":0,\"errorMsg\":\"\"},{\"name\":\"PERC2_TRAFFIC_LIGHT\",\"startTime\":9223372036854775807,\"endTime\":0,\"bootStatus\":\"WAIT_START\",\"errorCode\":0,\"errorMsg\":\"\"},{\"name\":\"ROUTING\",\"startTime\":9223372036854775807,\"endTime\":0,\"bootStatus\":\"WAIT_START\",\"errorCode\":0,\"errorMsg\":\"\"},{\"name\":\"PREDICTION\",\"startTime\":9223372036854775807,\"endTime\":0,\"bootStatus\":\"WAIT_START\",\"errorCode\":0,\"errorMsg\":\"\"},{\"name\":\"PLANNING\",\"startTime\":9223372036854775807,\"endTime\":0,\"bootStatus\":\"WAIT_START\",\"errorCode\":0,\"errorMsg\":\"\"},{\"name\":\"PNC_STATUS\",\"startTime\":9223372036854775807,\"endTime\":0,\"bootStatus\":\"WAIT_START\",\"errorCode\":0,\"errorMsg\":\"\"},{\"name\":\"CONTROL\",\"startTime\":9223372036854775807,\"endTime\":0,\"bootStatus\":\"WAIT_START\",\"errorCode\":0,\"errorMsg\":\"\"},{\"name\":\"ARTIFICIAL_OBSTACLE\",\"startTime\":9223372036854775807,\"endTime\":0,\"bootStatus\":\"WAIT_START\",\"errorCode\":0,\"errorMsg\":\"\"},{\"name\":\"DRIVERS_JOYSTICK\",\"startTime\":9223372036854775807,\"endTime\":0,\"bootStatus\":\"WAIT_START\",\"errorCode\":0,\"errorMsg\":\"\"},{\"name\":\"DRIVERS_V2X_TX\",\"startTime\":9223372036854775807,\"endTime\":0,\"bootStatus\":\"WAIT_START\",\"errorCode\":0,\"errorMsg\":\"\"},{\"name\":\"DRIVERS_V2X_RX\",\"startTime\":9223372036854775807,\"endTime\":0,\"bootStatus\":\"WAIT_START\",\"errorCode\":0,\"errorMsg\":\"\"}]}],\"hardwareModule\":{\"bootCount\":1,\"moduleBootDetailList\":[{\"name\":\"SELFCHECK_CONFIG\",\"startTime\":1706840807000000000,\"endTime\":1706840807040000000,\"bootStatus\":\"START_SUCCESS\",\"errorCode\":1013,\"errorMsg\":\"CODE_HARDWARE_CONFIG_SELECT_FINISH\"},{\"name\":\"MCU_RESPONSE\",\"startTime\":1706840807000000000,\"endTime\":1706840807050000000,\"bootStatus\":\"START_SUCCESS\",\"errorCode\":1004,\"errorMsg\":\"CODE_HARDWARE_GET_MCU_RESPONSE\"},{\"name\":\"MCU_UPDATE\",\"startTime\":1706840807000000000,\"endTime\":1706840807330000000,\"bootStatus\":\"START_SUCCESS\",\"errorCode\":1005,\"errorMsg\":\"CODE_HARDWARE_MCU_FIRMWARE_UPDATE_SUCCESS\"},{\"name\":\"NETWORK_CHECK\",\"startTime\":1706840808000000000,\"endTime\":1706840808030000000,\"bootStatus\":\"START_SUCCESS\",\"errorCode\":1201,\"errorMsg\":\"CODE_HARDWARE_CLOSE_NETWORK_CHECK\"},{\"name\":\"USB_CHECK\",\"startTime\":1706840808000000000,\"endTime\":1706840808320000000,\"bootStatus\":\"START_SUCCESS\",\"errorCode\":1305,\"errorMsg\":\"CODE_HARDWARE_USB_CHIP_NUM_CHECK_OK\"},{\"name\":\"PING_CHECK\",\"startTime\":1706840812000000000,\"endTime\":1706840816150000000,\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2019,\"errorMsg\":\"network check passed\"},{\"name\":\"CLOCK_SYNC\",\"startTime\":1706840825000000000,\"endTime\":1706840837790000000,\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2021,\"errorMsg\":\"clock sync success\"},{\"name\":\"DISK_CHECK\",\"startTime\":1706840825000000000,\"endTime\":1706840825140000000,\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2023,\"errorMsg\":\"xavier disk check passed\"},{\"name\":\"MOUNT_NFS\",\"startTime\":1706840825000000000,\"endTime\":1706840825050000000,\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2025,\"errorMsg\":\"mount nfs success\"},{\"name\":\"ROVER\",\"startTime\":1706852514000000000,\"endTime\":1706864171570000000,\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2018,\"errorMsg\":\"rover5 start finish!\"},{\"name\":\"PREPARE_ENGINE\",\"startTime\":1706840844000000000,\"endTime\":1706840858590000000,\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2017,\"errorMsg\":\"generate perception engine finish\"}]}}]}";
        VehicleBootDTO vehicleBootDto = JsonUtils.readValue(bootBody, VehicleBootDTO.class);
        Map<String, VehicleAlarmEventDTO> mapData = reportAlarmRepository.get(vehicleBootDto.getVehicleName());
        reportAlarmRepository.remove(vehicleBootDto.getVehicleName(), mapData.values().stream().map(event -> event.getType()).collect(Collectors.toList()));
        VehicleAlarmEventDTO alarmEvent = new VehicleAlarmEventDTO();
        alarmEvent.setType(AlarmTypeEnum.LOW_PRESSURE.getValue());
        reportAlarmRepository.save(vehicleBootDto.getVehicleName(), alarmEvent);
        vehicleBootAlarmManager.sendAlarmAdapter(vehicleBootDto);
    }
}