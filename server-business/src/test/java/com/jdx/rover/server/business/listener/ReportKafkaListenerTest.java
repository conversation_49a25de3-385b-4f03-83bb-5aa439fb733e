package com.jdx.rover.server.business.listener;

import com.google.protobuf.util.JsonFormat;
import jdx.rover.report.dto.proto.ReportDto;
import lombok.RequiredArgsConstructor;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;


@RequiredArgsConstructor(onConstructor_ = @Autowired)
@SpringBootTest
class ReportKafkaListenerTest {

    private final ReportKafkaListener reportKafkaListener;

    @Test
    void onMessage() throws Exception {
        String message = "{\"requestHeader\":{\"messageTypeRequest\":[{\"messageType\":\"BOOT\",\"needResponse\":true,\"retry\":true}],\"requestId\":\"1694649897956000162\",\"vehicleName\":\"ZBJ011\",\"requestTime\":\"1694649897956750973\"},\"reportStatisticsData\":{\"runtimeUuid\":\"1694649752541407507\",\"vehicleBoot\":{\"bootId\":\"1\",\"startTime\":\"1694649641429054843\",\"endTime\":\"1694649896427219435\",\"allBootStatus\":\"START_SUCCESS\",\"nodeBoot\":[{\"nodeName\":\"Compute\",\"ip\":\"***********\",\"startTime\":\"1694649641429054843\",\"endTime\":\"1694649859943053538\",\"bootStatus\":\"START_SUCCESS\",\"moduleBoot\":[{\"bootCount\":1,\"moduleBootDetail\":[{\"name\":\"DRIVERS_LIDAR_TOP\",\"startTime\":\"1694649779011535650\",\"endTime\":\"1694649780054535650\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_LIDAR_FRONT\",\"startTime\":\"1694649780055891255\",\"endTime\":\"1694649782067891255\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_LIDAR_RIGHT\",\"startTime\":\"1694649782069261990\",\"endTime\":\"1694649783075261990\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_LIDAR_LEFT\",\"startTime\":\"1694649783076480750\",\"endTime\":\"1694649784084480750\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_IMU\",\"startTime\":\"1694649784085300259\",\"endTime\":\"1694649785106300259\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_GPS_RAC\",\"startTime\":\"1694649785108074421\",\"endTime\":\"1694649787174074421\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"MAP_INITIALIZATION\",\"startTime\":\"1694649787175392559\",\"endTime\":\"1694649820196392559\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"LOCALIZATION_IMU\",\"startTime\":\"1694649820198684812\",\"endTime\":\"1694649821211684812\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"LOCALIZATION_INIT\",\"startTime\":\"1694649821212809233\",\"endTime\":\"1694649839968809233\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"LOCALIZATION_LIDAR\",\"startTime\":\"1694649839969909956\",\"endTime\":\"1694649842989909956\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"LOCALIZATION_EKF\",\"startTime\":\"1694649842991108383\",\"endTime\":\"1694649843003108383\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"LOCALIZATION_TRANSFORM\",\"startTime\":\"1694649843003863903\",\"endTime\":\"1694649843009863903\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"MAP_UPDATER\",\"startTime\":\"1694649843010583135\",\"endTime\":\"1694649844026583135\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_CAMERA360_FRONT\",\"startTime\":\"1694649844027850600\",\"endTime\":\"1694649847339850600\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_CAMERA360_LEFT\",\"startTime\":\"1694649847342547486\",\"endTime\":\"1694649848384547486\",\"bootStatus\":\"START_FAILED\",\"errorCode\":-1011},{\"name\":\"DRIVERS_CAMERA360_RIGHT\",\"startTime\":\"1694649848385328822\",\"endTime\":\"1694649849422328822\",\"bootStatus\":\"START_FAILED\",\"errorCode\":-1011},{\"name\":\"DRIVERS_CAMERA360_REAR\",\"startTime\":\"1694649849424160160\",\"endTime\":\"1694649851122160160\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_LIDAR_REAR\",\"startTime\":\"1694649851123070921\",\"endTime\":\"1694649852128070921\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"PERC2_CLOUD\",\"startTime\":\"1694649852129925401\",\"endTime\":\"1694649854148925401\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"PERC2_GEO\",\"startTime\":\"1694649854150566575\",\"endTime\":\"1694649854196566575\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"PERC2_IMAGE_F\",\"startTime\":\"1694649854197811760\",\"endTime\":\"1694649857952811760\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"PERC2_IMAGE_B\",\"startTime\":\"1694649857954161924\",\"endTime\":\"1694649858777161924\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"PERC2_SOD\",\"startTime\":\"1694649858777822895\",\"endTime\":\"1694649859840822895\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"PERC2_FUSION\",\"startTime\":\"1694649859842789959\",\"endTime\":\"1694649859895789959\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"UNIFORM\",\"startTime\":\"1694649859896503399\",\"endTime\":\"1694649859905503399\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"RECORDER_LITTLE_BAG\",\"startTime\":\"1694649859906951175\",\"endTime\":\"1694649859942951175\",\"bootStatus\":\"START_SUCCESS\"}]}],\"hardwareModule\":{\"bootCount\":1,\"moduleBootDetail\":[{\"name\":\"PREPARE_ENGINE\",\"startTime\":\"1694649759000000000\",\"endTime\":\"1694649785070000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2017,\"errorMsg\":\"generate perception engine finish\"},{\"name\":\"SELFCHECK_CONFIG\",\"startTime\":\"1626893546000000000\",\"endTime\":\"1626893546030000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":1013,\"errorMsg\":\"CODE_HARDWARE_CONFIG_SELECT_FINISH\"},{\"name\":\"MCU_RESPONSE\",\"startTime\":\"1626893546000000000\",\"endTime\":\"1626893546040000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":1004,\"errorMsg\":\"CODE_HARDWARE_GET_MCU_RESPONSE\"},{\"name\":\"MCU_UPDATE\",\"startTime\":\"1626893547000000000\",\"endTime\":\"1626893547270000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":1005,\"errorMsg\":\"CODE_HARDWARE_MCU_FIRMWARE_UPDATE_SUCCESS\"},{\"name\":\"NETWORK_CHECK\",\"startTime\":\"1626893547000000000\",\"endTime\":\"1626893582730000000\",\"bootStatus\":\"START_FAILED\",\"errorCode\":1208,\"errorMsg\":\"CODE_HARDWARE_NET_PING_LIDAR_ERR\"},{\"name\":\"USB_CHECK\",\"startTime\":\"1626893547000000000\",\"endTime\":\"1626893547330000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":1305,\"errorMsg\":\"CODE_HARDWARE_USB_CHIP_NUM_CHECK_OK\"},{\"name\":\"PING_CHECK\",\"startTime\":\"1626893547000000000\",\"endTime\":\"1626893547210000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2019,\"errorMsg\":\"network check passed\"},{\"name\":\"CLOCK_SYNC\",\"startTime\":\"1694649726000000000\",\"endTime\":\"1694649741610000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2021,\"errorMsg\":\"clock sync success\"},{\"name\":\"DISK_CHECK\",\"startTime\":\"1694649726000000000\",\"endTime\":\"1694649726160000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2023,\"errorMsg\":\"xavier disk check passed\"},{\"name\":\"MOUNT_NFS\",\"startTime\":\"1694649727000000000\",\"endTime\":\"1694649727000000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2025,\"errorMsg\":\"mount nfs success\"},{\"name\":\"ROVER\",\"startTime\":\"1694649773000000000\",\"endTime\":\"1694649775080000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2018,\"errorMsg\":\"rover5 start finish!\"}]}},{\"nodeName\":\"Control\",\"ip\":\"***********\",\"startTime\":\"1694649641429054843\",\"endTime\":\"1694649896427219435\",\"bootStatus\":\"START_SUCCESS\",\"moduleBoot\":[{\"bootCount\":1,\"moduleBootDetail\":[{\"name\":\"DRIVERS_CHASSIS_RX\",\"startTime\":\"1694649765280836711\",\"endTime\":\"1694649766306836711\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_CHASSIS_TX\",\"startTime\":\"1694649766307911199\",\"endTime\":\"1694649766324911199\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"MAP_INITIALIZATION\",\"startTime\":\"1694649766325722542\",\"endTime\":\"1694649818351722542\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"LOCALIZATION_TRANSFORM\",\"startTime\":\"1694649818354673359\",\"endTime\":\"1694649818364673359\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"MAP_UPDATER\",\"startTime\":\"1694649818365930569\",\"endTime\":\"1694649843377930569\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_CAMERA_TRAFFIC_LEFT\",\"startTime\":\"1694649843381268097\",\"endTime\":\"1694649846878268097\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_CAMERA_TRAFFIC_RIGHT\",\"startTime\":\"1694649846879498883\",\"endTime\":\"1694649848658498883\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"PERC2_TRAFFIC_LIGHT\",\"startTime\":\"1694649848659164892\",\"endTime\":\"1694649854123164892\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"ROUTING\",\"startTime\":\"1694649854124245476\",\"endTime\":\"1694649894173245476\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"PREDICTION\",\"startTime\":\"1694649894175295869\",\"endTime\":\"1694649894195295869\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"PLANNING\",\"startTime\":\"1694649894196201963\",\"endTime\":\"1694649895265201963\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"PNC_STATUS\",\"startTime\":\"1694649895265741806\",\"endTime\":\"1694649895282741806\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"CONTROL\",\"startTime\":\"1694649895283634481\",\"endTime\":\"1694649895365634481\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"ARTIFICIAL_OBSTACLE\",\"startTime\":\"1694649895366693195\",\"endTime\":\"1694649896406693195\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_JOYSTICK\",\"startTime\":\"1694649896408028780\",\"endTime\":\"1694649896418028780\",\"bootStatus\":\"START_SUCCESS\"},{\"name\":\"DRIVERS_V2X_TX\",\"startTime\":\"1694649896418677793\",\"endTime\":\"1694649896426677793\",\"bootStatus\":\"START_SUCCESS\"}]}],\"hardwareModule\":{\"bootCount\":1,\"moduleBootDetail\":[{\"name\":\"SELFCHECK_CONFIG\",\"startTime\":\"1694649696000000000\",\"endTime\":\"1694649696030000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":1013,\"errorMsg\":\"CODE_HARDWARE_CONFIG_SELECT_FINISH\"},{\"name\":\"MCU_RESPONSE\",\"startTime\":\"1694649696000000000\",\"endTime\":\"1694649696040000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":1004,\"errorMsg\":\"CODE_HARDWARE_GET_MCU_RESPONSE\"},{\"name\":\"MCU_UPDATE\",\"startTime\":\"1694649696000000000\",\"endTime\":\"1694649696270000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":1005,\"errorMsg\":\"CODE_HARDWARE_MCU_FIRMWARE_UPDATE_SUCCESS\"},{\"name\":\"NETWORK_CHECK\",\"startTime\":\"1694649696000000000\",\"endTime\":\"1694649696000000000\",\"bootStatus\":\"START_FAILED\",\"errorCode\":1211,\"errorMsg\":\"CODE_HARDWARE_NETWORK_CHECK\"},{\"name\":\"USB_CHECK\",\"startTime\":\"1694649696000000000\",\"endTime\":\"1694649696000000000\",\"bootStatus\":\"START_FAILED\",\"errorCode\":1306,\"errorMsg\":\"CODE_HARDWARE_USB_CHECK\"},{\"name\":\"PING_CHECK\",\"startTime\":\"1694649696000000000\",\"endTime\":\"1694649696260000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2019,\"errorMsg\":\"network check passed\"},{\"name\":\"CLOCK_SYNC\",\"startTime\":\"1694649726000000000\",\"endTime\":\"1694649755610000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2021,\"errorMsg\":\"clock sync success\"},{\"name\":\"DISK_CHECK\",\"startTime\":\"1694649726000000000\",\"endTime\":\"1694649726130000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2023,\"errorMsg\":\"xavier disk check passed\"},{\"name\":\"MOUNT_NFS\",\"startTime\":\"1694649726000000000\",\"endTime\":\"1694649726050000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2025,\"errorMsg\":\"mount nfs success\"},{\"name\":\"ROVER\",\"startTime\":\"1694649760000000000\",\"endTime\":\"1694649762090000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2018,\"errorMsg\":\"rover5 start finish!\"},{\"name\":\"PREPARE_ENGINE\",\"startTime\":\"1694649746000000000\",\"endTime\":\"1694649760540000000\",\"bootStatus\":\"START_SUCCESS\",\"errorCode\":2017,\"errorMsg\":\"generate perception engine finish\"}]}}]}}}";
        ReportDto.ReportDTO.Builder dataDTO = ReportDto.ReportDTO.newBuilder();
        JsonFormat.parser().merge(message, dataDTO);
        reportKafkaListener.onMessage(Lists.newArrayList(dataDTO.build().toByteArray()));
    }
}