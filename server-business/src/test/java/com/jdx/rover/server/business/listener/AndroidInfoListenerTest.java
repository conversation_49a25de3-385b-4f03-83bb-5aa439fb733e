package com.jdx.rover.server.business.listener;

import org.assertj.core.util.Lists;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
class AndroidInfoListenerTest {
    @Autowired
    private AndroidInfoListener androidInfoListener;
    @Test
    void onMessage() {
        String msg = "{\"data\":{\"gridList\":[{\"gridNo\":1,\"openStatus\":false},{\"gridNo\":2,\"openStatus\":false},{\"gridNo\":3,\"openStatus\":false},{\"gridNo\":4,\"openStatus\":false},{\"gridNo\":5,\"openStatus\":false},{\"gridNo\":6,\"openStatus\":false},{\"gridNo\":7,\"openStatus\":false},{\"gridNo\":8,\"openStatus\":false},{\"gridNo\":9,\"openStatus\":false},{\"gridNo\":10,\"openStatus\":false},{\"gridNo\":11,\"openStatus\":false},{\"gridNo\":12,\"openStatus\":false},{\"gridNo\":13,\"openStatus\":false},{\"gridNo\":14,\"openStatus\":false},{\"gridNo\":15,\"openStatus\":false},{\"gridNo\":16,\"openStatus\":false},{\"gridNo\":17,\"openStatus\":false},{\"gridNo\":18,\"openStatus\":false}]},\"header\":{\"messageType\":\"grid_status\",\"requestId\":1679379880867000041,\"requestTime\":1679379880867,\"vehicleName\":\"JDE0029\"}}";
        androidInfoListener.onMessage(Lists.newArrayList(msg));
    }
}