package com.jdx.rover.server.business.listener;

import com.jdx.rover.server.api.domain.dto.guardian.VehicleAlarmEventDTO;
import com.jdx.rover.server.api.domain.enums.guardian.AlarmTypeEnum;
import com.jdx.rover.server.repository.redis.guardian.ReportAlarmRepository;
import jdx.rover.guardian.dto.LocalView;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;

@Slf4j
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@SpringBootTest
class LocalViewListenerTest {

    private final LocalViewListener localViewListener;
    private final ReportAlarmRepository reportAlarmRepository;

    @Test
    void onMessage() throws Exception {
        String vehicleName = "JD0015";
        LocalView.LocalViewDTO.Builder logBuilder = build();
        logBuilder.getAuthInfoBuilder().setVehicleId(vehicleName);
        {
            // 路口遇阻
            logBuilder.setTimestamp(System.currentTimeMillis() * 1000000);
            logBuilder.getPlanningBuilder().getDecisionBuilder().setStopTime(190);
            logBuilder.getPlanningBuilder().getDecisionBuilder().setIsInIntersection(true);
            logBuilder.getPlanningBuilder().getDecisionBuilder().getBehaviorDecisionListBuilder().clear();
            logBuilder.getPlanningBuilder().getDecisionBuilder().getBehaviorDecisionListBuilder().addBehaviorDecision(0
                    , LocalView.BehaviorDecision.newBuilder().setState(LocalView.BehaviorState.DETOUR_STATIC_OBSTACLE));

            localViewListener.onMessage(Lists.newArrayList(logBuilder.build().toByteArray()));
            Map<String, VehicleAlarmEventDTO> alarmMapDb = reportAlarmRepository.get(vehicleName);
            assertThat(alarmMapDb).isNotNull();
            assertThat(alarmMapDb).hasSize(1);
            assertThat(alarmMapDb.get(AlarmTypeEnum.VEHICLE_STOP_INTERSECTION_STUCK.getValue())).isNotNull();
        }
        {
            // 路口遇阻消除
            Thread.sleep(2000);
            logBuilder.setTimestamp(System.currentTimeMillis() * 1000000);
            logBuilder.getPlanningBuilder().getDecisionBuilder().setStopTime(110);
            logBuilder.getPlanningBuilder().getDecisionBuilder().setIsInIntersection(true);
            logBuilder.getPlanningBuilder().getDecisionBuilder().getBehaviorDecisionListBuilder().clear();
            logBuilder.getPlanningBuilder().getDecisionBuilder().getBehaviorDecisionListBuilder().addBehaviorDecision(0
                    , LocalView.BehaviorDecision.newBuilder().setState(LocalView.BehaviorState.DETOUR_STATIC_OBSTACLE));

            localViewListener.onMessage(Lists.newArrayList(logBuilder.build().toByteArray()));
            Map<String, VehicleAlarmEventDTO> alarmMapDb = reportAlarmRepository.get(vehicleName);
            assertThat(alarmMapDb).isEmpty();
        }
        {
            // 遇阻停车
            Thread.sleep(2000);
            logBuilder.setTimestamp(System.currentTimeMillis() * 1000000);
            logBuilder.getPlanningBuilder().getDecisionBuilder().setStopTime(65);
            logBuilder.getPlanningBuilder().getDecisionBuilder().setIsInIntersection(false);
            logBuilder.getPlanningBuilder().getDecisionBuilder().getBehaviorDecisionListBuilder().clear();
            logBuilder.getPlanningBuilder().getDecisionBuilder().getBehaviorDecisionListBuilder().addBehaviorDecision(0
                    , LocalView.BehaviorDecision.newBuilder().setState(LocalView.BehaviorState.DETOUR_STATIC_OBSTACLE));

            localViewListener.onMessage(Lists.newArrayList(logBuilder.build().toByteArray()));
            Map<String, VehicleAlarmEventDTO> alarmMapDb = reportAlarmRepository.get(vehicleName);
            assertThat(alarmMapDb).isNotNull();
            assertThat(alarmMapDb).hasSize(1);
            assertThat(alarmMapDb.get(AlarmTypeEnum.VEHICLE_STOP_TIMEOUT.getValue())).isNotNull();
        }
        {
            // 遇阻停车消除
            Thread.sleep(2000);
            logBuilder.setTimestamp(System.currentTimeMillis() * 1000000);
            logBuilder.getPlanningBuilder().getDecisionBuilder().setStopTime(55);
            logBuilder.getPlanningBuilder().getDecisionBuilder().setIsInIntersection(false);
            logBuilder.getPlanningBuilder().getDecisionBuilder().getBehaviorDecisionListBuilder().clear();
            logBuilder.getPlanningBuilder().getDecisionBuilder().getBehaviorDecisionListBuilder().addBehaviorDecision(0
                    , LocalView.BehaviorDecision.newBuilder().setState(LocalView.BehaviorState.DETOUR_STATIC_OBSTACLE));

            localViewListener.onMessage(Lists.newArrayList(logBuilder.build().toByteArray()));
            Map<String, VehicleAlarmEventDTO> alarmMapDb = reportAlarmRepository.get(vehicleName);
            assertThat(alarmMapDb).isEmpty();
        }
    }


    private LocalView.LocalViewDTO.Builder build() {
        LocalView.LocalViewDTO.Builder builder = LocalView.LocalViewDTO.newBuilder();
        LocalView.Planning.Builder planning = LocalView.Planning.newBuilder();
        planning.addInterestedLight(LocalView.PlanningTrafficLight.newBuilder().setId("1"));

        LocalView.PlanningDecision.Builder decision = LocalView.PlanningDecision.newBuilder();
        LocalView.BehaviorDecisions.Builder behaviorDecisions = LocalView.BehaviorDecisions.newBuilder();
        behaviorDecisions.addBehaviorDecision(LocalView.BehaviorDecision.newBuilder().setState(LocalView.BehaviorState.STOP_TRAFFIC_LIGHT));
        decision.setBehaviorDecisionList(behaviorDecisions);
        decision.setStopTime(70);
        decision.setIsInIntersection(false);

        planning.setDecision(decision);

        builder.setPlanning(planning);
        builder.addTrafficLight(LocalView.TrafficLight.newBuilder().setLight(LocalView.TrafficLight.Light.RED).setId("1"));
        builder.setAuthInfo(LocalView.AuthorizeInfo.newBuilder());

        return builder;
    }
}