package com.jdx.rover.server.business.manager.localview;

import jdx.rover.guardian.dto.LocalView;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;

class PlanningManagerTest {

    @Test
    void isStopTrafficLight() {
        {
            LocalView.LocalViewDTO.Builder builder = LocalView.LocalViewDTO.newBuilder();
            LocalView.LocalViewDTO dto = builder.build();
            boolean result = PlanningManager.isStopTrafficLight(dto);
            assertThat(result).isFalse();
        }
        {
            LocalView.LocalViewDTO.Builder builder = build();
            LocalView.BehaviorDecision.Builder behaviorDecision = LocalView.BehaviorDecision.newBuilder();
            behaviorDecision.setState(LocalView.BehaviorState.STOP_TRAFFIC_LIGHT);
            builder.getPlanningBuilder().getDecisionBuilder().getBehaviorDecisionListBuilder().clear();
            builder.getPlanningBuilder().getDecisionBuilder().getBehaviorDecisionListBuilder().addBehaviorDecision(0, behaviorDecision);
            LocalView.LocalViewDTO dto = builder.build();
            boolean result = PlanningManager.isStopTrafficLight(dto);
            assertThat(result).isTrue();
        }
        {
            LocalView.LocalViewDTO.Builder builder = build();
            LocalView.BehaviorDecision.Builder behaviorDecision = LocalView.BehaviorDecision.newBuilder();
            behaviorDecision.setState(LocalView.BehaviorState.STOP_TRAFFIC_LIGHT);
            builder.getPlanningBuilder().getDecisionBuilder().getBehaviorDecisionListBuilder().clear();
            builder.getPlanningBuilder().getDecisionBuilder().getBehaviorDecisionListBuilder().addBehaviorDecision(0, behaviorDecision);
            LocalView.LocalViewDTO dto = builder.build();
            boolean result = PlanningManager.isStopTrafficLight(dto);
            assertThat(result).isFalse();
        }
    }

    private LocalView.LocalViewDTO.Builder build() {
        LocalView.LocalViewDTO.Builder builder = LocalView.LocalViewDTO.newBuilder();
        LocalView.Planning.Builder planning = LocalView.Planning.newBuilder();
        LocalView.PlanningDecision.Builder decision = LocalView.PlanningDecision.newBuilder();
        LocalView.BehaviorDecisions.Builder behaviorDecisions = LocalView.BehaviorDecisions.newBuilder();
        LocalView.BehaviorDecision.Builder behaviorDecision = LocalView.BehaviorDecision.newBuilder();
        behaviorDecision.setState(LocalView.BehaviorState.STOP_TRAFFIC_LIGHT);
        behaviorDecisions.addBehaviorDecision(0, behaviorDecision);
        decision.setBehaviorDecisionList(behaviorDecisions);
        planning.setDecision(decision);
        builder.setPlanning(planning);
        return builder;
    }
}