package com.jdx.rover.server.business.service.alarm;

import jdx.rover.guardian.dto.LocalView;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;

class StopTimeoutServiceTest {

    @Test
    void isReportAlarm() {
        {
            LocalView.LocalViewDTO.Builder builder = LocalView.LocalViewDTO.newBuilder();
            LocalView.Planning.Builder planning = LocalView.Planning.newBuilder();
            LocalView.PlanningDecision.Builder decision = LocalView.PlanningDecision.newBuilder();
            decision.setStopTime(70);
            decision.setIsInIntersection(false);
            planning.setDecision(decision);
            builder.setPlanning(planning);
            LocalView.LocalViewDTO dto = builder.build();
            boolean result = StopTimeoutService.isReportAlarm(dto);
            assertThat(result).isTrue();
        }
        {
            LocalView.LocalViewDTO.Builder builder = LocalView.LocalViewDTO.newBuilder();
            LocalView.Planning.Builder planning = LocalView.Planning.newBuilder();
            LocalView.PlanningDecision.Builder decision = LocalView.PlanningDecision.newBuilder();
            decision.setStopTime(50);
            decision.setIsInIntersection(false);
            planning.setDecision(decision);
            builder.setPlanning(planning);
            LocalView.LocalViewDTO dto = builder.build();
            boolean result = StopTimeoutService.isReportAlarm(dto);
            assertThat(result).isFalse();
        }
        {
            LocalView.LocalViewDTO.Builder builder = LocalView.LocalViewDTO.newBuilder();
            LocalView.Planning.Builder planning = LocalView.Planning.newBuilder();
            LocalView.PlanningDecision.Builder decision = LocalView.PlanningDecision.newBuilder();
            decision.setStopTime(70);
            decision.setIsInIntersection(true);
            planning.setDecision(decision);
            builder.setPlanning(planning);
            LocalView.LocalViewDTO dto = builder.build();
            boolean result = StopTimeoutService.isReportAlarm(dto);
            assertThat(result).isFalse();
        }
    }
}