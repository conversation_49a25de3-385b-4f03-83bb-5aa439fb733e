package com.jdx.rover.server.business.service.alarm;

import cn.hutool.core.date.StopWatch;
import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.util.JsonFormat;
import jdx.rover.guardian.dto.LocalView;
import org.apache.commons.collections4.queue.CircularFifoQueue;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.concurrent.TimeUnit;

import static org.assertj.core.api.Assertions.assertThat;

class IntersectionStuckServiceTest {

//    @Test
//    void isReportAlarm() {
//        {
//            LocalView.LocalViewDTO.Builder builder = LocalView.LocalViewDTO.newBuilder();
//            LocalView.LocalViewDTO dto = builder.build();
//            boolean result = IntersectionStuckService.isReportAlarm(dto);
//            assertThat(result).isFalse();
//        }
//        {
//            LocalView.LocalViewDTO.Builder builder = build();
//            LocalView.BehaviorDecision.Builder behaviorDecision = LocalView.BehaviorDecision.newBuilder();
//            behaviorDecision.setState(LocalView.BehaviorState.STOP_TRAFFIC_LIGHT);
//            builder.getPlanningBuilder().getDecisionBuilder().getBehaviorDecisionListBuilder().clear();
//            builder.getPlanningBuilder().getDecisionBuilder().getBehaviorDecisionListBuilder().addBehaviorDecision(0, behaviorDecision);
//            LocalView.LocalViewDTO dto = builder.build();
//            boolean result = IntersectionStuckService.isReportAlarm(dto);
//            assertThat(result).isFalse();
//        }
//        {
//            LocalView.LocalViewDTO.Builder builder = build();
//            LocalView.BehaviorDecision.Builder behaviorDecision = LocalView.BehaviorDecision.newBuilder();
//            behaviorDecision.setState(LocalView.BehaviorState.STOP_TRAFFIC_LIGHT);
//            builder.getPlanningBuilder().getDecisionBuilder().getBehaviorDecisionListBuilder().clear();
//            builder.getPlanningBuilder().getDecisionBuilder().getBehaviorDecisionListBuilder().addBehaviorDecision(0, behaviorDecision);
//            builder.getPlanningBuilder().getDecisionBuilder().setStopTime(190);
//            LocalView.LocalViewDTO dto = builder.build();
//            boolean result = IntersectionStuckService.isReportAlarm(dto);
//            assertThat(result).isTrue();
//        }
//        {
//            LocalView.LocalViewDTO.Builder builder = build();
//            LocalView.BehaviorDecision.Builder behaviorDecision = LocalView.BehaviorDecision.newBuilder();
//            behaviorDecision.setState(LocalView.BehaviorState.DETOUR_SLOW_OBSTACLE);
//            builder.getPlanningBuilder().getDecisionBuilder().getBehaviorDecisionListBuilder().clear();
//            builder.getPlanningBuilder().getDecisionBuilder().getBehaviorDecisionListBuilder().addBehaviorDecision(0, behaviorDecision);
//            LocalView.LocalViewDTO dto = builder.build();
//            boolean result = IntersectionStuckService.isReportAlarm(dto);
//            assertThat(result).isFalse();
//        }
//        {
//            LocalView.LocalViewDTO.Builder builder = build();
//            LocalView.BehaviorDecision.Builder behaviorDecision = LocalView.BehaviorDecision.newBuilder();
//            behaviorDecision.setState(LocalView.BehaviorState.DETOUR_SLOW_OBSTACLE);
//            builder.getPlanningBuilder().getDecisionBuilder().getBehaviorDecisionListBuilder().clear();
//            builder.getPlanningBuilder().getDecisionBuilder().getBehaviorDecisionListBuilder().addBehaviorDecision(0, behaviorDecision);
//            builder.getPlanningBuilder().getDecisionBuilder().setIsInIntersection(true);
//            LocalView.LocalViewDTO dto = builder.build();
//            boolean result = IntersectionStuckService.isReportAlarm(dto);
//            assertThat(result).isFalse();
//        }
//        {
//            LocalView.LocalViewDTO.Builder builder = build();
//            LocalView.BehaviorDecision.Builder behaviorDecision = LocalView.BehaviorDecision.newBuilder();
//            behaviorDecision.setState(LocalView.BehaviorState.DETOUR_SLOW_OBSTACLE);
//            builder.getPlanningBuilder().getDecisionBuilder().getBehaviorDecisionListBuilder().clear();
//            builder.getPlanningBuilder().getDecisionBuilder().getBehaviorDecisionListBuilder().addBehaviorDecision(0, behaviorDecision);
//            builder.getPlanningBuilder().getDecisionBuilder().setIsInIntersection(true);
//            builder.getPlanningBuilder().getDecisionBuilder().setStopTime(190);
//            LocalView.LocalViewDTO dto = builder.build();
//            boolean result = IntersectionStuckService.isReportAlarm(dto);
//            assertThat(result).isTrue();
//        }
//    }

    private LocalView.LocalViewDTO.Builder build() {
        LocalView.LocalViewDTO.Builder builder = LocalView.LocalViewDTO.newBuilder();

        LocalView.Planning.Builder planning = LocalView.Planning.newBuilder();
        LocalView.BehaviorDecisions.Builder behaviorDecisions = LocalView.BehaviorDecisions.newBuilder();

        LocalView.PlanningTrafficLight.Builder planningTrafficLight = LocalView.PlanningTrafficLight.newBuilder();
        planningTrafficLight.setId("1");
        planning.addInterestedLight(planningTrafficLight);

        LocalView.PlanningDecision.Builder decision = LocalView.PlanningDecision.newBuilder();

        LocalView.BehaviorDecision.Builder behaviorDecision = LocalView.BehaviorDecision.newBuilder();
        behaviorDecision.setState(LocalView.BehaviorState.STOP_TRAFFIC_LIGHT);
        behaviorDecisions.addBehaviorDecision(0, behaviorDecision);
        decision.setBehaviorDecisionList(behaviorDecisions);
        decision.setStopTime(70);
        decision.setIsInIntersection(false);
        planning.setDecision(decision);

        builder.setPlanning(planning);

        LocalView.TrafficLight.Builder trafficLight = LocalView.TrafficLight.newBuilder();
        trafficLight.setLight(LocalView.TrafficLight.Light.RED);
        trafficLight.setId("1");
        builder.addTrafficLight(trafficLight);

        return builder;
    }

    @Test
    public void test_isStopLineWithDistance() throws InvalidProtocolBufferException {
//        String hexData = "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";
//        String hexData = "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";
//        String hexData = "0a0a0d283c244615523d1646124e0a0f0dcd8eca3c15c49a193c1d44b146c011a0703d0ad7a3144020022802420a0dbcd8fbbf1501e1043f420a0d7e3ef6bf1501c329bf420a0d4848063f155a8e12bf420a0d9727f63e15a8151c3f1a4e0a0a0db69024411525020f3e0a0a0d851c96c015a29307be0a0a0dc4fac7c115fdd726bf120a0db69024411525020f3e120a0dd6db94c015664706be120a0d1eb5a1c1150b6c19bf22040a020a0022300a0a0de903f341156a135bbe0a0a0d4d8f26c115ad4285bf0a0a0dad9d19c215ae10c0bf0a0a0de1ce44c215ba89b1bf2a240a08375f3336313030301a0f0d0b0a65c315120b8f411d8fc295412107ce1951da8b3bc032210a07335f3431323436120a0dff7089c015d6929ac1120a0de479594215271394c132210a07335f3431323535120a0dc6fd8ac0152e1b75c1120a0dfc3a59421595f867c132210a07335f3431323536120a0dac5f8cc01572fc3bc1120a0dbf03594215229d2fc132210a07335f3431323638120a0d987b85c11503413fc1120a0d648f55c115a8eb3dc132210a07335f3431323739120a0d483e56c1154d7805c1120a0df69a85c115543a06c132210a07335f3431323830120a0dff7956c11581c99cc0120a0dd41286c115163b9ec032210a07335f3431323833120a0d1ccc5842151fb3edc0120a0db8c18dc015a2d702c132210a07335f3431323834120a0d609458421590f677c0120a0d4c138fc015b5b698c032210a07335f3431323932120a0d2bba56c1151aa599bf120a0dc79386c115db9c9ebf32210a07335f3431323935120a0dc2555842157582f23d120a0d88bc8bc01528a632bf32210a07335f3431353030120a0d648f55c115a8eb3dc1120a0dac5f8cc01572fc3bc132210a07335f3431353031120a0dc47054c115b69f76c1120a0dc6fd8ac0152e1b75c132210a07335f3431353032120a0dff2f53c1156a0a9bc1120a0dff7089c015d6929ac132210a07335f3431353033120a0db8c18dc015a2d702c1120a0d483e56c1154d7805c132210a07335f3431353034120a0d4c138fc015b5b698c0120a0dff7956c11581c99cc032210a07335f3431353035120a0d88bc8bc01528a632bf120a0d2bba56c1151aa599bf325d0a07335f3431353038120a0df69a85c115543a06c1120a0dae718cc11589ca0fc1120a0d068a90c115b45419c1120a0dfce391c115d5d822c1120a0d917f90c115ee562cc1120a0dc55c8cc115fdce35c1120a0d987b85c11503413fc132210a07335f3431353039120a0df69a85c115543a06c1120a0d609028c2158f640cc132210a07335f3431353130120a0dd41286c115163b9ec0120a0d9f7b28c2156594a9c032210a07335f3431353131120a0dc79386c115db9c9ebf120a0d306528c215047bc5bf325d0a07335f3431353132120a0dc79386c115db9c9ebf120a0da2079ac115c2029bbf120a0dd30caac115f27988bf120a0d5aa3b6c115d8044ebf120a0d36cbbfc115bb70dabe120a0d6984c5c115518c8b3d120a0df2cec7c115dff82d3f325d0a07335f3431383933120a0dd41286c115163b9ec0120a0d26cb98c115ea239ac0120a0d6b68a8c11500b08bc0120a0da1eab4c115adbe65c0120a0dca51bec115de631fc0120a0de59dc4c115239f88bf120a0df2cec7c115dff82d3f325d0a07335f3431383934120a0df69a85c115543a06c1120a0d7fa597c11533a902c1120a0d7be2a6c115dcb1edc0120a0deb51b3c1150a93c5c0120a0dcff3bcc115f0f58cc0120a0d26c8c3c1151cb507c0120a0df2cec7c115dff82d3f3a640a032d333111cdccccccccccfc3f1a0a0db47b88bf1575c8c4401a0a0d7c0ea2bf15c581c4401a0a0d551cfebf153618ad401a0a0dad8ffdbf15dce5a9401a0a0d0cdff3bf1569c996401a0a0dc7a5c2bf150687a2401a0a0dce227ebf153045b34028063a580a032d333011cdccccccccccfc3f1a0a0d1de92341156b79f4401a0a0dc7eb254115a46ee1401a0a0d28c126411523e7df401a0a0d545a2841157b0ae0401a0a0dbf48284115d63ce3401a0a0d9e832641156017eb4028063a580a032d323911cdccccccccccfc3f1a0a0daed7f74015f6f3d3401a0a0deab6f440157037d2401a0a0d7fc8f44015439ed0401a0a0d2b41f84015e65cca401a0a0d0494f9401547d3d0401a0a0d6f82f94015746cd24028063a640a032d323711cdccccccccccfc3f1a0a0daa0a0a411541b50b411a0a0d143e0941156bac0b411a0a0d487a084115ffd60a411a0a0ddd8b084115d13d09411a0a0d3e61094115117a08411a0a0d6bfa0a4115bd8b08411a0a0dcd8a0c4115006a094128063a7c0a032d323611cdccccccccccfc3f1a0a0d9e03e34015e4e5c7401a0a0d716ae1401538d4c7401a0a0d45d1df40158cc2c7401a0a0d94f2e640158e9f91401a0a0d6818e94015d0e784401a0a0d9864fd4015c9fd90401a0a0d6ef10741159fcda1401a0a0d8d3c05411532fbaf401a0a0dd0cdfe4015baafba4028063a700a032d323511cdccccccccccfc3f1a0a0d63dac04015af4628411a0a0d34cbbd4015d59b26411a0a0ddfcdbf401524d921411a0a0d5598e04015cdf218411a0a0d9d1e024115131d14411a0a0daaad02411508be19411a0a0d8e7fe94015b32521411a0a0d301ccc4015f2b7274128063a400a032d323411cdccccccccccfc3f1a0a0d1ad2d7bf1575b921411a0a0d7e9be4bf15c9a721411a0a0de264f1bf151d9621411a0a0ddcb9eabf155dd2204128063a400a032d323311cdccccccccccfc3f1a0a0d7d95d8bf1502faf6401a0a0d4528f2bf1551b3f6401a0a0da346f8bf157808f5401a0a0d8d18e5bf157c3df54028063a88010a032d323111cdccccccccccfc3f1a0a0d06f7844015591e19411a0a0d6e6f834015ec4818411a0a0d75d98d4015e1e60e411a0a0dbd0e98401599ea07411a0a0d7fb9994015d92607411a0a0dab529b4015af2f07411a0a0dec1d9b4015739509411a0a0dc13b974015a98111411a0a0da9d4934015c1d513411a0a0d0f188d40155ab1174128063a340a032d313911cdccccccccccfc3f1a0a0def26ac40155c9729411a0a0d1129a440152d6b29411a0a0d8438ac4015c5ca284128063a400a032d313811cdccccccccccfc3f1a0a0d4655b9c01595a519411a0a0d9f87bcc015e99319411a0a0d7564bcc015bcfa17411a0a0d1c32b9c015680c184128063a4c0a032d313611cdccccccccccfc3f1a0a0d150997c01507e625411a0a0d71b19bc015573224411a0a0d08399dc015eb5c23411a0a0d836d98c0156d7723411a0a0d661a92c0155c67244128063a700a032d313511cdccccccccccfc3f1a0a0d36c067c015c9ad3e401a0a0d8ff26ac015718a3e401a0a0de8246ec01519673e401a0a0d464374c015b4bb37401a0a0ddcce60c0150f2b32401a0a0d2a6a5ac015c07132401a0a0dcc4b54c015251d39401a0a0df66e54c015804f3c4028063a640a032d313411cdccccccccccfc3f1a0a0d988959c015ca6914411a0a0dfc5266c015724614411a0a0def4087c015b1d50b411a0a0df1b688c015ae330a411a0a0d300c87c015ed6f09411a0a0d7f1e3bc01541220b411a0a0d0f894fc015881e124128063a88010a032d313311cdccccccccccfc3f1a0a0de6de39c015fd78bf401a0a0d3f113dc0155167bf401a0a0d715966c015394fbb401a0a0db54965c01512b189401a0a0d610365c015b87e86401a0a0d5f855dc0154cb173401a0a0da7b64cc015e53361401a0a0d19ca3fc015eb8e5e401a0a0d7aec1ec015990ab0401a0a0df8551fc01521d6b44028063a640a032d333211cdccccccccccfc3f1a0a0dc7ec0841154e001a401a0a0d3120084115f6dc19401a0a0d9a530741159eb919401a0a0d9998064115913113401a0a0df9b2064115819a09401a0a0df054084115d7ae06401a0a0dbb1809411589040a4028063aac010a032d323211cdccccccccccfc3f1a0a0d3ec904401558dbf6401a0a0d3a08d03f154b3cf6401a0a0d73c55dbf156b28c9401a0a0df59874bf153007c1401a0a0dbf8d6ebf153e72af401a0a0d85138e3f15e2f50d401a0a0d3212db3f15a96b0c401a0a0da4486740150a460b401a0a0d61ad93401593d60a401a0a0d4b42a540155c5b0c401a0a0d3cf4a34015141849401a0a0df724954015729bdd401a0a0dc5e08c4015caa7e34028063a400a032d313211cdccccccccccfc3f1a0a0d469f26c01568961d411a0a0db5ff3cc0158d581d411a0a0d6c5046c015dea41b411a0a0d8a1d39c0157262194128063a640a032d313111cdccccccccccfc3f1a0a0de2c98140154de403411a0a0dae6570401549af03411a0a0dd888704015b3e202411a0a0ddf538040154a4202411a0a0d7ffc894015b8aa01411a0a0dd82e8d401564bc01411a0a0dfca68640153932034128063a7c0a032d313011cdccccccccccfc3f1a0a0d3ba50b40154cbf1c411a0a0df135a43f153f201c411a0a0d8294913f15906c1a411a0a0d3aa49e3f15a5b119411a0a0d8a44564015d22517411a0a0de376594015a82e17411a0a0d6bb85f4015eb0c18411a0a0d18725f401518a619411a0a0dc6de3b4015a9aa1b4128063a7b0a022d3911cdccccccccccfc3f1a0a0da2e3673f15853e0f411a0a0d3d1a5b3f15af350f411a0a0d26b08cbf15f2b0fb401a0a0d84ce92bf151906fa401a0a0d60cb57bf15c73df7401a0a0d3c4f324015a18804411a0a0ddabe4b4015e89b05411a0a0d09ce4e4015557106411a0a0d4627384015a8cc074128063a6f0a022d3711cdccccccccccfc3f1a0a0d5e03053f15a207eb401a0a0df373f03e15f6f5ea401a0a0db35e633e156c04e9401a0a0d4241983d153b36e7401a0a0d07bfbfbc15b579e5401a0a0d54032dbe155612e2401a0a0d4856efbd15d58ae0401a0a0d00d1363e153827e44028063a87010a022d3511cdccccccccccfc3f1a0a0d1e6490401541761ec01a0a0df1ca8e401599991ec01a0a0dc5318d4015f1bc1ec01a0a0d84668d4015015428c01a0a0db920974015a5e42dc01a0a0d59c9a04015ee4230c01a0a0dc47ac54015a8e329c01a0a0d4cbccb401592f122c01a0a0db7aacb401538bf1fc01a0a0db338b54015b07b1ec028063a87010a022d3411cdccccccccccfc3f1a0a0dd3de724015e7ff43401a0a0d0b4c59401526e542401a0a0d9afb4e401568cd08401a0a0d350152401541f3843f1a0a0dcb0b814015e1b2823f1a0a0d9f35a4401508c6883f1a0a0dce44a74015d31c963f1a0a0dbff6a5401521cb07401a0a0d7f23a04015675437401a0a0d91df9c401511403a4028063a4b0a022d3311cdccccccccccfc3f1a0a0df8f3f4401589271ec01a0a0d9ffecc4015259b21c01a0a0df5bace401582dc27c01a0a0d3a92024115275d26c01a0a0dfeb2054115116b1fc028063a570a022d3211cdccccccccccfc3f1a0a0daead1741153d871dc01a0a0d5be5064115786d20c01a0a0dbcba0741157a7c23c01a0a0ddff50a4115742126c01a0a0dd421264115bf6f21c01a0a0db1e6224115c5ca1ec028063a630a022d3111cdccccccccccfc3f1a0a0d59102c4115e2591bc01a0a0dc3432b41153a7d1bc01a0a0d2c772a411592a01bc01a0a0d0881264115a5831fc01a0a0d63ec344115cd3920c01a0a0d7f843a411564421fc01a0a0dd013404115a0181bc028063a5b0a062d313639353011cdccccccccccfc3f1a0a0d378a9bc015bf5d29401a0a0d4a83a9c01587f114401a0a0de20aabc015d49b11401a0a0d70b2a0c015028cde3f1a0a0d43199fc015b3d2de3f1a0a0d7d829ac015e3d4f23f28063a700a032d323811cdccccccccccfc3f1a0a0d63eefe4015452cfe401a0a0d3655fd4015991afe401a0a0d7234fa4015145efc401a0a0d8a9bfd4015e4b5f7401a0a0d01b11941156c03e2401a0a0d0c851941154e01ea401a0a0d20fc164115ff30f0401a0a0ddbfb114115065ef94028063a570a022d3611cdccccccccccfc3f1a0a0d1905a8bf159442dc401a0a0dcb69aebf15e830dc401a0a0d7dceb4bf153c1fdc401a0a0d0daf9abf159233d9401a0a0d5b4a94bf153e45d9401a0a0daf9094bf156bdeda4028063a670a062d313639343911cdccccccccccfc3f1a0a0dc2da6abf15308d3d401a0a0d8ffd30c015737138401a0a0d168033c015565228401a0a0dd81a2fc0150a0e843f1a0a0d0e48d1bf157cbc833f1a0a0d753049bf15316e883f1a0a0d39ba54bf158658074028063a4f0a062d313639343811cdccccccccccfc3f1a0a0d6697ddbf1559eaca3f1a0a0df660eabf151660ca3f1a0a0d9092e9bf15bc31b73f1a0a0dc82de3bf15de76b73f1a0a0dcd0dddbf15c820be3f28063a7f0a062d313639343711cdccccccccccfc3f1a0a0db53e0dc01593a746401a0a0dc0d516c0158a3d46401a0a0d1ef41cc01525923f401a0a0df4d01cc015ca5f3c401a0a0dba7af4bf15840401401a0a0dcddda2be1599060f401a0a0db7d563be15feb115401a0a0d123873be1578122c401a0a0dd1a132bf1523d03a4028063a370a062d313639343611cdccccccccccfc3f1a0a0df2f641c015d7c820401a0a0d710945c01531761d401a0a0db8d640c0152702044028063a5b0a062d313639333111cdccccccccccfc3f1a0a0deb65ef4015c16f1dc01a0a0d3105c940156ce11fc01a0a0d2f12c940152d1423c01a0a0d721ae940158a3d24c01a0a0d2dbe034115384e22c01a0a0daeb7034115771b1fc028063a670a062d313639333211cdccccccccccfc3f1a0a0da3cfa03f15819030401a0a0dec3d9b3f151ad626401a0a0d15c2a33f15477a873f1a0a0d105cea3f15251f843f1a0a0d32d73e40154714843f1a0a0defd24e4015b975853f1a0a0d54cd4b4015a40e094028063a580a032d313711cdccccccccccfc3f1a0a0d731b94c015630c0f411a0a0d93e49bc015717a0c411a0a0ddad8afc015837002411a0a0ddc4eb1c01580ce00411a0a0d5783acc01502e900411a0a0dfd1398c015d25a054128063a730a062d313639323811cdccccccccccfc3f1a0a0d5c4b3ac0153c2038401a0a0d12b28ec0158fd833401a0a0df32598c015c99f2c401a0a0d65b798c0154612f93f1a0a0da5c394c015df4bb33f1a0a0d4bbc7ec015ecb07a3f1a0a0df20332c015fdf8833f1a0a0d074636c0157515254028063a97010a062d313639333011cdccccccccccfc3f1a0a0de294dc401553be41401a0a0d6797cc4015b8ad40401a0a0df765c14015e5ee3f401a0a0d07dfb440156d172f401a0a0d319da0401546ba0d401a0a0db7c7a14015c3be8e3f1a0a0d40d0c14015d49b8c3f1a0a0d6d00d54015152a8f3f1a0a0de7bed94015ff32963f1a0a0d2caadf4015449dd03f1a0a0d5f49de40153b743b4028063a430a062d313638393811cdccccccccccfc3f1a0a0d4a218c4015ee5520c01a0a0d03971b40155cb825c01a0a0d811486401513df30c01a0a0d74448c4015a3ba26c028063a730a062d313639323711cdccccccccccfc3f1a0a0d747c71c015467427c01a0a0de14e9ac015825a2ac01a0a0d4c3d9ac015dc8c2dc01a0a0d8a9298c015df9b30c01a0a0dbe508dc015d0d632c01a0a0d9de06dc015fee730c01a0a0d159f67c015f36e2dc01a0a0d3fc267c015983c2ac028063a630a022d3811cdccccccccccfc3f1a0a0ddc86563f150c731a411a0a0d1dbf933e15d9111a411a0a0d158400bf151e5216411a0a0da833bebf15470910411a0a0d01a7bdbf151a700e411a0a0d3814a4bf1572930e411a0a0d725f5a3f15edda144128063a670a062d313639333311cdccccccccccfc3f1a0a0d3464f63f155e8020c01a0a0d595dda3e15b2a424c01a0a0df88fdc3e1567092bc01a0a0dd471713f1515d63cc01a0a0d07fc114015042029c01a0a0d8f3d184015f9a625c01a0a0db3b51140154fbb22c028063a8b010a062d313639343011cdccccccccccfc3f1a0a0dfa40313f15449741401a0a0dfac534bf15a0b93d401a0a0ded1a46bf15a27f0d401a0a0dd97739bf15fff2873f1a0a0d69581fbf15ab1b823f1a0a0dc3607a3f15008d843f1a0a0ddc279d3f1571ee853f1a0a0db7d0933f153ea72f401a0a0db5528c3f15504d3c401a0a0dfb857e3f15fb383f4028063a430a062d313639343511cdccccccccccfc3f1a0a0d88b6c7401507e922c01a0a0dfd84bc4015839f23c01a0a0d582bbe401533b826c01a0a0d86c3c74015c81b26c028063a670a062d313639313811cdccccccccccfc3f1a0a0de613a7be15702221c01a0a0d5d9ac9bf150c9624c01a0a0db50dc9bf15c1fa2ac01a0a0d5b1cc2bf151e3c31c01a0a0ddad9efbb158d133dc01a0a0dfd3ec23e15adca28c01a0a0dad25c13e15539825c028063a7f0a062d313639333611cdccccccccccfc3f1a0a0d546f81401513323d401a0a0dbce8424015dd103b401a0a0d2dd13f4015edc237401a0a0d2aa24340151f45893f1a0a0de2a19e401590f78a3f1a0a0da92da040157093913f1a0a0db4109f4015eff10b401a0a0d2cfc8f4015638f34401a0a0d56ae8c401538be3a4028063a5b0a062d313639303311cdccccccccccfc3f1a0a0d72a7194015a2b522c01a0a0d6502cd3f15aede24c01a0a0de1a3f33f15ae4127c01a0a0d3fec1940156a1a29c01a0a0da385604015c1862cc01a0a0d6f1e60401594ef22c028063a430a062d313639353111cdccccccccccfc3f1a0a0d9f6aa3c015e5e32dc01a0a0de103a5c01506042ec01a0a0d56b1a1c015d02834c01a0a0d59c1a1c0154af630c028063a4f0a062d313639333411cdccccccccccfc3f1a0a0d34cacfbf15c6ff24c01a0a0daa9064c01534622ac01a0a0d574a64c015e9c630c01a0a0dd6a73dc0157c8335c01a0a0d8c3dcfbf157b642bc028063a430a062d313639323511cdccccccccccfc3f1a0a0d7d711ec015ba27ce3f1a0a0d8b9f17c0155bffb43f1a0a0dfc8714c0153b9bbb3f1a0a0d43be14c015f065c83f28063a5b0a062d313638383911cdccccccccccfc3f1a0a0d9929214115e3311cc01a0a0dfcc7024115c93d20c01a0a0d719b034115345523c01a0a0da285204115367d2fc01a0a0dd803224115fb7b22c01a0a0d0ffd2141154e491fc028063a370a062d313639323911cdccccccccccfc3f1a0a0ddd51f3bf15c30129c01a0a0dffc96cc01524d72cc01a0a0d5d2313c015860e2dc028063a430a062d313639343411cdccccccccccfc3f1a0a0d12900cc0155243c83f1a0a0dd58e0fc0152b44bb3f1a0a0dda740fc015a8deb43f1a0a0d1b5c0cc0154e78bb3f28063a97010a062d313639313511cdccccccccccfc3f1a0a0d44c70b4115630647401a0a0d6463e440150d9e43401a0a0d433ee14015da3440401a0a0d31dbdf4015e24e33401a0a0ddb20e14015481dcd3f1a0a0dc465ee40154441953f1a0a0dbad8f44015ffb58f3f1a0a0d90021041151076953f1a0a0d16740f41153fe30d401a0a0df8740d4115e0a43d401a0a0db99a0c4115f8ee434028063a640a032d323011cdccccccccccfc3f1a0a0dce25bf4015df7909411a0a0d60c1bd4015450b07411a0a0df5d2bd4015af3e06411a0a0dbc28c64015ec6b02411a0a0d98b0cc401516f600411a0a0d3038ce401583cb01411a0a0dd97bcc4015da5b034128063a4e0a06323630343837118069dc0215fcf73f1a0a0dec4e2cc115bb2995401a0a0d4a8823c115726095401a0a0d6ed623c1156683c7401a0a0d119d2cc115b04cc74028033128c25aec48f0f8bf58093a500a06323630313336118052fffffffff73f1a0a0d71b573c115f9c553401a0a0dbf5a56c115586c5c401a0a0d88645bc11546fef6401a0a0d3abf78c11516abf240280131542246f1d3f4f7bf480158093a500a063235393937321160d5fffffffff73f1a0a0d7b304ac11557036c401a0a0d4b292cc115b97d72401a0a0dc20b30c11533a604411a0a0df2124ec1159a07034128013180e4e57c1845f8bf4801580940a0c9ebbcedd8d0801848e2fd99a9edd8d0801852240a0831365f3431383030120a0d09158ac015dd558dc1120a0dc4cc414215c78987c11a0052240a0831365f3431383031120a0dc4cc414215c78987c1120a0dc4cc4142150abb82c11a0052260a0831365f3431383134120a0dc4cc4142150abb82c1120a0d9a654a4215ae7d82c11a02100152260a0831365f3432363337120a0d9a654a4215ae7d82c1120a0d9b565942150a1382c11a021001523c0a0831365f3431383539120a0daecc88c01576d5a7c1120a0dbffb914115b377a5c1120a0dbbc50d42159dbea3c1120a0d3194594215a87da1c11a0052320a0831365f3431383136120a0d60ab8bc0152b1659c1120a0d3a73f54115416051c1120a0d671f594215a2d44bc11a02100152240a0831365f3431383032120a0dc4cc4142150abb82c1120a0dbd4f8ac015139988c11a0052360a0831365f3431383834120a0d01128dc01508341fc1120a0d80137dc01502191fc1120a0d27e8584215247613c11a0210021a021002522a0a0831365f3431393336120a0d715e85c115c30d22c1120a0d1e2056c115154a21c11a0210021a02100252260a0831365f3432313533120a0dd79785c1153a8b5bc1120a0daafe54c1153b8d5ac11a02100152320a0831365f3432313531120a0d7bd785c115cacdd4c0120a0d889f63c11558add3c0120a0d715c56c1150c4dd3c01a02100152260a0831365f3432363438120a0d8c9756c115ed8b4cc0120a0d2d4e86c115c2504fc01a02100152320a0831365f3431383738120a0d616a8ec0159c3dcfc0120a0d00e4c34115d61bc2c0120a0d23b058421552a1b4c01a02100152480a0831365f3431373933120a0d3ebb8fc0150f0045c0120a0df741884115e32329c0120a0d29a9e3411539941ac0120a0ddec5104215b72516c0120a0daf78584215f4f306c01a0052240a0831365f3432363437120a0dcadc56c1154e9b4b3f120a0d62d986c1159fcf423f1a00523c0a0831365f3431383731120a0d0f6f5842155959bfbf120a0d386edb4115798502c0120a0d06709c411532da08c0120a0d0ca48ec0159a691bc01a0052240a0831365f3431383537120a0d623c5842150e45de3f120a0d941389c015b613633f1a0052240a0831365f3431383130120a0dbd4f8ac015139988c1120a0ddde253c115185989c11a0052240a0831365f3432363738120a0d91ba53c1158b558dc1120a0d09158ac015dd558dc11a0052240a0831365f3431383538120a0d38a552c11592c4a8c1120a0daecc88c01576d5a7c11a0052240a0831365f3431383732120a0d9e499fc01524a145c0120a0d3ebb8fc0150f0045c01a0052260a0831365f3431373936120a0d9e499fc01524a145c0120a0d8d4dd4c015fafc46c01a02100252260a0831365f3432363532120a0d8d4dd4c015fafc46c0120a0d8c9756c115ed8b4cc01a02100152240a0831365f3431373931120a0d0ca48ec0159a691bc0120a0dc38da0c0155ed81bc01a0052240a0831365f3431373837120a0dc38da0c0155ed81bc0120a0d9e499fc01524a145c01a0052240a0831365f3431373932120a0d941389c015b613633f120a0dcadc56c1154e9b4b3f1a0052360a0831365f3432313337120a0dac9a28c21507fa27c1120a0d8df500c2153aac25c1120a0d715e85c115c30d22c11a0210011a02100152260a0831365f3432313530120a0dd28528c215113ce0c0120a0d7bd785c115cacdd4c01a02100152320a0831365f3432313438120a0d2d4e86c115c2504fc0120a0d271422c21539c762c0120a0d367128c215339863c01a02100152240a0831365f3432313434120a0d62d986c1159fcf423f120a0d2af301c215e5b11a3f1a0052240a0831365f3432313435120a0d2af301c215e5b11a3f120a0df05828c215210f023f1a005a0a0d98b438bf158fd5dabc625e0a074a445a3030343712114d565852434d314632584c4330303133371a4033336665376165353031373134346362393766303963303639353530303636333136663937653138373230613436313562636138613565653930383237613635720708f6baf9e2ab327a230a07365f343130333410021a0a0d1e2056c115154a21c11a0a0d91ba53c1158b558dc17a230a07365f343130333510021a0a0d3ebb8fc0150f0045c01a0a0d01128dc01508341fc1800196df808cecd8d08018";
//        String hexData = "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";
//        String hexData = "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";
//        String hexData = "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";
        String hexData = "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";
        byte[] bytes = hexStringToByteArray(hexData);
        LocalView.LocalViewDTO dto = LocalView.LocalViewDTO.parseFrom(bytes);
        boolean result = IntersectionStuckService.isStopLineWithDistance(2,  dto);
        System.out.println("距离停止线结果:" + result);
        System.out.println(JsonFormat.printer().print(dto));
    }

    @Test
    public void test_isStopLineWithDistanceTime() throws InvalidProtocolBufferException {
        String hexData = "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";
        byte[] bytes = hexStringToByteArray(hexData);
        LocalView.LocalViewDTO dto = LocalView.LocalViewDTO.parseFrom(bytes);
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        for (int i = 0; i < 100000; i++) {
            try {
                boolean stopLineWithDistance = IntersectionStuckService.isStopLineWithDistance(2, dto);
//                System.out.println(stopLineWithDistance);
//            System.out.println(JsonFormat.printer().print(dto));
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        stopWatch.stop();
        System.out.println("总耗时: " + stopWatch.getTotal(TimeUnit.MILLISECONDS) + " 毫秒");
    }

    @Test
    public void test_getLogicStrategy() throws InvalidProtocolBufferException {
//        String hexData = "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";
//        String hexData = "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";
//        String hexData = "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";
        String hexData = "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";
        byte[] bytes = hexStringToByteArray(hexData);

        LocalView.LocalViewDTO dto = LocalView.LocalViewDTO.parseFrom(bytes);
        IntersectionStuckService.LogicStrategyEnum logicStrategy = IntersectionStuckService.getLogicStrategy(dto);
        System.out.println("选择策略:" + logicStrategy);
        System.out.println(JsonFormat.printer().print(dto));

    }


    @Test
    public void test_vehicleLocationQueue() {
        CircularFifoQueue<IntersectionStuckService.VehicleLocation> vehicleLocationQueue = new CircularFifoQueue<>(31);
        for (int i = 0; i < 16; i++) {
            IntersectionStuckService.VehicleLocation vehicleLocation = new IntersectionStuckService.VehicleLocation(0d + i, 0d + i, 0d + i, 1L);
            vehicleLocationQueue.add(vehicleLocation);
        }
        System.out.println("队列大小:" + vehicleLocationQueue.size());
        vehicleLocationQueue.clear();
        System.out.println("队列大小:" + vehicleLocationQueue.size());
    }

    @Test
    public void test_computeDistance() {
        CircularFifoQueue<IntersectionStuckService.VehicleLocation> vehicleLocationQueue = new CircularFifoQueue<>(31);
        for (int i = 0; i < 16; i++) {
            IntersectionStuckService.VehicleLocation vehicleLocation = new IntersectionStuckService.VehicleLocation(0d + i, 0d + i, 0d + i, 1L);
            vehicleLocationQueue.add(vehicleLocation);
        }
        if (vehicleLocationQueue.size() <= 15) {
            return ;
        }
        System.out.println("队列大小" + vehicleLocationQueue.size());
        double distance = IntersectionStuckService.computeDistance(vehicleLocationQueue, 15, vehicleLocationQueue.size());
        System.out.println("计算距离为:" + distance);
        System.out.println("执行完成");

        for (int i = 0; i < 16; i++) {
            IntersectionStuckService.VehicleLocation vehicleLocation = new IntersectionStuckService.VehicleLocation(0d + i, 0d + i, 0d, 1L);
            vehicleLocationQueue.add(vehicleLocation);
        }
        if (!vehicleLocationQueue.isAtFullCapacity()) {
            return ;
        }
        System.out.println(vehicleLocationQueue.size());
        System.out.println("计算距离为:" + IntersectionStuckService.computeDistance(vehicleLocationQueue, 30, vehicleLocationQueue.size()));
        System.out.println("===============");
    }

    @Test
    public void test_isSpeedLess() {
        CircularFifoQueue<IntersectionStuckService.VehicleLocation> vehicleLocationQueue = new CircularFifoQueue<>(31);
        for (int i = 0; i < 16; i++) {
            IntersectionStuckService.VehicleLocation vehicleLocation = new IntersectionStuckService.VehicleLocation(0d + i, 0d + i, 0d, 1L);
            vehicleLocationQueue.add(vehicleLocation);
        }
        if (vehicleLocationQueue.size() <= 15) {
            return ;
        }
        boolean speedLessResult = IntersectionStuckService.isSpeedLess(vehicleLocationQueue, 15, 0.2, vehicleLocationQueue.size());
        System.out.println(vehicleLocationQueue.size());
        System.out.println("限速结果:" + speedLessResult);

        for (int i = 0; i < 16; i++) {
            IntersectionStuckService.VehicleLocation vehicleLocation = new IntersectionStuckService.VehicleLocation(0d + i, 0d + i, 0d, 1L);
            vehicleLocationQueue.add(vehicleLocation);
        }
        if (!vehicleLocationQueue.isAtFullCapacity()) {
            return ;
        }
        System.out.println(vehicleLocationQueue.size());
        System.out.println("限速结果:" + IntersectionStuckService.isSpeedLess(vehicleLocationQueue, 30, 0.5, vehicleLocationQueue.size()));
        System.out.println("===============");

    }

    @Test
    public void test_isSpeedEqualZero() {
        CircularFifoQueue<IntersectionStuckService.VehicleLocation> vehicleLocationQueue = new CircularFifoQueue<>(31);
        for (int i = 0; i < 9; i++) {
            IntersectionStuckService.VehicleLocation vehicleLocation = new IntersectionStuckService.VehicleLocation(0d + i, 0d + i, 0d, 1L);
            vehicleLocationQueue.add(vehicleLocation);
        }
        if (vehicleLocationQueue.size() > 8) {
            System.out.println("队列大小:" + vehicleLocationQueue.size());
            boolean speedEqualZeroResult = IntersectionStuckService.isSpeedEqualZero(vehicleLocationQueue, 8, vehicleLocationQueue.size());
            System.out.println("结果:" + speedEqualZeroResult);
        }
    }






        // 将16进制字符串转换为字节数组
    public static byte[] hexStringToByteArray(String s) {
        int len = s.length();
        byte[] data = new byte[len / 2];
        for (int i = 0; i < len; i += 2) {
            data[i / 2] = (byte) ((Character.digit(s.charAt(i), 16) << 4)
                    + Character.digit(s.charAt(i+1), 16));
        }
        return data;
    }
}