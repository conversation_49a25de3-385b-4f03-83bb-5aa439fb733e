/*
 * Copyright (c) 2021 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.server.business.service;

import com.google.protobuf.util.JsonFormat;
import com.jdx.rover.metadata.api.domain.dto.vehicle.VehicleMonitorBasicDto;
import com.jdx.rover.server.common.utils.proto.ProtoUtils;
import jdx.rover.guardian.data.dto.GuardianDataDto;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * <AUTHOR>
 */
@Slf4j
@SpringBootTest
public class GuardianServiceTest {
  private static final long NANOSECOND_TO_MILLISECOND = 1000000L;

  private static String vehicleName = "JD40005";

  @Autowired
  private GuardianService guardianService;

  @Test
  void receive() throws Exception {
    VehicleMonitorBasicDto vehicleMonitorBasicDto = new VehicleMonitorBasicDto();
    vehicleMonitorBasicDto.setName("JD0055");

    GuardianDataDto.GuardianInfoDTO infoOrign = buildGuardianInfoDTO();
    byte[] result = infoOrign.toByteArray();
    // 反序列化
    GuardianDataDto.GuardianInfoDTO infoObj = GuardianDataDto.GuardianInfoDTO.parseFrom(result);
    String message = ProtoUtils.protoToJson(infoObj);
    System.out.println(message);
    guardianService.receive(infoObj);

//    GuardianInfoDTO guardianInfoDTO = JsonUtils.readValue(message, GuardianInfoDTO.class);
//
//    Method checkParameterMethod = guardianService.getClass().getDeclaredMethod("checkParameter", GuardianInfoDTO.class);
//    checkParameterMethod.setAccessible(true);
//    checkParameterMethod.invoke(guardianService, guardianInfoDTO);
//
//    Method convertGuardianInfoProtoToVoMethod = guardianService.getClass()
//        .getDeclaredMethod("convertGuardianInfoProtoToVo", GuardianInfoDTO.class);
//    convertGuardianInfoProtoToVoMethod.setAccessible(true);
//    GuardianInfoAddVo vo = (GuardianInfoAddVo) convertGuardianInfoProtoToVoMethod.invoke(guardianService,
//        guardianInfoDTO);
//
//    assertThat(vo.getVehicleName()).isEqualTo(infoOrign.getVehicleId());
//    assertThat(vo.getReportTime()).isNotNull();
//
//    assertThat(vo.getDrivableDirectionRequest().getEnableBack())
//        .isEqualTo(infoOrign.getDrivableDirection().getEnableBack());
//    assertThat(vo.getDrivableDirectionRequest().getEnableFront())
//        .isEqualTo(infoOrign.getDrivableDirection().getEnableFront());
//    assertThat(vo.getDrivableDirectionRequest().getEnableLeft())
//        .isEqualTo(infoOrign.getDrivableDirection().getEnableLeft());
//    assertThat(vo.getDrivableDirectionRequest().getEnableRight())
//        .isEqualTo(infoOrign.getDrivableDirection().getEnableRight());
//
//    assertThat(vo.getVehicleExceptionLogSaveRequestList()).hasSameSizeAs(infoOrign.getAbnormalList());
//    for (int i = 0; i < infoOrign.getAbnormalList().size(); i++) {
//      VehicleExceptionLogAddVo actual = vo.getVehicleExceptionLogSaveRequestList().get(i);
//      GuardianDataDto.Abnormal expect = infoOrign.getAbnormalList().get(i);
//      assertThat(actual.getErrorCode()).isEqualTo(String.valueOf(expect.getErrorCode()));
//      assertThat(actual.getErrorLevel()).isEqualTo(expect.getErrorLevel());
//      assertThat(actual.getErrorMessage()).isEqualTo(expect.getErrorMsg());
//      assertThat(actual.getModule()).isEqualTo(expect.getModuleName());
//      assertThat(actual.getTimestamp()).isNotNull();
//    }
//
//    assertThat(vo.getVehicleStatusRequest().getChassisStatus()).isEqualTo(infoOrign.getChassisStatus().getDrivingMode().name());
//    assertThat(vo.getVehicleStatusRequest().getHeading()).isEqualTo(infoOrign.getChassisStatus().getHeading());
//    assertThat(vo.getVehicleStatusRequest().getLastUpdateTimeForChassis()).isNotNull();
//    assertThat(vo.getVehicleStatusRequest().getLastUpdateTimeForSolution()).isNotNull();
//    assertThat(vo.getVehicleStatusRequest().getLon()).isEqualTo(infoOrign.getChassisStatus().getLat(), Offset.offset(11.11));
//    assertThat(vo.getVehicleStatusRequest().getLat()).isEqualTo(infoOrign.getChassisStatus().getLon(), Offset.offset(11.11));
//    assertThat(vo.getVehicleStatusRequest().getPowerUsage()).isEqualTo(infoOrign.getChassisStatus().getBatteryCapacity(), Offset.offset(0.01));
//    assertThat(vo.getVehicleStatusRequest().getSolutionStatus()).isEqualTo(infoOrign.getSolutionStatus().getState().name());
//    assertThat(vo.getVehicleStatusRequest().getSpeed()).isEqualTo(infoOrign.getChassisStatus().getVelocity(), Offset.offset(0.01));
//
//    assertThat(vo.getLaunchStatusSaveRequestList()).hasSameSizeAs(infoOrign.getLaunchStatusList());
//    for (int i = 0; i < infoOrign.getLaunchStatusList().size(); i++) {
//      LaunchStatusAddVo actual = vo.getLaunchStatusSaveRequestList().get(i);
//      GuardianDataDto.LaunchStatus expect = infoOrign.getLaunchStatusList().get(i);
////      assertThat(actual.getReportTime()).isNotNull();
//      assertThat(actual.getState()).isEqualTo(expect.getState().name());
//      assertThat(actual.getCurrentStartingModuleName()).isEqualTo(expect.getCurrentStartingModuleName());
//      assertThat(actual.getIp()).isEqualTo(expect.getIp());
//      assertThat(actual.getRequiredModuleList()).hasSameSizeAs(expect.getNoexistRequiredModuleList());
//    }
//
//    assertThat(vo.getNavigationPncStatusRequest().getRoutingStatus().getStopResultList()).hasSameSizeAs(infoOrign.getRoutingStatus().getStopResultList());
//    for (int i = 0; i < infoOrign.getPlanningStatus().getInterestedLightList().size(); i++) {
//      NavigationStopResult actual = vo.getNavigationPncStatusRequest().getRoutingStatus().getStopResultList().get(i);
//      GuardianDataDto.StopResult expect = infoOrign.getRoutingStatus().getStopResultList().get(i);
//      assertThat(actual.getRoutingPositionList()).hasSameSizeAs(expect.getRoutingPointList());
//      for (int j = 0; j < expect.getRoutingPointList().size(); j++) {
//        assertThat(actual.getRoutingPositionList().get(j).getLatitude()).isEqualTo(expect.getRoutingPointList().get(j).getLatitude(), Offset.offset(0.01));
//        assertThat(actual.getRoutingPositionList().get(j).getLongitude()).isEqualTo(expect.getRoutingPointList().get(j).getLongitude(), Offset.offset(0.01));
//        assertThat(actual.getRoutingPositionList().get(j).getMapX()).isEqualTo(expect.getRoutingPointList().get(j).getMapX(), Offset.offset(0.01));
//        assertThat(actual.getRoutingPositionList().get(j).getMapY()).isEqualTo(expect.getRoutingPointList().get(j).getMapY(), Offset.offset(0.01));
//        assertThat(actual.getRoutingPositionList().get(j).getXcsX()).isEqualTo(expect.getRoutingPointList().get(j).getXcsX(), Offset.offset(0.01));
//        assertThat(actual.getRoutingPositionList().get(j).getXcsY()).isEqualTo(expect.getRoutingPointList().get(j).getXcsY(), Offset.offset(0.01));
//      }
//      assertThat(actual.getStopId()).isEqualTo(expect.getStopId());
//      assertThat(actual.getRoutingMileage()).isEqualTo(expect.getRoutingMileage(), Offset.offset(0.01));
//    }
//    assertThat(vo.getNavigationPncStatusRequest().getRoutingStatus().getType()).isEqualTo(infoOrign.getRoutingStatus().getResultCode().getType().name());
//    assertThat(vo.getNavigationPncStatusRequest().getRoutingStatus().getMsg()).isEqualTo(infoOrign.getRoutingStatus().getResultCode().getMsg().name());
//    assertThat(vo.getNavigationPncStatusRequest().getRoutingStatus().getRoutingId()).isEqualTo(infoOrign.getRoutingStatus().getRoutingId());
//    assertThat(vo.getNavigationPncStatusRequest().getRoutingStatus().getCurrentStopIndex()).isEqualTo(infoOrign.getRoutingStatus().getCurrentStopIndex());
//    assertThat(vo.getNavigationPncStatusRequest().getRoutingStatus().getGlobalMileage()).isEqualTo(infoOrign.getRoutingStatus().getGlobalMileage(), Offset.offset(0.01));
//    assertThat(vo.getNavigationPncStatusRequest().getRoutingStatus().getNavSequenceNum()).isEqualTo(infoOrign.getRoutingStatus().getNaviSequenceNum());
//    assertThat(vo.getNavigationPncStatusRequest().getRoutingStatus().getNaviRefreshId()).isEqualTo(infoOrign.getRoutingStatus().getNaviRefreshId());
  }

  //josn转protobuf
  @Test
  void protoToJson() throws Exception {
    GuardianDataDto.GuardianInfoDTO info = buildGuardianInfoDTO();
    byte[] result = info.toByteArray();
    //返序列化
    GuardianDataDto.GuardianInfoDTO guardianInfoDTO = GuardianDataDto.GuardianInfoDTO.parseFrom(result);
//    System.out.println("直接打印对象");
//    System.out.println(guardianInfoDTO);

    System.out.println("打印JSON对象");
    String jsonFormat = JsonFormat.printer().print(guardianInfoDTO);
    System.out.println(jsonFormat);
  }

  public static GuardianDataDto.GuardianInfoDTO buildGuardianInfoDTO() {
    GuardianDataDto.DrivableDirection.Builder drivableDirectionBuilder = GuardianDataDto.DrivableDirection.newBuilder();
    drivableDirectionBuilder.setEnableFront(true);
    drivableDirectionBuilder.setEnableBack(true);
    drivableDirectionBuilder.setEnableLeft(true);
    drivableDirectionBuilder.setEnableRight(true);

    GuardianDataDto.Abnormal.Builder abnormalBuilder = GuardianDataDto.Abnormal.newBuilder();
    abnormalBuilder.setTimestamp(System.currentTimeMillis() * NANOSECOND_TO_MILLISECOND);
    abnormalBuilder.setErrorCode(-13000);
    abnormalBuilder.setErrorLevel("FATAL");
    abnormalBuilder.setErrorMsg("ErrorCode: ERROR_GUARDIAN_NOT_INITIALIZED, Message: Component not initialized.");
    abnormalBuilder.setModuleName("LOCALIZATION_INIT");

    GuardianDataDto.Abnormal.Builder abnormalBuilder2 = GuardianDataDto.Abnormal.newBuilder();
    abnormalBuilder2.setTimestamp(System.currentTimeMillis() * NANOSECOND_TO_MILLISECOND);
    abnormalBuilder2.setErrorCode(-1350);
    abnormalBuilder2.setErrorLevel("FATAL");
    abnormalBuilder2.setErrorMsg("ErrorCode: ERROR_GUARDIAN_COMPONENT_NOT_EXIST, Detail: [ Not found required following component: CONTROL PNC_STATUS PLANNING DRIVERS_JOYSTICK ARTIFICIAL_OBSTACLE PREDICTION ].");
    abnormalBuilder2.setModuleName("10.10.1.103@ComponentExistance");

    GuardianDataDto.SolutionStatus.Builder solutionStatusBuilder = GuardianDataDto.SolutionStatus.newBuilder();
    solutionStatusBuilder.setState(GuardianDataDto.SolutionStatus.SolutionState.valueOf("REPAIRING"));

    GuardianDataDto.ChassisStatus.Builder chassisStatusBuilder = GuardianDataDto.ChassisStatus.newBuilder();
    chassisStatusBuilder.setLat(11.11f);
    chassisStatusBuilder.setLon(11.11f);
    GuardianDataDto.ChassisStatus.DrivingMode drivingMode = GuardianDataDto.ChassisStatus.DrivingMode.valueOf("DRIVEMODE_ENGAGED_GUARDIAN_NORMAL");
    chassisStatusBuilder.setDrivingMode(drivingMode);
    chassisStatusBuilder.setHeading(11.11);
    chassisStatusBuilder.setBatteryCapacity(80.4000015258789);
    chassisStatusBuilder.setVelocity(11.11);

    GuardianDataDto.WheelEntity.Builder wheelEntityBuilder = GuardianDataDto.WheelEntity.newBuilder();
    wheelEntityBuilder.setPosition(GuardianDataDto.WheelEntity.WheelPosition.valueOf("WHEEL_POSITION_FRONT_LEFT"));
    wheelEntityBuilder.setPressureStatus(GuardianDataDto.WheelEntity.PressureStatus.valueOf("TIRE_PRESSURE_UNDER"));
    wheelEntityBuilder.setPressure(240);

//    wheelEntityBuilder.setTemperature(temperature);

    GuardianDataDto.LaunchStatus.Builder launchStatusBuilder = GuardianDataDto.LaunchStatus.newBuilder();
    launchStatusBuilder.setState(GuardianDataDto.LaunchStatus.LaunchState.valueOf("STARTING"));
    launchStatusBuilder.setCurrentStartingModuleName("LOCALIZATION_INIT");
    launchStatusBuilder.setIp("***********");
    launchStatusBuilder.addNoexistRequiredModule("test11");
    launchStatusBuilder.addNoexistRequiredModule("test12");

    GuardianDataDto.PlanningStatus.ResultCode.Builder resultCodeBuilder = GuardianDataDto.PlanningStatus.ResultCode.newBuilder();
    resultCodeBuilder.setMsg(GuardianDataDto.PlanningStatus.ResultCode.Msg.valueOf("ACTIVE_PLANNING_TOO_MANY_FAILURE"));
    resultCodeBuilder.setType(GuardianDataDto.PlanningStatus.ResultCode.Type.valueOf("NOTREADY"));

    GuardianDataDto.PlanningTrafficLightInfo.Builder interestedLightBuilder = GuardianDataDto.PlanningTrafficLightInfo.newBuilder();
    interestedLightBuilder.setId("test11");
    interestedLightBuilder.setSource(GuardianDataDto.PlanningTrafficLightInfo.StateSource.valueOf("SUPERVISOR"));

    GuardianDataDto.RoutingPosition.Builder routingPositionBuilder = GuardianDataDto.RoutingPosition.newBuilder();
    routingPositionBuilder.setLatitude(11.11);
    routingPositionBuilder.setLongitude(11.11);
    routingPositionBuilder.setMapX(11.11);
    routingPositionBuilder.setMapY(11.11);
    routingPositionBuilder.setXcsX(11.11);
    routingPositionBuilder.setXcsY(11.11);

    GuardianDataDto.StopResult.Builder stopResultBuilder = GuardianDataDto.StopResult.newBuilder();
    stopResultBuilder.addRoutingPoint(routingPositionBuilder.build());
    stopResultBuilder.setStopId(11);
    stopResultBuilder.setRoutingMileage(11.11);

    GuardianDataDto.RoutingStatus.ResultCode.Builder routingResultCodeBuilder = GuardianDataDto.RoutingStatus.ResultCode.newBuilder();
    routingResultCodeBuilder.setMsg(GuardianDataDto.RoutingStatus.ResultCode.Msg.valueOf("PROCESSING_SMOTHING"));
    routingResultCodeBuilder.setType(GuardianDataDto.RoutingStatus.ResultCode.Type.valueOf("OK"));

    GuardianDataDto.RoutingStatus.Builder routingStatusBuilder = GuardianDataDto.RoutingStatus.newBuilder();
    routingStatusBuilder.addStopResult(stopResultBuilder.build());
    routingStatusBuilder.setResultCode(routingResultCodeBuilder.build());
    routingStatusBuilder.setRoutingId(11);
    routingStatusBuilder.setCurrentStopIndex(11);
    routingStatusBuilder.setGlobalMileage(11.11);
    routingStatusBuilder.setMileageToNextStop(11.11);
    routingStatusBuilder.setCurrentStopFinishedMileage(22.22);
    routingStatusBuilder.setNaviSequenceNum(11);
    routingStatusBuilder.setNaviRefreshId(11);

    GuardianDataDto.GuardianInfoDTO.Builder guardianInfoBuilder = GuardianDataDto.GuardianInfoDTO.newBuilder();
    guardianInfoBuilder.setVehicleId(vehicleName);
    guardianInfoBuilder.setTimestamp(System.currentTimeMillis() * 1000000L + System.nanoTime() % 1000000L);
    guardianInfoBuilder.setDrivableDirection(drivableDirectionBuilder);
    guardianInfoBuilder.addAbnormal(abnormalBuilder);
    guardianInfoBuilder.addAbnormal(abnormalBuilder2);
    guardianInfoBuilder.setSolutionStatus(solutionStatusBuilder);
    guardianInfoBuilder.setChassisStatus(chassisStatusBuilder);
    guardianInfoBuilder.addWheelEntity(wheelEntityBuilder);
    guardianInfoBuilder.addLaunchStatus(launchStatusBuilder);
    guardianInfoBuilder.setRoutingStatus(routingStatusBuilder);

    GuardianDataDto.GuardianInfoDTO info = guardianInfoBuilder.build();
    return info;
  }
}