/*
 * Copyright (c) 2021 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.server.business.manager;

import com.jdx.rover.server.business.service.GuardianService;
import com.jdx.rover.server.business.service.GuardianServiceTest;
import jdx.rover.guardian.data.dto.GuardianDataDto;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * <AUTHOR>
 */
@Slf4j
@SpringBootTest
class GuardianPncManagerTest {

  @Autowired
  private GuardianPncManager guardianPncManager;

  @Autowired
  private GuardianService guardianService;

  @Test
  void handle() throws Exception {
    GuardianDataDto.GuardianInfoDTO info = GuardianServiceTest.buildGuardianInfoDTO();
//    String message = JsonFormat.printer().print(info);
//    GuardianInfoDTO dto = JsonUtils.readValue(message, GuardianInfoDTO.class);
//
//    Method convertGuardianInfoProtoToVoMethod = guardianService.getClass().getDeclaredMethod("convertGuardianInfoProtoToVo", GuardianInfoDTO.class);
//    convertGuardianInfoProtoToVoMethod.setAccessible(true);
//    GuardianInfoAddVo vo = (GuardianInfoAddVo) convertGuardianInfoProtoToVoMethod.invoke(guardianService, dto);
//    VehiclePncInfoDTO vehiclePncInfoDTO = guardianPncManager.handle(vo);
//
//    assertThat(vehiclePncInfoDTO.getRecordTime()).isNotNull();
//    assertThat(vehiclePncInfoDTO.getNaviRefreshId()).isEqualTo(info.getRoutingStatus().getNaviRefreshId());
//    assertThat(vehiclePncInfoDTO.getGlobalMileage()).isEqualTo(info.getRoutingStatus().getGlobalMileage(), Offset.offset(0.01));
//
//    assertThat(vehiclePncInfoDTO.getTrafficLightInfo()).hasSameSizeAs(info.getPlanningStatus().getInterestedLightList());
//    for (int i = 0; i < info.getPlanningStatus().getInterestedLightList().size(); i++) {
//      VehiclePncTrafficLightInfoDTO actual = vehiclePncInfoDTO.getTrafficLightInfo().get(i);
//      GuardianDataDto.PlanningTrafficLightInfo expect = info.getPlanningStatus().getInterestedLightList().get(i);
//      assertThat(actual.getId()).isEqualTo(expect.getId());
//      assertThat(actual.getTrafficLightSource()).isEqualTo(expect.getSource().name());
//    }
//
//    assertThat(vehiclePncInfoDTO.getNavigationStop()).hasSameSizeAs(info.getRoutingStatus().getStopResultList());
//    for (int i = 0; i < info.getRoutingStatus().getStopResultList().size(); i++) {
//      VehiclePncNavigationStopInfoDTO actual = vehiclePncInfoDTO.getNavigationStop().get(i);
//      GuardianDataDto.StopResult expect = info.getRoutingStatus().getStopResultList().get(i);
//      assertThat(actual.getStopId()).isEqualTo(expect.getStopId());
//      assertThat(actual.getRoutingMileage()).isEqualTo(expect.getRoutingMileage(), Offset.offset(0.01));
//
//      assertThat(actual.getRouting()).hasSameSizeAs(expect.getRoutingPointList());
//      for (int j = 0; j < expect.getRoutingPointList().size(); j++) {
//        VehiclePncNavigationRoutingPositionDTO actualPosition = actual.getRouting().get(j);
//        GuardianDataDto.RoutingPosition expectPosition = expect.getRoutingPointList().get(j);
//        assertThat(actualPosition.getLat()).isEqualTo(expectPosition.getLatitude(), Offset.offset(0.01));
//        assertThat(actualPosition.getLon()).isEqualTo(expectPosition.getLongitude(), Offset.offset(0.01));
//      }
//    }
  }
}