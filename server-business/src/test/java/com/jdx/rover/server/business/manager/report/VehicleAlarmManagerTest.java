/*
 * Copyright (c) 2023 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.server.business.manager.report;

import lombok.RequiredArgsConstructor;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * 注
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/28
 */
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@SpringBootTest
class VehicleAlarmManagerTest {

    private final VehicleAlarmManager vehicleAlarmManager;

    @Test
    void handStopAlarm() {
        vehicleAlarmManager.handStopAlarm();
    }
}