package com.jdx.rover.server.business.manager;

import com.jdx.rover.server.api.domain.dto.hardware.data.SimDataDTO;
import com.jdx.rover.server.api.domain.dto.hardware.status.SimStatusDTO;
import com.jdx.rover.server.api.domain.dto.report.alarm.VehicleAlarmEventDTO;
import com.jdx.rover.server.api.domain.enums.guardian.AlarmTypeEnum;
import com.jdx.rover.server.business.manager.report.VehicleAlarmManager;
import com.jdx.rover.server.repository.redis.RedissonRepository;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * SIM卡异常告警管理器测试
 *
 * <AUTHOR>
 * @date 2025-08-27
 */
@ExtendWith(MockitoExtension.class)
@Slf4j
public class VehicleSimCardManagerTest {

    @Mock
    private VehicleAlarmManager vehicleAlarmManager;

    @Mock
    private RedissonRepository redissonRepository;

    @InjectMocks
    private VehicleSimCardManager vehicleSimCardManager;

    private String vehicleName;
    private Date recordTime;
    private Map<String, VehicleAlarmEventDTO> alarmMapDb;

    @BeforeEach
    void setUp() {
        vehicleName = "test_vehicle_001";
        recordTime = new Date();
        alarmMapDb = new HashMap<>();
    }

    @Test
    void testHandleSimCardAbnormal_OfflineStatus() {
        // 测试条件1：SIM卡离线状态
        SimStatusDTO simStatus = new SimStatusDTO();
        simStatus.setOnlineStatus(1); // 1-离线
        simStatus.setDeviceId(1);
        simStatus.setRecordTime(recordTime);

        List<SimStatusDTO> simStatusList = List.of(simStatus);

        vehicleSimCardManager.handleSimCardAbnormal(vehicleName, simStatusList, null, alarmMapDb, recordTime);

        // 验证生成告警
        verify(vehicleAlarmManager, times(1)).addAlarm(eq(vehicleName), any(VehicleAlarmEventDTO.class));
        log.info("测试SIM卡离线状态告警生成 - 通过");
    }

    @Test
    void testHandleSimCardAbnormal_4GDuration() {
        // 测试条件2：4G状态持续10秒
        SimDataDTO simData = SimDataDTO.builder()
                .vehicleName(vehicleName)
                .standard("LTE")
                .deviceId(1)
                .recordTime(recordTime)
                .build();

        List<SimDataDTO> simDataList = List.of(simData);

        // 模拟Redis ZSet操作
        when(redissonRepository.addToScoredSortedSet(anyString(), anyString(), anyDouble())).thenReturn(true);
        when(redissonRepository.expireScoredSortedSet(anyString(), anyLong())).thenReturn(true);
        when(redissonRepository.removeRangeByScore(anyString(), anyDouble(), anyDouble())).thenReturn(1);
        when(redissonRepository.countByScore(anyString(), anyDouble(), anyDouble())).thenReturn(10); // 满足10秒条件

        vehicleSimCardManager.handleSimCardAbnormal(vehicleName, null, simDataList, alarmMapDb, recordTime);

        // 验证生成告警
        verify(vehicleAlarmManager, times(1)).addAlarm(eq(vehicleName), any(VehicleAlarmEventDTO.class));
        log.info("测试4G状态持续时间告警生成 - 通过");
    }

    @Test
    void testHandleSimCardAbnormal_5GSignalQuality() {
        // 测试条件3：5G信号质量差持续5秒
        SimDataDTO simData = SimDataDTO.builder()
                .vehicleName(vehicleName)
                .standard("5G NR")
                .rsrp(-110.0f) // 小于-100阈值
                .sinr(2.0f)    // 小于3阈值
                .deviceId(1)
                .recordTime(recordTime)
                .build();

        List<SimDataDTO> simDataList = List.of(simData);

        // 模拟Redis ZSet操作
        when(redissonRepository.addToScoredSortedSet(anyString(), anyString(), anyDouble())).thenReturn(true);
        when(redissonRepository.expireScoredSortedSet(anyString(), anyLong())).thenReturn(true);
        when(redissonRepository.removeRangeByScore(anyString(), anyDouble(), anyDouble())).thenReturn(1);
        when(redissonRepository.countByScore(anyString(), anyDouble(), anyDouble())).thenReturn(5); // 满足5秒条件

        vehicleSimCardManager.handleSimCardAbnormal(vehicleName, null, simDataList, alarmMapDb, recordTime);

        // 验证生成告警
        verify(vehicleAlarmManager, times(1)).addAlarm(eq(vehicleName), any(VehicleAlarmEventDTO.class));
        log.info("测试5G信号质量差告警生成 - 通过");
    }

    @Test
    void testHandleSimCardAbnormal_AlarmRemoval_OnlineStatus() {
        // 测试告警消除条件1：SIM卡在线状态
        VehicleAlarmEventDTO existingAlarm = new VehicleAlarmEventDTO();
        existingAlarm.setType(AlarmTypeEnum.SIM_CARD_ABNORMAL.getValue());
        alarmMapDb.put(AlarmTypeEnum.SIM_CARD_ABNORMAL.getValue(), existingAlarm);

        SimStatusDTO simStatus = new SimStatusDTO();
        simStatus.setOnlineStatus(2); // 2-在线
        simStatus.setDeviceId(1);
        simStatus.setRecordTime(recordTime);

        List<SimStatusDTO> simStatusList = List.of(simStatus);

        vehicleSimCardManager.handleSimCardAbnormal(vehicleName, simStatusList, null, alarmMapDb, recordTime);

        // 验证消除告警
        verify(vehicleAlarmManager, times(1)).removeAlarm(vehicleName, AlarmTypeEnum.SIM_CARD_ABNORMAL.getValue());
        log.info("测试SIM卡在线状态告警消除 - 通过");
    }

    @Test
    void testHandleSimCardAbnormal_AlarmRemoval_No4G() {
        // 测试告警消除条件2：没有4G状态
        VehicleAlarmEventDTO existingAlarm = new VehicleAlarmEventDTO();
        existingAlarm.setType(AlarmTypeEnum.SIM_CARD_ABNORMAL.getValue());
        alarmMapDb.put(AlarmTypeEnum.SIM_CARD_ABNORMAL.getValue(), existingAlarm);

        SimDataDTO simData = SimDataDTO.builder()
                .vehicleName(vehicleName)
                .standard("5G NR") // 非4G状态
                .rsrp(-90.0f)      // 信号质量正常
                .sinr(5.0f)        // 信号质量正常
                .deviceId(1)
                .recordTime(recordTime)
                .build();

        List<SimDataDTO> simDataList = List.of(simData);

        vehicleSimCardManager.handleSimCardAbnormal(vehicleName, null, simDataList, alarmMapDb, recordTime);

        // 验证消除告警
        verify(vehicleAlarmManager, times(1)).removeAlarm(vehicleName, AlarmTypeEnum.SIM_CARD_ABNORMAL.getValue());
        verify(redissonRepository, times(2)).deleteObject(anyString()); // 清理Redis记录
        log.info("测试非4G状态告警消除 - 通过");
    }

    @Test
    void testHandleSimCardAbnormal_AlarmRemoval_5GGoodSignal() {
        // 测试告警消除条件3：5G信号质量正常
        VehicleAlarmEventDTO existingAlarm = new VehicleAlarmEventDTO();
        existingAlarm.setType(AlarmTypeEnum.SIM_CARD_ABNORMAL.getValue());
        alarmMapDb.put(AlarmTypeEnum.SIM_CARD_ABNORMAL.getValue(), existingAlarm);

        SimDataDTO simData = SimDataDTO.builder()
                .vehicleName(vehicleName)
                .standard("5G NR")
                .rsrp(-90.0f)  // 大于等于-100阈值
                .sinr(5.0f)    // 大于3阈值
                .deviceId(1)
                .recordTime(recordTime)
                .build();

        List<SimDataDTO> simDataList = List.of(simData);

        vehicleSimCardManager.handleSimCardAbnormal(vehicleName, null, simDataList, alarmMapDb, recordTime);

        // 验证消除告警
        verify(vehicleAlarmManager, times(1)).removeAlarm(vehicleName, AlarmTypeEnum.SIM_CARD_ABNORMAL.getValue());
        verify(redissonRepository, times(2)).deleteObject(anyString()); // 清理Redis记录
        log.info("测试5G信号质量正常告警消除 - 通过");
    }

    @Test
    void testHandleSimCardAbnormal_NoAlarmGeneration_4GNotEnoughDuration() {
        // 测试4G状态持续时间不足，不生成告警
        SimDataDTO simData = SimDataDTO.builder()
                .vehicleName(vehicleName)
                .standard("LTE")
                .deviceId(1)
                .recordTime(recordTime)
                .build();

        List<SimDataDTO> simDataList = List.of(simData);

        // 模拟Redis ZSet操作，持续时间不足
        when(redissonRepository.addToScoredSortedSet(anyString(), anyString(), anyDouble())).thenReturn(true);
        when(redissonRepository.expireScoredSortedSet(anyString(), anyLong())).thenReturn(true);
        when(redissonRepository.removeRangeByScore(anyString(), anyDouble(), anyDouble())).thenReturn(1);
        when(redissonRepository.countByScore(anyString(), anyDouble(), anyDouble())).thenReturn(5); // 不满足10秒条件

        vehicleSimCardManager.handleSimCardAbnormal(vehicleName, null, simDataList, alarmMapDb, recordTime);

        // 验证不生成告警
        verify(vehicleAlarmManager, never()).addAlarm(eq(vehicleName), any(VehicleAlarmEventDTO.class));
        log.info("测试4G状态持续时间不足不生成告警 - 通过");
    }

    @Test
    void testHandleSimCardAbnormal_NoAlarmGeneration_ExistingAlarm() {
        // 测试已存在告警时不重复生成
        VehicleAlarmEventDTO existingAlarm = new VehicleAlarmEventDTO();
        existingAlarm.setType(AlarmTypeEnum.SIM_CARD_ABNORMAL.getValue());
        alarmMapDb.put(AlarmTypeEnum.SIM_CARD_ABNORMAL.getValue(), existingAlarm);

        SimStatusDTO simStatus = new SimStatusDTO();
        simStatus.setOnlineStatus(1); // 1-离线
        simStatus.setDeviceId(1);
        simStatus.setRecordTime(recordTime);

        List<SimStatusDTO> simStatusList = List.of(simStatus);

        vehicleSimCardManager.handleSimCardAbnormal(vehicleName, simStatusList, null, alarmMapDb, recordTime);

        // 验证不生成新告警
        verify(vehicleAlarmManager, never()).addAlarm(eq(vehicleName), any(VehicleAlarmEventDTO.class));
        log.info("测试已存在告警时不重复生成 - 通过");
    }

    @Test
    void testHandleSimCardAbnormal_EmptyInput() {
        // 测试空输入
        vehicleSimCardManager.handleSimCardAbnormal(vehicleName, null, null, alarmMapDb, recordTime);

        // 验证不进行任何操作
        verify(vehicleAlarmManager, never()).addAlarm(anyString(), any(VehicleAlarmEventDTO.class));
        verify(vehicleAlarmManager, never()).removeAlarm(anyString(), anyString());
        log.info("测试空输入处理 - 通过");
    }
}
