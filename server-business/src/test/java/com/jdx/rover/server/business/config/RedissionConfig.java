/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.server.business.config;

import lombok.extern.slf4j.Slf4j;
import org.redisson.codec.JsonJacksonCodec;
import org.redisson.spring.starter.RedissonAutoConfigurationCustomizer;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import redis.embedded.RedisServer;

import java.util.ArrayList;
import java.util.List;

/**
 * 配置 redisson 的序列化方式
 *
 * <AUTHOR>
 */
@Configuration
@Slf4j
public class RedissionConfig implements DisposableBean, InitializingBean {
  private RedisServer redisServer;

  @Override
  public void destroy() throws Exception {
    if (this.redisServer != null) {
      this.redisServer.stop();
      this.redisServer = null;
    }
  }

  @Override
  public void afterPropertiesSet() {
    if (this.redisServer != null) {
      return;
    }
    int port = 6379;
    this.redisServer = RedisServer.builder().setting("maxmemory 128m").port(port).build();
    this.redisServer.start();
    log.info("Starting local embedded redis server successfully, port is " + port);
  }

  /**
   * <p>
   * 配置 redisson 的序列化方式
   * </p>
   */
  @Bean
  public List<RedissonAutoConfigurationCustomizer> redissonAutoConfigurationCustomizers() {
    log.info("------------test redis");
    List<RedissonAutoConfigurationCustomizer> RedissonAutoConfigurationCustomizer = new ArrayList<>();
    RedissonAutoConfigurationCustomizer.add(configuration -> configuration.setCodec(new JsonJacksonCodec()));
    return RedissonAutoConfigurationCustomizer;
  }
}
