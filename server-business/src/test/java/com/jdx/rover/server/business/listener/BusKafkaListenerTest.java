package com.jdx.rover.server.business.listener;

import com.google.protobuf.util.JsonFormat;
import jdx.rover.dto.proto.BusDto;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
class BusKafkaListenerTest {

    @Autowired
    private BusKafkaListener busKafkaListener;

    @Test
    void onMessage() throws Exception {
        String message = "{\"requestHeader\":{\"messageTypeRequest\":[{\"messageType\":\"MONITOR\",\"needResponse\":true}],\"requestId\":\"1678432975811460520\",\"vehicleName\":\"JDZ0038\",\"requestTime\":\"1678432975811460520\"},\"monitor\":{\"monitorType\":\"FEATURE_MSG\",\"featureData\":{\"featureDetail\":[{\"seqNumber\":57,\"moduleName\":\"test_mode\",\"featureName\":\"test_feature\",\"timestamp\":\"1678432973308765928\",\"duration\":3,\"msg\":\"\\n\\n\\n\\bmsg_test\"},{\"seqNumber\":58,\"moduleName\":\"test_mode\",\"featureName\":\"test_feature\",\"timestamp\":\"1678432973409646959\",\"duration\":3,\"msg\":\"\\n\\n\\n\\bmsg_test\"},{\"seqNumber\":59,\"moduleName\":\"test_mode\",\"featureName\":\"test_feature\",\"timestamp\":\"1678432973511376951\",\"duration\":3,\"msg\":\"\\n\\n\\n\\bmsg_test\"},{\"seqNumber\":60,\"moduleName\":\"test_mode\",\"featureName\":\"test_feature\",\"timestamp\":\"1678432973612137631\",\"duration\":3,\"msg\":\"\\n\\n\\n\\bmsg_test\"},{\"seqNumber\":61,\"moduleName\":\"test_mode\",\"featureName\":\"test_feature\",\"timestamp\":\"1678432973712684390\",\"duration\":3,\"msg\":\"\\n\\n\\n\\bmsg_test\"},{\"seqNumber\":62,\"moduleName\":\"test_mode\",\"featureName\":\"test_feature\",\"timestamp\":\"1678432973813281251\",\"duration\":3,\"msg\":\"\\n\\n\\n\\bmsg_test\"},{\"seqNumber\":63,\"moduleName\":\"test_mode\",\"featureName\":\"test_feature\",\"timestamp\":\"1678432973914284380\",\"duration\":3,\"msg\":\"\\n\\n\\n\\bmsg_test\"},{\"seqNumber\":64,\"moduleName\":\"test_mode\",\"featureName\":\"test_feature\",\"timestamp\":\"1678432974014927156\",\"duration\":3,\"msg\":\"\\n\\n\\n\\bmsg_test\"},{\"seqNumber\":65,\"moduleName\":\"test_mode\",\"featureName\":\"test_feature\",\"timestamp\":\"1678432974115619405\",\"duration\":3,\"msg\":\"\\n\\n\\n\\bmsg_test\"},{\"seqNumber\":66,\"moduleName\":\"test_mode\",\"featureName\":\"test_feature\",\"timestamp\":\"1678432974216195526\",\"duration\":3,\"msg\":\"\\n\\n\\n\\bmsg_test\"},{\"seqNumber\":67,\"moduleName\":\"test_mode\",\"featureName\":\"test_feature\",\"timestamp\":\"1678432974316881823\",\"duration\":3,\"msg\":\"\\n\\n\\n\\bmsg_test\"},{\"seqNumber\":68,\"moduleName\":\"test_mode\",\"featureName\":\"test_feature\",\"timestamp\":\"1678432974417480792\",\"duration\":3,\"msg\":\"\\n\\n\\n\\bmsg_test\"},{\"seqNumber\":69,\"moduleName\":\"test_mode\",\"featureName\":\"test_feature\",\"timestamp\":\"1678432974518052017\",\"duration\":3,\"msg\":\"\\n\\n\\n\\bmsg_test\"},{\"seqNumber\":70,\"moduleName\":\"test_mode\",\"featureName\":\"test_feature\",\"timestamp\":\"1678432974618733833\",\"duration\":3,\"msg\":\"\\n\\n\\n\\bmsg_test\"},{\"seqNumber\":71,\"moduleName\":\"test_mode\",\"featureName\":\"test_feature\",\"timestamp\":\"1678432974719599234\",\"duration\":3,\"msg\":\"\\n\\n\\n\\bmsg_test\"},{\"seqNumber\":72,\"moduleName\":\"test_mode\",\"featureName\":\"test_feature\",\"timestamp\":\"1678432974820187745\",\"duration\":3,\"msg\":\"\\n\\n\\n\\bmsg_test\"},{\"seqNumber\":73,\"moduleName\":\"test_mode\",\"featureName\":\"test_feature\",\"timestamp\":\"1678432974920803458\",\"duration\":3,\"msg\":\"\\n\\n\\n\\bmsg_test\"},{\"seqNumber\":74,\"moduleName\":\"test_mode\",\"featureName\":\"test_feature\",\"timestamp\":\"1678432975021458914\",\"duration\":3,\"msg\":\"\\n\\n\\n\\bmsg_test\"},{\"seqNumber\":75,\"moduleName\":\"test_mode\",\"featureName\":\"test_feature\",\"timestamp\":\"1678432975122069539\",\"duration\":3,\"msg\":\"\\n\\n\\n\\bmsg_test\"},{\"seqNumber\":76,\"moduleName\":\"test_mode\",\"featureName\":\"test_feature\",\"timestamp\":\"1678432975222681219\",\"duration\":3,\"msg\":\"\\n\\n\\n\\bmsg_test\"},{\"seqNumber\":77,\"moduleName\":\"test_mode\",\"featureName\":\"test_feature\",\"timestamp\":\"1678432975323286500\",\"duration\":3,\"msg\":\"\\n\\n\\n\\bmsg_test\"},{\"seqNumber\":78,\"moduleName\":\"test_mode\",\"featureName\":\"test_feature\",\"timestamp\":\"1678432975424194148\",\"duration\":3,\"msg\":\"\\n\\n\\n\\bmsg_test\"},{\"seqNumber\":79,\"moduleName\":\"test_mode\",\"featureName\":\"test_feature\",\"timestamp\":\"1678432975524779429\",\"duration\":3,\"msg\":\"\\n\\n\\n\\bmsg_test\"},{\"seqNumber\":80,\"moduleName\":\"test_mode\",\"featureName\":\"test_feature\",\"timestamp\":\"1678432975625442373\",\"duration\":3,\"msg\":\"\\n\\n\\n\\bmsg_test\"},{\"seqNumber\":81,\"moduleName\":\"test_mode\",\"featureName\":\"test_feature\",\"timestamp\":\"1678432975726289766\",\"duration\":3,\"msg\":\"\\n\\n\\n\\bmsg_test\"}]}}}";
        BusDto.BusDTO.Builder busDataDTO = BusDto.BusDTO.newBuilder();
        JsonFormat.parser().merge(message, busDataDTO);
        busKafkaListener.onMessage(Lists.newArrayList(busDataDTO.build().toByteArray()));
    }
}