/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.server.business.manager;

import com.jdx.rover.server.api.domain.dto.guardian.VehicleAbnormalDetailDTO;
import com.jdx.rover.server.business.service.GuardianService;
import com.jdx.rover.server.business.service.GuardianServiceTest;
import com.jdx.rover.server.domain.vo.guardian.GuardianInfoAddVo;
import com.jdx.rover.server.repository.redis.guardian.VehicleAbnormalRepository;
import jdx.rover.guardian.data.dto.GuardianDataDto;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Date;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;

@Slf4j
@SpringBootTest
class VehicleAbnormalManagerTest {
  @Autowired
  private VehicleAbnormalManager vehicleAbnormalManager;
  @Autowired
  private GuardianService guardianService;

  @Autowired
  private VehicleAbnormalRepository vehicleAbnormalRepository;

  @Test
  void handle() throws Exception {
    GuardianDataDto.GuardianInfoDTO info = GuardianServiceTest.buildGuardianInfoDTO();
    GuardianInfoAddVo vo = guardianService.convertGuardianInfoProtoToVo(info);
    vehicleAbnormalManager.handle(vo);

    Map<String, VehicleAbnormalDetailDTO> map = vehicleAbnormalRepository.get(vo.getVehicleName());
    assertThat(map).isNotNull();
//    assertThat(map).hasSameSizeAs();

    vo.getVehicleExceptionLogSaveRequestList().get(0).setTimestamp(new Date());
    vo.getVehicleExceptionLogSaveRequestList().get(1).setTimestamp(new Date());
    vehicleAbnormalManager.handle(vo);

  }

  @Test
  void handStopReportException() {
  }
}