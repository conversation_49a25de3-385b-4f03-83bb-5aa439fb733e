package com.jdx.rover.server.business.manager.report;

import lombok.RequiredArgsConstructor;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@RequiredArgsConstructor(onConstructor_ = @Autowired)
@SpringBootTest
class VehicleAbnormalReportManagerTest {
    private final VehicleAbnormalReportManager vehicleAbnormalReportManager;


    @Test
    void synchronousAbnormal() {
        vehicleAbnormalReportManager.guardianAbnormalToReport();
    }
}