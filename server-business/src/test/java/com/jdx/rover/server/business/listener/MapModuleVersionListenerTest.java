package com.jdx.rover.server.business.listener;

import com.google.common.collect.Lists;
import com.jdx.rover.server.business.BusinessApplication;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * 注
 *
 * <AUTHOR>
 * @date 2024/3/16
 */
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@Slf4j
@SpringBootTest(classes = BusinessApplication.class)
class MapModuleVersionListenerTest {

    /**
     * 地图模块版本监听器
     */
    private final MapModuleVersionListener mapModuleVersionListener;

    @Test
    void onMessage() {
        String msg = "{\"nodeName\":\"compute\",\"vehicleName\":\"JD001\",\"moduleName\":\"map\",\"curVersion\":\"149-205\"}";
        mapModuleVersionListener.onMessage(Lists.newArrayList(msg));
    }
}