package com.jdx.rover.server.business.manager;

import cn.hutool.core.date.DateUtil;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleAlarmEventDTO;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleRealtimeInfoDTO;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleRealtimeWheelInfoDTO;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 * @date 2024/9/18 16:46
 * @description
 */
@Slf4j
public class VehicleWheelManagerTest {

    @Test
    void handle() throws Exception {
        // vehicleAlarmManager需要注释掉
        VehicleWheelManager vehicleWheelManager = new VehicleWheelManager();
        Map<String, VehicleAlarmEventDTO> alarmMapDb = new HashMap<>();
        VehicleRealtimeInfoDTO vehicleRealtimeInfoDTO = new VehicleRealtimeInfoDTO();
        vehicleRealtimeInfoDTO.setVehicleName("test");
        vehicleRealtimeInfoDTO.setRecordTime(DateUtil.parseDateTime("2024-09-18 16:40:00"));
        List<VehicleRealtimeWheelInfoDTO> wheels = new ArrayList<>();
        VehicleRealtimeWheelInfoDTO frontLeft = new VehicleRealtimeWheelInfoDTO();
        frontLeft.setWheelPosition("WHEEL_POSITION_FRONT_LEFT");
        frontLeft.setPressureStatus("");
        frontLeft.setPressure(100);
        wheels.add(frontLeft);
        VehicleRealtimeWheelInfoDTO frontRight = new VehicleRealtimeWheelInfoDTO();
        frontRight.setWheelPosition("WHEEL_POSITION_FRONT_RIGHT");
        frontRight.setPressureStatus("");
        frontRight.setPressure(100);
        wheels.add(frontRight);
        VehicleRealtimeWheelInfoDTO backLeft = new VehicleRealtimeWheelInfoDTO();
        backLeft.setWheelPosition("WHEEL_POSITION_BACK_LEFT");
        backLeft.setPressureStatus("");
        backLeft.setPressure(100);
        wheels.add(backLeft);
        VehicleRealtimeWheelInfoDTO backRight = new VehicleRealtimeWheelInfoDTO();
        backRight.setWheelPosition("WHEEL_POSITION_BACK_RIGHT");
        backRight.setPressureStatus("");
        backRight.setPressure(100);
        wheels.add(backRight);
        vehicleRealtimeInfoDTO.setWheelInfo(wheels);
        vehicleWheelManager.handleWheelPressureSharpDecrease(vehicleRealtimeInfoDTO, alarmMapDb);
        log.info("---");

        vehicleRealtimeInfoDTO.setRecordTime(DateUtil.parseDateTime("2024-09-18 16:40:59"));
        vehicleWheelManager.handleWheelPressureSharpDecrease(vehicleRealtimeInfoDTO, alarmMapDb);
        log.info("---filter");

        frontLeft.setPressure(60);
        vehicleRealtimeInfoDTO.setRecordTime(DateUtil.parseDateTime("2024-09-18 16:41:01"));
        vehicleWheelManager.handleWheelPressureSharpDecrease(vehicleRealtimeInfoDTO, alarmMapDb);
        log.info("---add");

        frontLeft.setPressure(50);
        vehicleRealtimeInfoDTO.setRecordTime(DateUtil.parseDateTime("2024-09-18 16:42:01"));
        vehicleWheelManager.handleWheelPressureSharpDecrease(vehicleRealtimeInfoDTO, alarmMapDb);
        log.info("---exist");

        frontLeft.setPressure(80);
        frontRight.setPressure(50);
        vehicleRealtimeInfoDTO.setRecordTime(DateUtil.parseDateTime("2024-09-18 16:43:01"));
        vehicleWheelManager.handleWheelPressureSharpDecrease(vehicleRealtimeInfoDTO, alarmMapDb);
        log.info("---exist");

        frontRight.setPressure(80);
        vehicleRealtimeInfoDTO.setRecordTime(DateUtil.parseDateTime("2024-09-18 16:44:01"));
        vehicleWheelManager.handleWheelPressureSharpDecrease(vehicleRealtimeInfoDTO, alarmMapDb);
        log.info("---remove");

        vehicleRealtimeInfoDTO.setRecordTime(DateUtil.parseDateTime("2024-09-18 16:45:00"));
        vehicleWheelManager.handleWheelPressureSharpDecrease(vehicleRealtimeInfoDTO, alarmMapDb);
        log.info("---filter");

        vehicleRealtimeInfoDTO.setRecordTime(DateUtil.parseDateTime("2024-09-18 16:46:01"));
        vehicleWheelManager.handleWheelPressureSharpDecrease(vehicleRealtimeInfoDTO, alarmMapDb);
        log.info("---");

        frontLeft.setPressure(60);
        vehicleRealtimeInfoDTO.setRecordTime(DateUtil.parseDateTime("2024-09-18 16:47:01"));
        vehicleWheelManager.handleWheelPressureSharpDecrease(vehicleRealtimeInfoDTO, alarmMapDb);
        log.info("---");

        frontLeft.setPressure(60);
        vehicleRealtimeInfoDTO.setRecordTime(DateUtil.parseDateTime("2024-09-18 16:48:01"));
        vehicleWheelManager.handleWheelPressureSharpDecrease(vehicleRealtimeInfoDTO, alarmMapDb);
        log.info("---");

        frontLeft.setPressure(60);
        vehicleRealtimeInfoDTO.setRecordTime(DateUtil.parseDateTime("2024-09-18 16:49:01"));
        vehicleWheelManager.handleWheelPressureSharpDecrease(vehicleRealtimeInfoDTO, alarmMapDb);
        log.info("---");

        frontLeft.setPressure(29);
        vehicleRealtimeInfoDTO.setRecordTime(DateUtil.parseDateTime("2024-09-18 16:50:01"));
        vehicleWheelManager.handleWheelPressureSharpDecrease(vehicleRealtimeInfoDTO, alarmMapDb);
        log.info("---");
    }
}
