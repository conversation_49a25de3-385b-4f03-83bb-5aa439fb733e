/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.server.business.manager;

import com.jdx.rover.server.domain.vo.guardian.GuardianInfoAddVo;
import com.jdx.rover.server.domain.vo.guardian.VehicleStatusAddVo;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * <AUTHOR>
 */
@SpringBootTest
class VehiclePowerManagerTest {
  @Autowired
  private VehiclePowerManager vehiclePowerManager;

  @Test
  void handle() {
    GuardianInfoAddVo guardianInfoAddVo = new GuardianInfoAddVo();
    guardianInfoAddVo.setVehicleName("testabc");
    VehicleStatusAddVo vehicleStatusRequest = new VehicleStatusAddVo();
    vehicleStatusRequest.setPowerUsage(50.0);
    guardianInfoAddVo.setVehicleStatusRequest(vehicleStatusRequest);
    vehiclePowerManager.handle(guardianInfoAddVo);
    vehiclePowerManager.handle(guardianInfoAddVo);
    vehiclePowerManager.handle(guardianInfoAddVo);
    vehicleStatusRequest.setPowerUsage(40.0);
    vehiclePowerManager.handle(guardianInfoAddVo);
    vehiclePowerManager.handle(guardianInfoAddVo);
  }
}