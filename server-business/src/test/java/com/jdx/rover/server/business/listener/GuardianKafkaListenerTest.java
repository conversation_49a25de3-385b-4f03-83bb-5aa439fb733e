/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.server.business.listener;

import com.jdx.rover.server.business.service.GuardianServiceTest;
import jdx.rover.guardian.data.dto.GuardianDataDto;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

/**
 * <AUTHOR>
 */
@SpringBootTest
class GuardianKafkaListenerTest {

  @Autowired
  private GuardianKafkaListener guardianKafkaListener;

  @Test
  void onMessage() throws Exception {
    GuardianDataDto.GuardianInfoDTO infoObj = GuardianServiceTest.buildGuardianInfoDTO();
    List<byte[]> messageList = Lists.newArrayList(infoObj.toByteArray());
    guardianKafkaListener.onMessage(messageList);
  }
}