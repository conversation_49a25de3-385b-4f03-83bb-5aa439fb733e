/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/
package com.jdx.rover.server.business.manager.report;

import com.google.common.collect.Lists;
import com.jdx.rover.common.constant.NumberConstant;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleAbnormalDetailDTO;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleAlarmEventDTO;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleRealtimeInfoDTO;
import com.jdx.rover.server.api.domain.dto.report.abnormal.VehicleAbnormalDTO;
import com.jdx.rover.server.api.domain.enums.guardian.AlarmTypeEnum;
import com.jdx.rover.server.api.domain.enums.guardian.VehicleStateEnum;
import com.jdx.rover.server.business.config.ProducerTopicProperties;
import com.jdx.rover.server.domain.dto.vehicle.ReportBootDTO;
import com.jdx.rover.server.domain.dto.vehicle.VehicleStatusDTO;
import com.jdx.rover.server.repository.jmq.JmqProducerManager;
import com.jdx.rover.server.repository.redis.guardian.ReportAbnormalRepository;
import com.jdx.rover.server.repository.redis.guardian.ReportAlarmRepository;
import com.jdx.rover.server.repository.redis.guardian.VehicleAbnormalRepository;
import com.jdx.rover.server.repository.redis.guardian.VehicleRealtimeInfoRepository;
import com.jdx.rover.server.repository.redis.guardian.VehicleStatusRepository;
import com.jdx.rover.server.repository.redis.report.ReportBootRepository;
import jdx.rover.report.dto.proto.ReportDto;
import jdx.rover.report.dto.proto.ReportHeader;
import jdx.rover.report.statistics.proto.ReportStatistics;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 收到异常信息,对外发送kafka消息
 *
 * <AUTHOR>
 * @version 1.0
 * @date  2022-01-01
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class VehicleAbnormalReportManager {
    private final KafkaTemplate<String, String> kafkaTemplate;
    private final ReportAbnormalRepository reportAbnormalRepository;
    private final ReportAlarmRepository reportAlarmRepository;
    private final VehicleAlarmManager vehicleAlarmManager;
    private final VehicleAbnormalRepository vehicleAbnormalRepository;
    private final VehicleStatusRepository vehicleStatusRepository;
    private final ReportBootRepository reportBootRepository;
    private final VehicleRealtimeInfoRepository vehicleRealtimeInfoRepository;
    /**
     * jmq消息发送producer
     */
    @Autowired
    private final JmqProducerManager jmqProducerManager;

    /**
     * 生产主题配置类
     */
    @Autowired
    private final ProducerTopicProperties producerTopicProperties;

    private static final int SPECIFY_MS = 10000;
    /**
     * guardian异常关闭是30s+20s=50s消失,只有结束时间大于50s
     */
    private static final int GUARDIAN_ABNORMAL_CLOSE_MS = 55000;

    /**
     * 处理启动信息
     */
    public void handle(ReportDto.ReportDTO reportDTO, ReportHeader.MessageTypeRequest messageTypeRequest) {
        if (!ReportHeader.MessageType.ABNORMAL.equals(messageTypeRequest.getMessageType())) {
            return;
        }
        if (CollectionUtils.isEmpty(reportDTO.getReportStatisticsData().getAbnormalDataList())) {
            return;
        }
        // 处理异常数据
        List<VehicleAbnormalDTO> abnormalList = new ArrayList<>(reportDTO.getReportStatisticsData().getAbnormalDataCount());
        for (ReportStatistics.AbnormalData abnormalData : reportDTO.getReportStatisticsData().getAbnormalDataList()) {
            VehicleAbnormalDTO vehicleAbnormal = new VehicleAbnormalDTO();
            vehicleAbnormal.setVehicleName(reportDTO.getRequestHeader().getVehicleName());
            vehicleAbnormal.setRuntimeUuid(reportDTO.getReportStatisticsData().getRuntimeUuid());
            vehicleAbnormal.setReportTime(reportDTO.getRequestHeader().getRequestTime());
            vehicleAbnormal.setStartTime(abnormalData.getStartTime());
            vehicleAbnormal.setEndTime(abnormalData.getEndTime());
            vehicleAbnormal.setModuleName(abnormalData.getModuleName());
            vehicleAbnormal.setErrorCode(String.valueOf(abnormalData.getErrorCode()));
            vehicleAbnormal.setErrorLevel(abnormalData.getErrorLevel());
            vehicleAbnormal.setErrorMsg(abnormalData.getErrorMsg());
            abnormalList.add(vehicleAbnormal);
        }

        saveRedisAndSendJmq(abnormalList, reportDTO.getRequestHeader().getVehicleName());
    }

    /**
     * 保存redis并发送jmq
     */
    public void saveRedisAndSendJmq(List<VehicleAbnormalDTO> abnormalList, String vehicleName) {
        saveRedis(abnormalList);

        String jsonStr = JsonUtils.writeValueAsString(abnormalList);
        jmqProducerManager.sendOrdered(producerTopicProperties.getServerReportAbnormal(), jsonStr, vehicleName);
        log.info("发送异常信息={}", jsonStr);
    }

    /**
     * 保存到redis
     *
     * @param abnormalList
     */
    public void saveRedis(List<VehicleAbnormalDTO> abnormalList) {
        if (CollectionUtils.isEmpty(abnormalList)) {
            return;
        }

        String vehicleName = abnormalList.get(0).getVehicleName();
        List<VehicleAbnormalDTO> saveList = new ArrayList<>();
        List<VehicleAbnormalDTO> removeList = new ArrayList<>();
        for (VehicleAbnormalDTO vehicleAbnormalDTO : abnormalList) {
            Map<String, VehicleAbnormalDTO> abnormalMapDb = reportAbnormalRepository.get(vehicleName);
            if (vehicleAbnormalDTO.getEndTime() > 0) {
                removeList.add(vehicleAbnormalDTO);
                reportAbnormalRepository.remove(Lists.newArrayList(vehicleAbnormalDTO));
                continue;
            }
            String hashKey = ReportAbnormalRepository.getHashKeyByDTO(vehicleAbnormalDTO);
            VehicleAbnormalDTO vehicleAbnormalDb = abnormalMapDb.get(hashKey);
            if (!Objects.isNull(vehicleAbnormalDb) && Objects.equals(vehicleAbnormalDb.getErrorLevel(), vehicleAbnormalDTO.getErrorLevel())) {
                log.info("当前异常已存在{},redis异常{}", vehicleAbnormalDTO, vehicleAbnormalDb);
            } else {
                saveList.add(vehicleAbnormalDTO);
                reportAbnormalRepository.save(Lists.newArrayList(vehicleAbnormalDTO));
            }
        }
//        reportAbnormalRepository.save(saveList);
//        reportAbnormalRepository.remove(removeList);
        if (!CollectionUtils.isEmpty(saveList) || !CollectionUtils.isEmpty(removeList)) {
            List<VehicleAbnormalDTO> abnormalAlarmList = new ArrayList<>(removeList);
            abnormalAlarmList.addAll(reportAbnormalRepository.get(vehicleName).values());
            vehicleAlarmManager.saveAlarmRedis(abnormalAlarmList);
            log.info("saveList={}, removeList={}, abnormalAlarmList={}", saveList, removeList, abnormalAlarmList);
        }
    }

    /**
     * guardian告警同步report链路
     */
    public void guardianAbnormalToReport() {
        Map<String, Map<String, VehicleAbnormalDetailDTO>> guardianAbnormalMap = vehicleAbnormalRepository.getAll();
        Map<String, Map<String, VehicleAbnormalDTO>> reportAbnormalMap = reportAbnormalRepository.getAll();
        synchronousRemoveAbnormal(reportAbnormalMap, guardianAbnormalMap);
        synchronousUpdateAbnormal(guardianAbnormalMap, reportAbnormalMap);

        synchronousAlarm(guardianAbnormalMap);
    }

    /**
     * 同步删除异常
     *
     * @param reportAbnormalMap
     * @param guardianAbnormalMap
     */
    private void synchronousRemoveAbnormal(Map<String, Map<String, VehicleAbnormalDTO>> reportAbnormalMap, Map<String, Map<String, VehicleAbnormalDetailDTO>> guardianAbnormalMap) {
        for (Map.Entry<String, Map<String, VehicleAbnormalDTO>> reportEntry : reportAbnormalMap.entrySet()) {
            String vehicleName = reportEntry.getKey();
            Map<String, VehicleAbnormalDetailDTO> guardianVheicleMap = guardianAbnormalMap.get(vehicleName);
            List<VehicleAbnormalDTO> abnormalAlarmList = new ArrayList<>();
            for (VehicleAbnormalDTO abnormal : reportEntry.getValue().values()) {
                String key = abnormal.getErrorCode() + ":" + abnormal.getModuleName();
                if (!CollectionUtils.isEmpty(guardianVheicleMap) && !Objects.isNull(guardianVheicleMap.get(key))) {
                    continue;
                }
                if (Objects.isNull(abnormal.getStartTime()) || isBeforeNowSpecifyNsTime(abnormal.getStartTime())) {
                    // guardian不在线,不处理
                    if (isGuardianOffline(vehicleName)) {
                        continue;
                    }
                    reportAbnormalRepository.remove(vehicleName, abnormal.getErrorCode(), abnormal.getModuleName());
                    abnormal.setEndTime(System.currentTimeMillis() * NumberConstant.MILLION);
                    abnormalAlarmList.add(abnormal);
                }
            }
            if (!CollectionUtils.isEmpty(abnormalAlarmList)) {
                log.info("guardianAbnormalToReport同步guardian删除异常{}", abnormalAlarmList);
                abnormalAlarmList.addAll(reportAbnormalRepository.get(vehicleName).values());
                vehicleAlarmManager.saveAlarmRedis(abnormalAlarmList);
                jmqProducerManager.sendOrdered(producerTopicProperties.getServerReportAbnormal(), JsonUtils.writeValueAsString(abnormalAlarmList), vehicleName);
            }
        }
    }

    /**
     * 同步更新异常
     *
     * @param guardianAbnormalMap
     * @param reportAbnormalMap
     */
    private void synchronousUpdateAbnormal(Map<String, Map<String, VehicleAbnormalDetailDTO>> guardianAbnormalMap, Map<String, Map<String, VehicleAbnormalDTO>> reportAbnormalMap) {
        for (Map.Entry<String, Map<String, VehicleAbnormalDetailDTO>> guardianEntry : guardianAbnormalMap.entrySet()) {
            String vehicleName = guardianEntry.getKey();
            Map<String, VehicleAbnormalDTO> reportVheicleMap = reportAbnormalMap.get(vehicleName);
            if (CollectionUtils.isEmpty(reportVheicleMap)) {
                boolean isOld = true;
                for (VehicleAbnormalDetailDTO abnormal : guardianEntry.getValue().values()) {
                    // guardian异常是30s+20s=50s消失,只有结束时间大于50s
                    if (Objects.isNull(abnormal.getEndTime()) ||
                            !isBeforeNowSpecifyMsTime(abnormal.getEndTime().getTime(), GUARDIAN_ABNORMAL_CLOSE_MS)) {
                        isOld = false;
                        break;
                    }
                }
                // 只有10s前才打印,否则新数据,等待下次判断
                if (isOld) {
                    log.error("guardianAbnormalToReport {}车report异常全部不存在!{}", vehicleName, guardianEntry);
                }
                continue;
            }
            boolean isSynchronous = false;
            for (VehicleAbnormalDetailDTO abnormal : guardianEntry.getValue().values()) {
                String key = abnormal.getErrorCode() + ":" + abnormal.getModuleName();
                VehicleAbnormalDTO reportAbnormal = reportVheicleMap.get(key);
                if (Objects.isNull(reportAbnormal)) {
                    if (Objects.isNull(abnormal.getEndTime()) ||
                            !isBeforeNowSpecifyMsTime(abnormal.getEndTime().getTime(), GUARDIAN_ABNORMAL_CLOSE_MS)) {
                        continue;
                    }
                    log.error("guardianAbnormalToReport {}车report异常部分不存在!{}", vehicleName, abnormal);
                } else if (!Objects.equals(abnormal.getErrorLevel(), reportAbnormal.getErrorLevel()) && isBeforeNowSpecifyMsTime(abnormal.getStartTime().getTime())) {
                    log.error("guardianAbnormalToReport {}车report异常级别不一致!guardian={},report={}", vehicleName, abnormal, reportAbnormal);
                    reportAbnormal.setErrorLevel(abnormal.getErrorLevel());
                    reportAbnormalRepository.save(Lists.newArrayList(reportAbnormal));
                    isSynchronous = true;
                }
            }
            if (isSynchronous) {
                List<VehicleAbnormalDTO> abnormalAlarmList = Lists.newArrayList(reportAbnormalRepository.get(vehicleName).values());
                vehicleAlarmManager.saveAlarmRedis(abnormalAlarmList);
            }
        }
    }


    /**
     * 同步告警
     *
     * @param guardianAbnormalMap
     */
    private void synchronousAlarm(Map<String, Map<String, VehicleAbnormalDetailDTO>> guardianAbnormalMap) {
        Map<String, Map<String, VehicleAlarmEventDTO>> reportAlarmMap = reportAlarmRepository.getAll();
        for (Map.Entry<String, Map<String, VehicleAlarmEventDTO>> alarmEntry : reportAlarmMap.entrySet()) {
            String vehicleName = alarmEntry.getKey();
            Map<String, VehicleAbnormalDetailDTO> guardianVheicleMap = guardianAbnormalMap.get(vehicleName);
            List<VehicleAlarmEventDTO> alarmList = new ArrayList<>();
            for (VehicleAlarmEventDTO alarm : alarmEntry.getValue().values()) {
                if (StringUtils.equalsAny(alarm.getType(), AlarmTypeEnum.BOOT_FAIL.getValue(),
                        AlarmTypeEnum.BOOT_ABNORMAL.getValue(), AlarmTypeEnum.BOOT_ABNORMAL_FAIL.getValue()
                        , AlarmTypeEnum.VEHICLE_STOP_BUTTON.getValue()
                        , AlarmTypeEnum.VEHICLE_STOP_TIMEOUT.getValue()
                        , AlarmTypeEnum.VEHICLE_STOP_INTERSECTION_STUCK.getValue()
                        , AlarmTypeEnum.GATE_STUCK.getValue(), AlarmTypeEnum.PRESSURE_SHARP_DECREASE.getValue()
                        , AlarmTypeEnum.LOCALIZATION_ERROR.getValue(), AlarmTypeEnum.PARK_HARD.getValue()
                        , AlarmTypeEnum.PASS_NO_SIGNAL_INTERSECTION.getValue())) {
                    continue;
                }
                if (Objects.equals(alarm.getType(), AlarmTypeEnum.BOOT_TIMEOUT.getValue())) {
                    VehicleStatusDTO vehicleStatusDTO = vehicleStatusRepository.get(vehicleName);
                    if (!Objects.isNull(vehicleStatusDTO) && !Objects.equals(vehicleStatusDTO.getVehicleState(), VehicleStateEnum.INITIALIZATION.getVehicleState())) {
                        ReportBootDTO reportBootDb = reportBootRepository.get(vehicleName);
                        if (Objects.isNull(reportBootDb) || !isBeforeNowSpecifyMsTime(alarm.getReportTime().getTime())) {
                            continue;
                        }
                        reportAlarmRepository.remove(vehicleName, alarm.getType());
                        alarmList.add(alarm);
                        log.info("guardianAbnormalToReport 同步guardian开机成功,删除告警{},reportBoot={}", alarmList, reportBootDb);
                    }
                    continue;
                }
                boolean isSame = false;
                if (!CollectionUtils.isEmpty(guardianVheicleMap)) {
                    for (VehicleAbnormalDetailDTO value : guardianVheicleMap.values()) {
                        if (Objects.equals(value.getErrorCode(), alarm.getErrorCode()) && Objects.equals(value.getErrorLevel(), alarm.getErrorLevel())) {
//                        if (Objects.equals(value.getErrorCode(), alarm.getErrorCode())) {
                            isSame = true;
                            break;
                        }
                    }
                }
                if (isSame) {
                    continue;
                }
                if (Objects.isNull(alarm.getReportTime()) || isBeforeNowSpecifyMsTime(alarm.getReportTime().getTime())) {
                    // guardian不在线,不处理
                    if (isGuardianOffline(vehicleName)) {
                        continue;
                    }
                    reportAlarmRepository.remove(vehicleName, alarm.getType());
                    alarmList.add(alarm);
                }
            }
            if (!CollectionUtils.isEmpty(alarmList)) {
                log.info("guardianAbnormalToReport同步guardian删除告警{} ,{}", vehicleName, alarmList);
                vehicleAlarmManager.sendKafka(vehicleName);
            }
        }
    }

    /**
     * 比当前时间早10s
     *
     * @param millisecond 毫秒
     */

    private boolean isBeforeNowSpecifyMsTime(long millisecond) {
        return isBeforeNowSpecifyMsTime(millisecond, SPECIFY_MS);
    }

    /**
     * 比当前时间早指定毫秒
     *
     * @param millisecond 毫秒
     */

    private boolean isBeforeNowSpecifyMsTime(long millisecond, int specifyMs) {
        return millisecond < (System.currentTimeMillis() - specifyMs);
    }

    /**
     * 比当前时间早10s
     *
     * @param nanosecond 纳秒
     */
    private boolean isBeforeNowSpecifyNsTime(long nanosecond) {
        return isBeforeNowSpecifyMsTime(nanosecond / NumberConstant.MILLION);
    }

    /**
     * 是否guardian离线
     */
    private boolean isGuardianOffline(String vehicleName) {
        VehicleRealtimeInfoDTO realtime = vehicleRealtimeInfoRepository.get(vehicleName);
        return Objects.isNull(realtime) || isBeforeNowSpecifyMsTime(realtime.getRecordTime().getTime(), 18000);
    }
}
