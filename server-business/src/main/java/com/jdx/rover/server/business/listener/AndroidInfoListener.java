package com.jdx.rover.server.business.listener;

import com.jdx.rover.server.business.service.AndroidInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 安卓上报信息数据Kafka监听
 * <AUTHOR>
 */
@Service
@Slf4j
public class AndroidInfoListener {

  @Autowired
  private AndroidInfoService androidInfoService;

//  @KafkaListener(topics = {KafkaTopicConstant.SERVER_MQTT_ANDROID_INFO})
  public void onMessage(List<String> messageList) {
    androidInfoService.receiveList(messageList);
  }
}