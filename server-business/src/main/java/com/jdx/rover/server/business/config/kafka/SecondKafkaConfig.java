package com.jdx.rover.server.business.config.kafka;

import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.serialization.ByteArrayDeserializer;
import org.apache.kafka.common.serialization.ByteArraySerializer;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.kafka.KafkaProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.config.KafkaListenerContainerFactory;
import org.springframework.kafka.core.ConsumerFactory;
import org.springframework.kafka.core.DefaultKafkaConsumerFactory;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.core.ProducerFactory;
import org.springframework.kafka.listener.ConcurrentMessageListenerContainer;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * 第二个kafka配置
 *
 * <AUTHOR>
 */
@Configuration
public class SecondKafkaConfig {
    @Autowired
    private KafkaProperties kafkaProperties;

    @Value("${project.kafka.second.bootstrap-servers}")
    private String bootstrapServers;

    @Bean
    public KafkaTemplate<String, byte[]> secondKafkaTemplate() {
        Map<String, Object> producerConfigs = producerConfigs(ByteArraySerializer.class);
        ProducerFactory<String, byte[]> producerFactory = new DefaultKafkaProducerFactory<>(producerConfigs);
        return new KafkaTemplate<>(producerFactory);
    }

    @Bean
    public KafkaListenerContainerFactory<ConcurrentMessageListenerContainer<String, byte[]>> secondKafkaContainerFactory() {
        ConcurrentKafkaListenerContainerFactory<String, byte[]> factory = new ConcurrentKafkaListenerContainerFactory<>();
        ConsumerFactory<String, byte[]> consumerFactory = new DefaultKafkaConsumerFactory<>(consumerConfigs(ByteArrayDeserializer.class));
        factory.setConsumerFactory(consumerFactory);
        // 设置为批量监听
        factory.setBatchListener(true);
        Integer concurrency = kafkaProperties.getListener().getConcurrency();
        if (concurrency != null && concurrency.intValue() > 1) {
            factory.setConcurrency(concurrency);
        }
        return factory;
    }

    private Map<String, Object> producerConfigs(Class serializerClass) {
        Map<String, Object> props = new HashMap<>();
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, this.bootstrapServers);
        Integer producerRetries = Optional.ofNullable(this.kafkaProperties.getProducer().getRetries()).orElse(5);
        props.put(ProducerConfig.RETRIES_CONFIG, producerRetries);
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, serializerClass);
        return props;
    }

    private Map<String, Object> consumerConfigs(Class deserializerClass) {
        Map<String, Object> props = new HashMap<>();
        props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, this.bootstrapServers);
        props.put(ConsumerConfig.GROUP_ID_CONFIG, kafkaProperties.getConsumer().getGroupId());
        props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, deserializerClass);
        return props;
    }
}
