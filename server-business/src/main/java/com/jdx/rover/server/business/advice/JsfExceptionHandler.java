package com.jdx.rover.server.business.advice;

import com.jd.jsf.gd.msg.RequestMessage;
import com.jd.jsf.gd.msg.ResponseMessage;
import com.jdx.rover.common.utils.exception.AppException;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.jsf.exception.ExceptionHandler;
import org.springframework.stereotype.Component;

/**
 * <p>
 * 自定义异常处理
 * </p>
 *
 * <AUTHOR>
 * @date 2024/09/25
 */
@Component
public class JsfExceptionHandler implements ExceptionHandler {

    @Override
    public boolean handleException(RequestMessage request, ResponseMessage response, Throwable exception) {
        if (exception instanceof AppException) {
            AppException ex = (AppException) exception;
            response.setResponse(HttpResult.error(ex.getCode(), ex.getMessage()));
            return true;
        }
        response.setResponse(HttpResult.error());
        return true;
    }
}