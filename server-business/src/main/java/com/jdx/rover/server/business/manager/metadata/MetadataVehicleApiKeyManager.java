package com.jdx.rover.server.business.manager.metadata;

import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.symmetric.AES;
import com.jdx.rover.common.domain.page.PageDTO;
import com.jdx.rover.common.domain.page.PageVO;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.metadata.domain.dto.vehicle.VehicleApiKeyPageListDTO;
import com.jdx.rover.metadata.jsf.service.vehicle.MetadataVehicleBasicService;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 车辆API密钥管理器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/8/26
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class MetadataVehicleApiKeyManager {

    /**
     * 车辆API密钥缓存
     */
    private static final Map<String, AES> LOCAL_CACHE_MAP = new ConcurrentHashMap<>();

    /**
     * 元数据车辆基础服务
     */
    private final MetadataVehicleBasicService metadataVehicleBasicService;

    /**
     * 获取车辆API密钥
     *
     * @param vehicleName 车辆名称
     * @return 车辆API密钥，如果不存在则返回null
     */
    public AES getVehicleApiKey(String vehicleName) {
        return LOCAL_CACHE_MAP.get(vehicleName);
    }

    /**
     * 初始化API密钥缓存map
     */
    @PostConstruct
    public void initVehicleApiKeyMap() {
        try {
            PageVO pageVO = new PageVO();
            pageVO.setPageNum(1);
            pageVO.setPageSize(2000);
            Map<String, VehicleApiKeyPageListDTO> resultMap = new HashMap<>();
            do {
                HttpResult<PageDTO<VehicleApiKeyPageListDTO>> httpResult = metadataVehicleBasicService.getVehicleApiKeyPageList(pageVO);
                if (!HttpResult.isSuccess(httpResult)) {
                    log.error("获取车辆API密钥列表失败: {}", httpResult);
                    break;
                }

                PageDTO<VehicleApiKeyPageListDTO> pageDTO = httpResult.getData();
                if (pageDTO == null) {
                    log.warn("获取车辆API密钥列表返回数据为空: {}", httpResult);
                    break;
                }

                List<VehicleApiKeyPageListDTO> dataList = pageDTO.getList();
                if (CollectionUtils.isEmpty(dataList)) {
                    log.warn("获取车辆API密钥列表返回数据列表为空: {}", httpResult);
                    break;
                }

                log.info("请求结果大小={},参数={}", dataList.size(), pageVO);
                for (VehicleApiKeyPageListDTO tmp : dataList) {
                    resultMap.put(tmp.getName(), tmp);
                }

                if (pageDTO.getPageNum() >= pageDTO.getPages() || pageDTO.getPages() == 0) {
                    break;
                }
                pageVO.setPageNum(pageVO.getPageNum() + 1);
            } while (true);

            if (MapUtils.isNotEmpty(resultMap)) {
                Map<String, AES> aesMap = new HashMap<>(resultMap.size());
                for (VehicleApiKeyPageListDTO value : resultMap.values()) {
                    if (Objects.isNull(value.getApiKey())) {
                        continue;
                    }
                    // 创建并缓存加密器
                    AES aes = SecureUtil.aes(value.getApiKey().getBytes(StandardCharsets.UTF_8));
                    aesMap.put(value.getName(), aes);
                }
                if (!aesMap.isEmpty()) {
                    // 统一批量操作,减少并发问题,并提高效率
                    LOCAL_CACHE_MAP.putAll(aesMap);
                }
            }
            log.info("初始化车辆API-map完成!");

        } catch (Exception e) {
            log.error("初始化车辆API密钥缓存失败", e);
        }
    }
}
