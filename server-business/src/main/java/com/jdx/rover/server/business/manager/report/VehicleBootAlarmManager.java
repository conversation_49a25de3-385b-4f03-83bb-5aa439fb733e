/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.server.business.manager.report;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.google.common.collect.Iterables;
import com.google.common.collect.Lists;
import com.jdx.rover.common.constant.NumberConstant;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleAlarmEventDTO;
import com.jdx.rover.server.api.domain.dto.report.boot.ModuleBootDTO;
import com.jdx.rover.server.api.domain.dto.report.boot.ModuleBootDetailDTO;
import com.jdx.rover.server.api.domain.dto.report.boot.VehicleBootDTO;
import com.jdx.rover.server.api.domain.enums.guardian.AlarmTypeEnum;
import com.jdx.rover.server.api.domain.enums.report.BootStatusEnum;
import com.jdx.rover.server.domain.enums.GuardianErrorEnum;
import com.jdx.rover.server.domain.enums.GuardianErrorLevelEnum;
import com.jdx.rover.server.domain.enums.RedisCacheEnum;
import com.jdx.rover.server.repository.redis.RedissonUtils;
import com.jdx.rover.server.repository.redis.guardian.ReportAlarmRepository;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.Map;
import java.util.Objects;

/**
 * <p>
 * 车辆启动异常告警管理
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@AllArgsConstructor
@Slf4j
public class VehicleBootAlarmManager {

  private static final long START_UP_TIMEOUT = 5 * 60 * 1000;

  private final ReportAlarmRepository reportAlarmRepository;
  private final VehicleAlarmManager vehicleAlarmManager;

  public void sendAlarmAdapter(VehicleBootDTO vehicleBoot) {
    String vehicleName = vehicleBoot.getVehicleName();
    Map<String, VehicleAlarmEventDTO> alarmMapDb = reportAlarmRepository.get(vehicleName);
    if (Objects.equals(vehicleBoot.getAllBootStatus(), BootStatusEnum.START_SUCCESS.name())) {
      processVehicleBootSucess(vehicleBoot, alarmMapDb);
    } else if (vehicleBoot.getBootId() > 1 && Objects.isNull(alarmMapDb.get(AlarmTypeEnum.BOOT_ABNORMAL.getValue()))
            && Objects.isNull(alarmMapDb.get(AlarmTypeEnum.BOOT_ABNORMAL_FAIL.getValue()))
            && isFirstRoverBootSucess(vehicleName, vehicleBoot.getRuntimeUuid(), vehicleBoot.getBootId()) &&
            Objects.isNull(RedissonUtils.getObject(RedisCacheEnum.VEHICLE_RESTART_ROVER.getKey(vehicleName)))) {
      processVehicleBootAbnormal(vehicleBoot, alarmMapDb);
    } else if (Objects.equals(vehicleBoot.getAllBootStatus(), BootStatusEnum.START_FAILED.name())) {
      processVehicleBootFailed(vehicleBoot, alarmMapDb);
    } else if (Objects.equals(vehicleBoot.getAllBootStatus(), BootStatusEnum.STARTING.name())) {
      processVehicleBootStart(vehicleBoot, alarmMapDb);
    }
  }

  private void processVehicleBootAbnormal(VehicleBootDTO vehicleBoot, Map<String, VehicleAlarmEventDTO> alarmMapDb) {
    String vehicleName = vehicleBoot.getVehicleName();
    log.info("处理车辆{}异常重启告警{}-{}", vehicleName, JsonUtils.writeValueAsString(alarmMapDb), vehicleBoot.getBootId());
    removeBootAlarm(vehicleName, alarmMapDb, GuardianErrorEnum.BOOT_TIMEOUT);
    removeBootAlarm(vehicleName, alarmMapDb, GuardianErrorEnum.BOOT_FAIL);
    addVehilceBootAlarm(vehicleBoot, AlarmTypeEnum.BOOT_ABNORMAL.getValue());
  }

  private void processVehicleBootSucess(VehicleBootDTO vehicleBoot, Map<String, VehicleAlarmEventDTO> alarmMapDb) {
    String vehicleName = vehicleBoot.getVehicleName();
    log.info("处理车辆{}启动成功{}-{}", vehicleName, JsonUtils.writeValueAsString(alarmMapDb), vehicleBoot.getBootId());
    removeBootAlarm(vehicleName, alarmMapDb, GuardianErrorEnum.BOOT_TIMEOUT);
    removeBootAlarm(vehicleName, alarmMapDb, GuardianErrorEnum.BOOT_FAIL);
    removeBootAlarm(vehicleName, alarmMapDb, GuardianErrorEnum.BOOT_ABNORMAL);
    removeBootAlarm(vehicleName, alarmMapDb, GuardianErrorEnum.BOOT_ABNORMAL_FAIL);
    RedissonUtils.deleteObject(RedisCacheEnum.VEHICLE_RESTART_ROVER.getKey(vehicleName));
    saveLastRoverBootSuccessRecord(vehicleBoot, vehicleName);
  }

  private void processVehicleBootStart(VehicleBootDTO vehicleBoot, Map<String, VehicleAlarmEventDTO> alarmMapDb) {
    String vehicleName = vehicleBoot.getVehicleName();
    log.info("处理车辆{}启动中告警{}-{}", vehicleName, JsonUtils.writeValueAsString(alarmMapDb), vehicleBoot.getBootId());
    // core且5min内: 异常重启  core>5min 异常重启失败  非core>5min: 启动超时 非core<5min：无操作返回
    long startUpTimeout = vehicleBoot.getStartTime() / NumberConstant.MILLION + START_UP_TIMEOUT;
    VehicleAlarmEventDTO bootAbnormal = alarmMapDb.get(AlarmTypeEnum.BOOT_ABNORMAL.getValue());
    boolean lastBootNoAbnormal = Objects.isNull(bootAbnormal) &&
            Objects.isNull(alarmMapDb.get(AlarmTypeEnum.BOOT_ABNORMAL_FAIL.getValue()));
    if (lastBootNoAbnormal && startUpTimeout > System.currentTimeMillis()) {
      removeBootAlarm(vehicleName, alarmMapDb, GuardianErrorEnum.BOOT_TIMEOUT);
      removeBootAlarm(vehicleName, alarmMapDb, GuardianErrorEnum.BOOT_FAIL);
    } else if (startUpTimeout > System.currentTimeMillis() && vehicleBoot.getBootId() > 1 &&
            !Objects.isNull(RedissonUtils.getObject(RedisCacheEnum.VEHICLE_RESTART_ROVER.getKey(vehicleName)))) {
      removeBootAlarm(vehicleName, alarmMapDb, GuardianErrorEnum.BOOT_ABNORMAL);
      removeBootAlarm(vehicleName, alarmMapDb, GuardianErrorEnum.BOOT_ABNORMAL_FAIL);
    } else if (!Objects.isNull(bootAbnormal)
            && (vehicleBoot.getReportTime()/ NumberConstant.MILLION - START_UP_TIMEOUT) >
            vehicleBoot.getStartTime() / NumberConstant.MILLION) {
      // 存在异常启动告警，超时5min转成异常重启失败
      removeBootAlarm(vehicleName, alarmMapDb, GuardianErrorEnum.BOOT_TIMEOUT);
      removeBootAlarm(vehicleName, alarmMapDb, GuardianErrorEnum.BOOT_ABNORMAL);
      addVehilceBootAlarm(vehicleBoot, AlarmTypeEnum.BOOT_ABNORMAL_FAIL.getValue());
    } else if (startUpTimeout < System.currentTimeMillis() && lastBootNoAbnormal &&
            Objects.isNull(alarmMapDb.get(GuardianErrorEnum.BOOT_TIMEOUT.getAlarmType()))) {
      // 构造正常启动超时告警
      addVehilceBootAlarm(vehicleBoot, AlarmTypeEnum.BOOT_TIMEOUT.getValue());
    }
  }

  private void processVehicleBootFailed(VehicleBootDTO vehicleBoot, Map<String, VehicleAlarmEventDTO> alarmMapDb) {
    String vehicleName = vehicleBoot.getVehicleName();
    log.info("处理车辆{}启动失败{}-{}", vehicleName, JsonUtils.writeValueAsString(alarmMapDb), vehicleBoot.getBootId());
    // 上次启动存在异常重启失败，本次又失败则合并
    if (!Objects.isNull(alarmMapDb.get(AlarmTypeEnum.BOOT_ABNORMAL_FAIL.getValue()))) {
        return;
    }
    if (!Objects.isNull(alarmMapDb.get(AlarmTypeEnum.BOOT_ABNORMAL.getValue()))) {
      // 异常重启失败之后转异常重启失败
      removeBootAlarm(vehicleName, alarmMapDb, GuardianErrorEnum.BOOT_ABNORMAL);
      addVehilceBootAlarm(vehicleBoot, AlarmTypeEnum.BOOT_ABNORMAL_FAIL.getValue());
    } else {
      removeBootAlarm(vehicleName, alarmMapDb, GuardianErrorEnum.BOOT_TIMEOUT);
      addVehilceBootAlarm(vehicleBoot, AlarmTypeEnum.BOOT_FAIL.getValue());
    }
  }

  private void addVehilceBootAlarm(VehicleBootDTO vehicleBoot, String alarmType) {
    VehicleAlarmEventDTO vehicleAlarmEventDTO = new VehicleAlarmEventDTO();
    vehicleAlarmEventDTO.setType(alarmType);
    vehicleAlarmEventDTO.setReportTime(new Date(vehicleBoot.getReportTime() / NumberConstant.MILLION));
    if (StringUtils.equalsAny(alarmType, AlarmTypeEnum.BOOT_FAIL.getValue())) {
      vehicleAlarmEventDTO.setReportTime(new Date(vehicleBoot.getStartTime() / NumberConstant.MILLION));
    }
    vehicleAlarmEventDTO.setErrorCode(alarmType);
    vehicleAlarmEventDTO.setErrorLevel(GuardianErrorLevelEnum.ERROR.getErrorLevel());
    vehicleAlarmEventDTO.setRuntimeUuid(String.valueOf(vehicleBoot.getRuntimeUuid()));
    if (StringUtils.equalsAny(alarmType, AlarmTypeEnum.BOOT_FAIL.getValue(), AlarmTypeEnum.BOOT_ABNORMAL_FAIL.getValue())) {
      vehicleBoot.getNodeBootList().forEach(node -> {
        if (CollectionUtils.isEmpty(node.getModuleBootList())) {
          return;
        }
        ModuleBootDTO moduleBoot = Iterables.getLast(node.getModuleBootList());
        for (int i = moduleBoot.getModuleBootDetailList().size() - 1; i > 0; i--) {
          ModuleBootDetailDTO moduleBootDetailDTO = moduleBoot.getModuleBootDetailList().get(i);
          if (Objects.equals(moduleBootDetailDTO.getBootStatus(), BootStatusEnum.START_FAILED.name())) {
            vehicleAlarmEventDTO.setErrorCode(moduleBootDetailDTO.getErrorCode() + "");
            vehicleAlarmEventDTO.setErrorLevel(GuardianErrorLevelEnum.FATAL.getErrorLevel());
            vehicleAlarmEventDTO.setErrorMessage(moduleBootDetailDTO.getErrorMsg());
            return;
          }
        }
      });
    }
    reportAlarmRepository.save(vehicleBoot.getVehicleName(), Lists.newArrayList(vehicleAlarmEventDTO));
    vehicleAlarmManager.sendKafka(vehicleBoot.getVehicleName());
  }

  /**
   * 移除启动异常告警
   *
   * @param vehicleName
   * @param alarmMapDb
   * @param guardianErrorEnum
   */
  private void removeBootAlarm(String vehicleName, Map<String, VehicleAlarmEventDTO> alarmMapDb, GuardianErrorEnum guardianErrorEnum) {
    VehicleAlarmEventDTO alarm = alarmMapDb.get(guardianErrorEnum.getAlarmType());
    if (Objects.isNull(alarm)) {
      return;
    }
    reportAlarmRepository.remove(vehicleName, guardianErrorEnum.getAlarmType());
    vehicleAlarmManager.sendKafka(vehicleName);
  }

  /**
   * 记录车辆启动成功
   *
   * @param vehicleBoot
   * @param vehicleName
   */
  private void saveLastRoverBootSuccessRecord(VehicleBootDTO vehicleBoot, String vehicleName) {
    long expire = DateUtil.between(new Date(), DateUtil.endOfDay(new Date()), DateUnit.SECOND);
    String key = RedisCacheEnum.VEHICLE_LAST_ROVER_BOOT_SUCCESS.getKey(vehicleName);
    RedissonUtils.setMapObject(key, vehicleBoot.getRuntimeUuid(), vehicleBoot.getBootId(), expire);
  }

  /**
   * 判断车辆本次启动之前是否有启动成功的记录
   */
  private boolean isFirstRoverBootSucess(String vehicleName, Long bootUuid, Long bootId) {
    String key = RedisCacheEnum.VEHICLE_LAST_ROVER_BOOT_SUCCESS.getKey(vehicleName);
    Long lastSucessBootId = RedissonUtils.getMapObject(key, bootUuid);
    if (Objects.isNull(lastSucessBootId)) {
      return false;
    }
    return lastSucessBootId < bootId;
  }

}
