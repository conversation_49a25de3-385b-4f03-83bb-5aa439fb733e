/*
 * Copyright (c) 2025 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.server.business.manager.hardware.data;

import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.hardware.data.ChassisData;
import com.jdx.rover.server.api.domain.dto.hardware.data.ChassisDataDTO;
import com.jdx.rover.server.business.config.ProducerTopicProperties;
import com.jdx.rover.server.repository.jmq.JmqProducerManager;
import com.jdx.rover.server.repository.redis.hardware.data.HardwareChassisDataRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;

/**
 * 车辆硬件底盘信息数据管理器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025-08-25
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class HardwareChassisDataManager {
    private final HardwareChassisDataRepository hardwareChassisDataRepository;
    /**
     * jmq消息发送producer
     */
    private final JmqProducerManager jmqProducerManager;

    /**
     * 生产主题配置类
     */
    private final ProducerTopicProperties producerTopicProperties;

    /**
     * 处理底盘实时数据
     *
     * @param chassisDataList 底盘实时数据列表
     * @param vehicleName     车辆名称
     * @param recordTime      记录时间
     */
    public void processChassisData(List<ChassisData> chassisDataList, String vehicleName, Date recordTime) {
        if (CollectionUtils.isEmpty(chassisDataList)) {
            return;
        }

        ChassisData chassisData = chassisDataList.get(0);
        ChassisDataDTO dto = convertChassisData(chassisData, recordTime, vehicleName);
        if (dto != null) {
            hardwareChassisDataRepository.set(dto);
            jmqProducerManager.sendOrderedAndLog(producerTopicProperties.getServerHardwareChassisData(), JsonUtils.writeValueAsString(dto), vehicleName);
        }
    }

    /**
     * 转换底盘实时数据
     *
     * @param chassisData 底盘实时数据
     * @param recordTime  记录时间
     * @param vehicleName 车辆名称
     * @return 底盘实时数据DTO
     */
    private ChassisDataDTO convertChassisData(ChassisData chassisData, Date recordTime, String vehicleName) {
        if (chassisData == null) {
            return null;
        }

        return ChassisDataDTO.builder()
                .vehicleName(vehicleName)
                .errorLevel(chassisData.getErrorLevel().getNumber())
                .gear(chassisData.getGear())
                .velocity(chassisData.getVelocity())
                .workMode(chassisData.getWorkMode())
                .smallBatteryVoltage(chassisData.getSmallBatteryVoltage())
                .powerBatterySoc(chassisData.getPowerBatterySoc())
                .powerBatterySoh(chassisData.getPowerBatterySoh())
                .powerBatteryVoltage(chassisData.getPowerBatteryVoltage())
                .powerBatteryTemperature(chassisData.getPowerBatteryTemperature())
                .chargeState(chassisData.getChargeState())
                .frontLeftPressure(chassisData.getFrontLeftPressure())
                .frontRightPressure(chassisData.getFrontRightPressure())
                .backLeftPressure(chassisData.getBackLeftPressure())
                .backRightPressure(chassisData.getBackRightPressure())
                .recordTime(recordTime)
                .build();
    }
}