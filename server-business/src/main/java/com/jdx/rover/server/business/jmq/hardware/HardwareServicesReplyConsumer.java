/*
 * Copyright (c) 2025 www.jd.com All rights reserved.
 */
package com.jdx.rover.server.business.jmq.hardware;

import com.jd.jmq.client.consumer.MessageListener;
import com.jd.jmq.common.message.Message;
import com.jdx.rover.server.business.service.hardware.HardwareServicesReplyService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 车辆硬件指令回复数据JMQ消费者
 *
 * <AUTHOR>
 * @date 2025-08-25
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class HardwareServicesReplyConsumer implements MessageListener {
    /**
     * 车辆硬件信息数据服务
     */
    private final HardwareServicesReplyService hardwareServicesReplyService;

    /**
     * 接收到消息后的处理
     *
     * @param messageList 消息列表
     */
    @Override
    public void onMessage(List<Message> messageList) {
        if (CollectionUtils.isEmpty(messageList)) {
            return;
        }
        for (Message message : messageList) {
            hardwareServicesReplyService.receive(message);
        }
    }
}