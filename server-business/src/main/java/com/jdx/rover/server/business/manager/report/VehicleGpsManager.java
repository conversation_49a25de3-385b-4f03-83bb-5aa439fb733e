/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/
package com.jdx.rover.server.business.manager.report;

import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.server.api.domain.dto.status.ChangeStatusDTO;
import com.jdx.rover.server.business.config.ProducerTopicProperties;
import com.jdx.rover.server.common.utils.ParamMap;
import com.jdx.rover.server.common.utils.date.ServerDateUtils;
import com.jdx.rover.server.domain.dto.vehicle.VehicleStatusDTO;
import com.jdx.rover.server.repository.jmq.JmqProducerManager;
import com.jdx.rover.server.repository.redis.guardian.VehicleStatusRepository;
import jdx.rover.report.dto.proto.ReportDto;
import jdx.rover.report.dto.proto.ReportHeader;
import jdx.rover.report.gps.proto.ReportGps;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 收到GPS信息,对外发送kafka消息
 *
 * <AUTHOR>
 * @version 1.0
 * @date  2022-01-01
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class VehicleGpsManager {
    private final VehicleStatusRepository vehicleStatusRepository;
    private final KafkaTemplate<String, String> kafkaTemplate;
    /**
     * jmq消息发送producer
     */
    private final JmqProducerManager jmqProducerManager;

    /**
     * 生产主题配置类
     */
    private final ProducerTopicProperties producerTopicProperties;

    /**
     * 处理GPS信号
     */
    public void handle(ReportDto.ReportDTO reportDTO, ReportHeader.MessageTypeRequest messageTypeRequest) {
        if (!ReportHeader.MessageType.GPS.equals(messageTypeRequest.getMessageType())) {
            return;
        }
        // 处理GPS数据
        ReportGps.ReportGpsData dto = reportDTO.getReportGpsData();
        String vehicleName = reportDTO.getRequestHeader().getVehicleName();
        VehicleStatusDTO vehicleStatusDTO = vehicleStatusRepository.get(vehicleName);
        String gpsSignal = String.valueOf(dto.getGpsSignal());
        String sceneSignal = null;
        if (!Objects.isNull(dto.getSceneSignal())) {
            sceneSignal = String.format("%.2f", dto.getSceneSignal());
        }
        if (!Objects.isNull(vehicleStatusDTO) && Objects.equals(vehicleStatusDTO.getGpsSignal(), gpsSignal)
                && Objects.equals(vehicleStatusDTO.getSceneSignal(), sceneSignal)) {
            return;
        }
        ParamMap<VehicleStatusDTO> updateMapData = new ParamMap<>();
        ChangeStatusDTO changeStatusDTO = new ChangeStatusDTO();
        changeStatusDTO.setVehicleName(vehicleName);
        changeStatusDTO.setRecordTime(ServerDateUtils.getDateFromNanosecond(dto.getTimestamp()));
        if (Objects.isNull(vehicleStatusDTO) || !Objects.equals(vehicleStatusDTO.getGpsSignal(), gpsSignal)) {
            updateMapData.addProperty(VehicleStatusDTO::getGpsSignal, gpsSignal);
            changeStatusDTO.setOldValue(Objects.isNull(vehicleStatusDTO) ? "" : vehicleStatusDTO.getGpsSignal());
            changeStatusDTO.setNewValue(gpsSignal);
            changeStatusDTO.setChangeType(ChangeStatusDTO.ChangeTypeEnum.GPS_SIGNAL.name());
            String jsonStr = JsonUtils.writeValueAsString(changeStatusDTO);
            jmqProducerManager.sendOrdered(producerTopicProperties.getServerVehicleChangeStatus(), jsonStr, vehicleName);
            log.info("handle gpsSignal send kafka {}", jsonStr);
        }
        if (StringUtils.isNotBlank(sceneSignal) && dto.getSceneSignal() > 0 &&
                (Objects.isNull(vehicleStatusDTO) || !Objects.equals(vehicleStatusDTO.getSceneSignal(), sceneSignal))) {
            updateMapData.addProperty(VehicleStatusDTO::getSceneSignal, sceneSignal);
            changeStatusDTO.setOldValue(Objects.isNull(vehicleStatusDTO) ? "" : vehicleStatusDTO.getSceneSignal());
            changeStatusDTO.setNewValue(sceneSignal);
            changeStatusDTO.setChangeType(ChangeStatusDTO.ChangeTypeEnum.SCENE_SIGNAL.name());
            String jsonStr = JsonUtils.writeValueAsString(changeStatusDTO);
            jmqProducerManager.sendOrdered(producerTopicProperties.getServerVehicleChangeStatus(), jsonStr, vehicleName);
            log.info("handle sceneSignal send kafka {}", jsonStr);
        }
        if (updateMapData.toMap().isEmpty()) {
            return;
        }
        vehicleStatusRepository.putAllMapObject(vehicleName, updateMapData.toMap());
    }
}
