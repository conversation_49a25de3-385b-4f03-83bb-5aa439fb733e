/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.server.business.manager;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleAbnormalDTO;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleAbnormalDetailDTO;
import com.jdx.rover.server.business.config.ProducerTopicProperties;
import com.jdx.rover.server.domain.enums.RedisCacheEnum;
import com.jdx.rover.server.domain.vo.guardian.GuardianInfoAddVo;
import com.jdx.rover.server.domain.vo.guardian.VehicleExceptionLogAddVo;
import com.jdx.rover.server.repository.jmq.JmqProducerManager;
import com.jdx.rover.server.repository.redis.RedissonUtils;
import com.jdx.rover.server.repository.redis.guardian.VehicleAbnormalRepository;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CancellationException;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * This is a guardian exception info manager which contains operations on redis.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date  2022-01-01
 */
@Service
@Slf4j
public class VehicleAbnormalManager {
  /**
   * 最大异常间隔时间,超过了表示新的异常
   */
  public static final int MAX_ABNORMAL_INTERVAL = 30;

  @Autowired
  private VehicleAbnormalRepository vehicleAbnormalRepository;

  @Autowired
  private KafkaTemplate kafkaTemplate;

  /**
   * jmq消息发送producer
   */
  @Autowired
  private JmqProducerManager jmqProducerManager;

  /**
   * 生产主题配置类
   */
  @Autowired
  private ProducerTopicProperties producerTopicProperties;

  /**
   * <p>
   * Build vehicle exception info entity by GuardianInfoAddVo.
   * </p>
   *
   * @param guardianInfoAddVo The guardian info.
   * @return status of supervisor real time system.
   */
  public void handle(GuardianInfoAddVo guardianInfoAddVo) {
    List<VehicleExceptionLogAddVo> exceptionLogAddVos = guardianInfoAddVo.getVehicleExceptionLogSaveRequestList();
    if (exceptionLogAddVos.isEmpty()) {
      return;
    }
    String vehicleName = guardianInfoAddVo.getVehicleName();
    Map<String, VehicleAbnormalDetailDTO> exceptionLogMapDb = vehicleAbnormalRepository.get(vehicleName);
    Map<String, VehicleAbnormalDetailDTO> exceptionLogMap = exceptionLogAddVos.stream()
        .map(exceptionLog -> getVehicleExceptionLog(exceptionLog, exceptionLogMapDb))
        .collect(Collectors.toMap(VehicleAbnormalRepository::getHashKeyByDTO, Function.identity(), (v1, v2) -> v1));
    vehicleAbnormalRepository.save(vehicleName, exceptionLogMap);

    // 待发送的异常日志
    Map<String, VehicleAbnormalDetailDTO> sendExceptionLogMap = new HashMap<>();
    // 获取新增异常
    for (VehicleAbnormalDetailDTO entity : exceptionLogMap.values()) {
      if (isNewAbnormal(entity, exceptionLogMapDb)) {
        // 新增异常,结束时间设置为null
        entity.setEndTime(null);
        String key = VehicleAbnormalRepository.getHashKeyByDTO(entity);
        sendExceptionLogMap.put(key, entity);
      }
    }

    if (CollectionUtils.isEmpty(sendExceptionLogMap)) {
      return;
    }

    VehicleAbnormalDTO vehicleAbnormalDTO = new VehicleAbnormalDTO();
    vehicleAbnormalDTO.setRecordTime(guardianInfoAddVo.getReportTime());
    vehicleAbnormalDTO.setVehicleName(vehicleName);
    vehicleAbnormalDTO.setAbnormalMap(sendExceptionLogMap);
    String vehicleExceptionInfoEntityMsg = JsonUtils.writeValueAsString(vehicleAbnormalDTO);
    jmqProducerManager.sendOrdered(producerTopicProperties.getServerGuardianAbnormal(), vehicleExceptionInfoEntityMsg, vehicleName);
    log.info("发送车辆异常消息={}", vehicleExceptionInfoEntityMsg);
  }

  /**
   * 处理所有停止上报异常
   */
  public void handAllStopReportException() {
    boolean lockFlag = false;
    RLock lock = RedissonUtils.getRedissonClient().getLock("lock:" + RedisCacheEnum.SERVER_ABNORMAL_HASH.getValue());
    try {
      //尝试获取锁,失败等下次循环
      lockFlag = lock.tryLock();
      if (!lockFlag) {
        return;
      }
      //获取到锁,正常执行关单操作
      handStopReportException();
    } catch (CancellationException e) {
      // 未获取到锁,直接忽略,退出
      log.info("未成功获取锁,直接退出CancellationException");
    } catch (Exception e) {
      log.error("处理所有停止上报异常异常", e);
    } finally {
      if (lockFlag) {
        lock.unlock();
      }
    }
  }

  /**
   * 处理所有停止上报异常
   */
  public void handStopReportException() {
    Map<String, Map<String, VehicleAbnormalDetailDTO>> map = vehicleAbnormalRepository.getAll();
    for (Map.Entry<String, Map<String, VehicleAbnormalDetailDTO>> entry : map.entrySet()) {
      handStopReportException(entry.getKey(), entry.getValue());
    }
  }

  /**
   * 处理停止上报的异常信息
   *
   * @param vehicleName
   * @param exceptionLogMapDb
   */
  public void handStopReportException(String vehicleName, Map<String, VehicleAbnormalDetailDTO> exceptionLogMapDb) {
    if (CollectionUtils.isEmpty(exceptionLogMapDb)) {
      return;
    }
    Map<String, VehicleAbnormalDetailDTO> exceptionLogMap = new HashMap<>();
    Date now = new Date();
    for (Map.Entry<String, VehicleAbnormalDetailDTO> entry : exceptionLogMapDb.entrySet()) {
      VehicleAbnormalDetailDTO dto = entry.getValue();
      long time = DateUtil.between(now, dto.getEndTime(), DateUnit.SECOND);
      if (time < MAX_ABNORMAL_INTERVAL) {
        continue;
      }
      VehicleAbnormalDetailDTO result = vehicleAbnormalRepository.remove(vehicleName, dto.getErrorCode(), dto.getModuleName());
      if (result == null) {
        log.info("redis已经被删除,不发送异常kafka,车辆={},异常={}", vehicleName, entry.getValue());
        continue;
      }
      exceptionLogMap.put(entry.getKey(), dto);
    }
    if (CollectionUtils.isEmpty(exceptionLogMap)) {
      return;
    }
    VehicleAbnormalDTO exceptionInfoEntity = new VehicleAbnormalDTO();
    exceptionInfoEntity.setRecordTime(now);
    exceptionInfoEntity.setVehicleName(vehicleName);
    exceptionInfoEntity.setAbnormalMap(exceptionLogMap);
    String vehicleExceptionInfoEntityMsg = JsonUtils.writeValueAsString(exceptionInfoEntity);
    jmqProducerManager.sendOrdered(producerTopicProperties.getServerGuardianAbnormal(), vehicleExceptionInfoEntityMsg, vehicleName);
    log.info("发送停止上报异常消息={}", vehicleExceptionInfoEntityMsg);
  }

  /**
   * <p>
   * Get supervisor real-time vehicle exception info by GuardianInfoAddVo.
   * </p>
   *
   * @param exceptionLogAddVo The guardian exception info.
   * @return exception info of supervisor real time system.
   */
  private VehicleAbnormalDetailDTO getVehicleExceptionLog(VehicleExceptionLogAddVo exceptionLogAddVo, Map<String, VehicleAbnormalDetailDTO> exceptionLogMapDb) {
    VehicleAbnormalDetailDTO exceptionLogEntity = new VehicleAbnormalDetailDTO();
    exceptionLogEntity.setModuleName(exceptionLogAddVo.getModule());
    exceptionLogEntity.setErrorCode(exceptionLogAddVo.getErrorCode());
    exceptionLogEntity.setErrorLevel(exceptionLogAddVo.getErrorLevel());
    exceptionLogEntity.setErrorMsg(exceptionLogAddVo.getErrorMessage());
    Date startTime = getStartTime(exceptionLogAddVo, exceptionLogMapDb);
    exceptionLogEntity.setStartTime(startTime);
    Date endTIme = getEndTime(exceptionLogAddVo, exceptionLogMapDb);
    exceptionLogEntity.setEndTime(endTIme);
    return exceptionLogEntity;
  }

  /**
   * 获取开始时间
   *
   * @param exceptionLogAddVo
   * @param exceptionLogMapDb
   * @return
   */
  private Date getStartTime(VehicleExceptionLogAddVo exceptionLogAddVo, Map<String, VehicleAbnormalDetailDTO> exceptionLogMapDb) {
    Date startTime = exceptionLogAddVo.getTimestamp();
    if (CollectionUtils.isEmpty(exceptionLogMapDb)) {
      return startTime;
    }
    String key = VehicleAbnormalRepository.getHashKey(exceptionLogAddVo.getErrorCode(), exceptionLogAddVo.getModule());
    if (exceptionLogMapDb.get(key) == null) {
      return startTime;
    }
    Date startTimeDb = exceptionLogMapDb.get(key).getStartTime();
    if (startTimeDb == null) {
      return startTime;
    }
    return startTimeDb;
  }

  /**
   * 获取结束时间
   *
   * @param exceptionLogAddVo
   * @param exceptionLogMapDb
   * @return
   */
  private Date getEndTime(VehicleExceptionLogAddVo exceptionLogAddVo, Map<String, VehicleAbnormalDetailDTO> exceptionLogMapDb) {
    Date endTime = exceptionLogAddVo.getTimestamp();
    Date now = new Date();
    long time = DateUtil.between(now, endTime, DateUnit.SECOND);
    // 结束时间和当前时间相差MAX_ABNORMAL_INTERVAL分钟以上,结束时间取当前时间,防止积压插入太多数据
    if (time >= MAX_ABNORMAL_INTERVAL) {
      endTime = now;
    }
    if (CollectionUtils.isEmpty(exceptionLogMapDb)) {
      return endTime;
    }
    String key = VehicleAbnormalRepository.getHashKey(exceptionLogAddVo.getErrorCode(), exceptionLogAddVo.getModule());
    if (exceptionLogMapDb.get(key) == null) {
      return endTime;
    }
    Date endTimeDb = exceptionLogMapDb.get(key).getEndTime();
    if (endTimeDb == null) {
      return endTime;
    }
    if (endTimeDb.before(endTime)) {
      return endTime;
    }
    return endTimeDb;
  }

  /**
   * 是否新的异常(redis中不存在为新的异常)
   *
   * @param dto
   * @param exceptionLogMapDb
   * @return
   */
  private boolean isNewAbnormal(VehicleAbnormalDetailDTO dto, Map<String, VehicleAbnormalDetailDTO> exceptionLogMapDb) {
    if (CollectionUtils.isEmpty(exceptionLogMapDb)) {
      return true;
    }
    String key = VehicleAbnormalRepository.getHashKeyByDTO(dto);
    if (!exceptionLogMapDb.containsKey(key)) {
      return true;
    }
    if (!Objects.equals(dto.getErrorLevel(), exceptionLogMapDb.get(key).getErrorLevel())) {
      return true;
    }
    return false;
  }
}
