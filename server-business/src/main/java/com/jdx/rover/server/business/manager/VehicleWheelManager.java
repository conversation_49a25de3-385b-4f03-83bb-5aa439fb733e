/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.server.business.manager;

import com.jdx.rover.server.api.domain.dto.guardian.VehicleAlarmEventDTO;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleRealtimeInfoDTO;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleRealtimeWheelInfoDTO;
import com.jdx.rover.server.api.domain.enums.guardian.AlarmTypeEnum;
import com.jdx.rover.server.business.manager.report.VehicleAlarmManager;
import com.jdx.rover.server.domain.enums.WheelPositionEnum;
import com.jdx.rover.server.domain.enums.WheelPressureStatusEnum;
import java.time.Duration;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.atomic.AtomicBoolean;
import javax.annotation.Resource;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.queue.CircularFifoQueue;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/9/10 21:18
 * @description 胎压骤降处理
 */
@Service
@Slf4j
public class VehicleWheelManager {

    /**
     * VehicleAlarmManager
     */
    @Resource
    private VehicleAlarmManager vehicleAlarmManager;

    /**
     * 胎压上报时间记录 {k:vehicleName v:timestamp}
     */
    private static final ConcurrentMap<String, Long> vehicleHandleTimeMap = new ConcurrentHashMap<>(100);

    /**
     * 胎压值记录 {k:vehicleName, v:{k:wheelPosition, v:pressureQueue}}
     */
    private static final Map<String, Map<String, CircularFifoQueue<WheelInfo>>> vehicleWheelInfoQueueMap = new ConcurrentHashMap<>(100);

    /**
     * 胎压骤减阈值
     */
    private static final int PRESSURE_THRESHOLD = 30;

    /**
     * 胎压窗口时间，单位：分钟
     */
    private static final int WINDOW_SIZE = 30;

    /**
     * 胎压存储间隔1分钟
     */
    private static final long HANDLE_INTERVAL_TIME = 1_000L * 60;

    /**
     * 胎压骤减异常处理
     *
     * @param vehicleRealtimeInfoDTO vehicleRealtimeInfoDTO
     */
    public void handleWheelPressureSharpDecrease(VehicleRealtimeInfoDTO vehicleRealtimeInfoDTO, Map<String, VehicleAlarmEventDTO> alarmMapDb) {
        if (isNotSufficientIntervalTime(vehicleRealtimeInfoDTO)) {
            return;
        }

        String vehicleName = vehicleRealtimeInfoDTO.getVehicleName();
        List<VehicleRealtimeWheelInfoDTO> wheelInfos = vehicleRealtimeInfoDTO.getWheelInfo();
        if (CollectionUtils.isEmpty(wheelInfos)) {
            return;
        }

        // 获取车辆对应轮胎胎压定长队列
        vehicleWheelInfoQueueMap.putIfAbsent(vehicleName, new ConcurrentHashMap<>());
        Map<String, CircularFifoQueue<WheelInfo>> wheelPressureMap = vehicleWheelInfoQueueMap.get(vehicleName);
        VehicleAlarmEventDTO alarmDb = alarmMapDb.get(AlarmTypeEnum.PRESSURE_SHARP_DECREASE.getValue());
        AtomicBoolean wheelAlarmExist = new AtomicBoolean(false);
        wheelInfos.forEach(wheelInfo -> {
            String wheelPosition = wheelInfo.getWheelPosition();
            if (StringUtils.isBlank(wheelPosition) || WheelPositionEnum.WHEEL_POSITION_NONE.getType().equals(wheelPosition)) {
                return;
            }

            int pressure = Objects.isNull(wheelInfo.getPressure()) ? 0 : wheelInfo.getPressure();
            wheelPressureMap.putIfAbsent(wheelPosition, new CircularFifoQueue<>(WINDOW_SIZE));
            CircularFifoQueue<WheelInfo> pressures = wheelPressureMap.get(wheelPosition);

            if (!wheelAlarmExist.get()) {
                // 获取当前轮胎位置队列的胎压最大值
                pressures.stream().max(Comparator.comparing(WheelInfo::getPressure)).ifPresent(maxWheelInfo -> {
                    if (maxWheelInfo.getPressure() == 0) {
                        return;
                    }

                    // 若胎压减少值大于30，生成报警
                    if (maxWheelInfo.getPressure() - pressure > PRESSURE_THRESHOLD) {
                        if (Objects.isNull(alarmDb)) {
                            VehicleAlarmEventDTO alarm = new VehicleAlarmEventDTO();
                            alarm.setType(AlarmTypeEnum.PRESSURE_SHARP_DECREASE.getValue());
                            alarm.setReportTime(vehicleRealtimeInfoDTO.getRecordTime());
                            alarm.setErrorCode(AlarmTypeEnum.PRESSURE_SHARP_DECREASE.getValue());
                            alarm.setErrorMessage(buildErrorMsg(maxWheelInfo, vehicleRealtimeInfoDTO.getRecordTime(), wheelInfos));
                            vehicleAlarmManager.addAlarm(vehicleName, alarm);
                        }
                        wheelAlarmExist.set(true);
                    }
                });
            }

            // 缓存当前上报轮胎胎压
            WheelInfo curWheelInfo = new WheelInfo(vehicleRealtimeInfoDTO.getRecordTime(), wheelPosition, pressure);
            pressures.add(curWheelInfo);
        });

        // 30分钟内，车辆全部车胎，胎压减少值均不大于30，消失
        if (!Objects.isNull(alarmDb) && !wheelAlarmExist.get()) {
            vehicleAlarmManager.removeAlarm(vehicleName, AlarmTypeEnum.PRESSURE_SHARP_DECREASE.getValue());
        }
    }

    /**
     * 构造胎压骤减异常信息
     *
     * @param maxWheelInfo maxWheelInfo
     * @param curWheelInfos curWheelInfos
     * @return error msg
     */
    private String buildErrorMsg(WheelInfo maxWheelInfo, Date recordTime, List<VehicleRealtimeWheelInfoDTO> curWheelInfos) {
        StringBuilder errMsg = new StringBuilder();
        errMsg.append(Duration.between(maxWheelInfo.getRecordTime().toInstant(), recordTime.toInstant()).toMinutes())
            .append("分钟内，车辆")
            .append(WheelPositionEnum.of(maxWheelInfo.getWheelPosition()).getName())
            .append("胎压减少值大于30。");
        curWheelInfos.forEach(wheelInfo -> errMsg.append(WheelPositionEnum.of(wheelInfo.getWheelPosition()).getName())
            .append("：")
            .append(WheelPressureStatusEnum.of(wheelInfo.getPressureStatus()).getName())
            .append(wheelInfo.getPressure())
            .append("；")
        );
        return errMsg.toString();
    }

    /**
     * 胎压值处理间隔需大于等于1分钟
     *
     * @param vehicleRealtimeInfoDTO vehicleRealtimeInfoDTO
     * @return 数据是否需要过滤
     */
    private boolean isNotSufficientIntervalTime(VehicleRealtimeInfoDTO vehicleRealtimeInfoDTO) {
        String vehicleName = vehicleRealtimeInfoDTO.getVehicleName();
        long handleTime = vehicleHandleTimeMap.getOrDefault(vehicleName, 0L);
        long reportTime = vehicleRealtimeInfoDTO.getRecordTime().getTime();

        // 两条数据间隔小于1min,不满足最小间隔
        if (reportTime - handleTime < HANDLE_INTERVAL_TIME) {
            return true;
        }
        vehicleHandleTimeMap.put(vehicleName, reportTime);
        return false;
    }

    @Data
    @AllArgsConstructor
    static class WheelInfo {

        /**
         * 记录时间
         */
        private Date recordTime;

        /**
         * 位置
         */
        private String wheelPosition;

        /**
         * 胎压
         */
        private int pressure;
    }
}
