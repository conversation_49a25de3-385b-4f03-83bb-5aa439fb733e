/*
 * Copyright (c) 2022-2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.server.business.controller.vehicle;

import com.google.common.collect.Lists;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.server.api.domain.constants.KafkaTopicConstant;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleRealtimeInfoDTO;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleRealtimeWheelInfoDTO;
import com.jdx.rover.server.domain.enums.WheelPositionEnum;
import com.jdx.rover.server.domain.enums.WheelPressureStatusEnum;
import com.jdx.rover.server.repository.redis.RedissonUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 车辆实时胎压信息
 * <AUTHOR>
 */
@RestController
public class VehicleWheelInfoController {

  /**
   * 获取车辆实时胎压信息
   *
   * @param vehicleName
   */
  @GetMapping(value = "/server/business/vehicle/wheel_info/{vehicleName}")
  public HttpResult getWheelInfo(@PathVariable("vehicleName") String vehicleName) {
    String redisKey = KafkaTopicConstant.SERVER_GUARDIAN_REALTIME + "_" + vehicleName;
    VehicleRealtimeInfoDTO dto = RedissonUtils.getObject(redisKey);
    if (dto == null || dto.getWheelInfo() == null) {
      return HttpResult.success(Lists.newArrayList());
    }
    List<VehicleRealtimeWheelInfoDTO> data = dto.getWheelInfo().stream().map(wheelInfo -> {
      VehicleRealtimeWheelInfoDTO wheelInfoDto = new VehicleRealtimeWheelInfoDTO();
      wheelInfoDto.setPressure(wheelInfo.getPressure());
      wheelInfoDto.setPressureStatus(WheelPressureStatusEnum.of(wheelInfo.getPressureStatus()).getName());
      wheelInfoDto.setWheelPosition(WheelPositionEnum.of(wheelInfo.getWheelPosition()).getName());
      return wheelInfoDto;
    }).collect(Collectors.toList());
    return HttpResult.success(data);
  }
}
