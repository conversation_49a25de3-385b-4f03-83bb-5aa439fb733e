package com.jdx.rover.server.business.service.alarm;

import com.jdx.rover.server.business.manager.localview.PlanningManager;
import jdx.rover.guardian.dto.LocalView;
import lombok.extern.slf4j.Slf4j;

/**
 * 遇阻停车
 *
 * <AUTHOR>
 */
@Slf4j
public class StopTimeoutService {
    /**
     * 是否遇阻停车
     *
     * @param dto
     * @return
     */
    public static boolean isReportAlarm(LocalView.LocalViewDTO dto) {
        LocalView.PlanningDecision decision = dto.getPlanning().getDecision();
        // 在路口内属于路口遇阻,不处理
        if (decision.getIsInIntersection()) {
            return false;
        }
        // 看灯状态,不处理
        if (PlanningManager.isStopTrafficLight(dto)) {
            return false;
        }
        // 停车事件小于60s,不报警
        if (decision.getStopTime() < 60) {
            return false;
        }
        return true;
    }
}
