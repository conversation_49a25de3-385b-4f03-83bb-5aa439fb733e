/*
 * Copyright (c) 2022-2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.server.business.controller.vehicle;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.server.api.domain.constants.KafkaTopicConstant;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleChargeInfoDTO;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleRealtimeInfoDTO;
import com.jdx.rover.server.api.domain.enums.guardian.ChargeStateEnum;
import com.jdx.rover.server.api.domain.enums.guardian.VehicleStateEnum;
import com.jdx.rover.server.api.domain.guardian.VehiclePowerInfoEntity;
import com.jdx.rover.server.repository.redis.RedissonUtils;
import com.jdx.rover.server.repository.redis.guardian.VehicleKafkaPowerRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RestController;

/**
 * 车辆实时充电状态
 * <AUTHOR>
 */
@RestController
public class VehicleChargeInfoController {

  @Autowired
  private VehicleKafkaPowerRepository vehicleKafkaPowerRepository;

  /**
   * 获取车辆实时充电状态
   *
   * @param vehicleName
   */
  @GetMapping(value = "/server/business/vehicle/charge_state/{vehicleName}")
  public HttpResult getChargeInfo(@PathVariable("vehicleName") String vehicleName) {
    VehiclePowerInfoEntity powerInfoEntity = vehicleKafkaPowerRepository.getValue(vehicleName);
    VehicleChargeInfoDTO vehicleChargeInfoDto = new VehicleChargeInfoDTO();
    vehicleChargeInfoDto.setChargeState(ChargeStateEnum.UNKNOWN.getChargeState());
    vehicleChargeInfoDto.setVehicleState(VehicleStateEnum.UNKNOWN.getVehicleState());
    if (powerInfoEntity != null) {
      vehicleChargeInfoDto.setChargeState(powerInfoEntity.getChargeState());
      vehicleChargeInfoDto.setPower(powerInfoEntity.getPower());
    }
    String redisKey = KafkaTopicConstant.SERVER_GUARDIAN_REALTIME + "_" + vehicleName;
    VehicleRealtimeInfoDTO dto = RedissonUtils.getObject(redisKey);
    if (dto != null) {
      vehicleChargeInfoDto.setVehicleState(dto.getVehicleState());
    }
    return HttpResult.success(vehicleChargeInfoDto);
  }
}
