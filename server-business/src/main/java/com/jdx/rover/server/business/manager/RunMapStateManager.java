/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/
package com.jdx.rover.server.business.manager;

import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.server.api.domain.dto.status.ChangeStatusDTO;
import com.jdx.rover.server.business.config.ProducerTopicProperties;
import com.jdx.rover.server.common.utils.date.ServerDateUtils;
import com.jdx.rover.server.domain.dto.vehicle.VehicleStatusDTO;
import com.jdx.rover.server.repository.jmq.JmqProducerManager;
import com.jdx.rover.server.repository.redis.guardian.VehicleStatusRepository;
import jdx.rover.guardian.data.dto.GuardianDataDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * rover状态处理
 *
 * <AUTHOR>
 * @version 1.0
 * @date  2022-01-01
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class RunMapStateManager {
    private final KafkaTemplate<String, String> kafkaTemplate;

    private final VehicleStatusRepository vehicleStatusRepository;
    /**
     * jmq消息发送producer
     */
    private final JmqProducerManager jmqProducerManager;

    /**
     * 生产主题配置类
     */
    private final ProducerTopicProperties producerTopicProperties;

    /**
     * 处理电量信息
     */
    public void handle(GuardianDataDto.GuardianInfoDTO dto) {
        GuardianDataDto.RoverStatus.RunMapState runMapState = dto.getRoverStatus().getRunMapState();
        if (Objects.equals(runMapState, GuardianDataDto.RoverStatus.RunMapState.MAP_UNKNOWN)) {
            return;
        }

        String vehicleName = dto.getVehicleId();
        VehicleStatusDTO vehicleStatusDTO = vehicleStatusRepository.get(vehicleName);
        if (!Objects.isNull(vehicleStatusDTO) && Objects.equals(vehicleStatusDTO.getRunMapState(), runMapState.name())) {
            return;
        }

        vehicleStatusRepository.putMapValue(vehicleName, VehicleStatusDTO::getRunMapState, runMapState.name());

        ChangeStatusDTO changeStatusDTO = new ChangeStatusDTO();
        changeStatusDTO.setVehicleName(vehicleName);
        changeStatusDTO.setOldValue(vehicleStatusDTO.getRunMapState());
        changeStatusDTO.setNewValue(runMapState.name());
        changeStatusDTO.setChangeType(ChangeStatusDTO.ChangeTypeEnum.RUN_MAP_STATE.name());
        changeStatusDTO.setRecordTime(ServerDateUtils.getDateFromNanosecond(dto.getTimestamp()));
        String jsonStr = JsonUtils.writeValueAsString(changeStatusDTO);
        jmqProducerManager.sendOrderedAndLog(producerTopicProperties.getServerVehicleChangeStatus(), jsonStr, vehicleName);
    }
}
