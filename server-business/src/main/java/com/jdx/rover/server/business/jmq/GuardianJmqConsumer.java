/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */
package com.jdx.rover.server.business.jmq;

import com.jd.jmq.client.consumer.MessageListener;
import com.jd.jmq.common.message.Message;
import com.jdx.rover.server.business.service.GuardianService;
import jdx.rover.guardian.data.dto.GuardianDataDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * guardian消息接收Listener
 *
 * <AUTHOR>
 * @date 2024-12-17
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class GuardianJmqConsumer implements MessageListener {
    /**
     * Guardian消息处理
     */
    private final GuardianService guardianService;

    /**
     * 接收到消息后的处理
     *
     * @param messageList 消息列表
     */
    @Override
    public void onMessage(List<Message> messageList) {
        if (CollectionUtils.isEmpty(messageList)) {
            return;
        }
        for (Message message : messageList) {
            try {
                GuardianDataDto.GuardianInfoDTO dto = GuardianDataDto.GuardianInfoDTO.parseFrom(message.getByteBody());
                guardianService.receive(dto);
            } catch (Exception e) {
                log.error("处理消息失败!{}", message, e);
            }
        }
    }
}
