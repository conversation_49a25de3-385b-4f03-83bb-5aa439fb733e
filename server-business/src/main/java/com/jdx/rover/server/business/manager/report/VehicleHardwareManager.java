/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/
package com.jdx.rover.server.business.manager.report;

import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.server.api.domain.dto.hardware.ReportHardwareEventDTO;
import com.jdx.rover.server.api.domain.dto.status.ChangeStatusDTO;
import com.jdx.rover.server.business.config.ProducerTopicProperties;
import com.jdx.rover.server.common.utils.date.ServerDateUtils;
import com.jdx.rover.server.domain.dto.vehicle.VehicleStatusDTO;
import com.jdx.rover.server.repository.jmq.JmqProducerManager;
import com.jdx.rover.server.repository.redis.guardian.VehicleStatusRepository;
import jdx.rover.report.dto.proto.ReportDto;
import jdx.rover.report.dto.proto.ReportHeader;
import jdx.rover.report.gps.proto.ReportGps;
import jdx.rover.report.hardware.proto.ReportHardware;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 处理车辆硬件上报信息
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025-07-15
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class VehicleHardwareManager {

    /**
     * jmq消息发送producer
     */
    private final JmqProducerManager jmqProducerManager;

    /**
     * 生产主题配置类
     */
    private final ProducerTopicProperties producerTopicProperties;

    /**
     * 处理硬件类事件上报
     */
    public void handle(ReportDto.ReportDTO reportDTO, ReportHeader.MessageTypeRequest messageTypeRequest) {
        if (!ReportHeader.MessageType.HARDWARE.equals(messageTypeRequest.getMessageType())) {
            return;
        }
        // 处理硬件类事件上报数据
        ReportHardware.ReportHardwareData dto = reportDTO.getReportHardwareData();
        String vehicleName = reportDTO.getRequestHeader().getVehicleName();
        switch (dto.getEventType()) {
            case DC_STALK_EVENT:
                ReportHardwareEventDTO reportHardwareEventDto = new ReportHardwareEventDTO();
                reportHardwareEventDto.setVehicleName(vehicleName);
                reportHardwareEventDto.setRecordTime(ServerDateUtils.getDateFromNanosecond(dto.getDataCollectionStalkEvent().getReportTime()));
                reportHardwareEventDto.setEventType(ReportHardwareEventDTO.EventTypeEnum.DC_STALK_EVENT.getValue());
                String jsonStr = JsonUtils.writeValueAsString(reportHardwareEventDto);
                log.info("发送数采车{}拨杆事件{}", vehicleName, jsonStr);
                jmqProducerManager.sendOrdered(producerTopicProperties.getServerReportHardwareEvent(), jsonStr, vehicleName);
                break;
            default:
                log.info("忽略处理硬件类上报事件{}", dto.getEventType());

        }
    }
}