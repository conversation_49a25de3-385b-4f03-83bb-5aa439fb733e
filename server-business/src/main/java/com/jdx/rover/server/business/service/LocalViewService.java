package com.jdx.rover.server.business.service;

import com.jdx.rover.common.constant.NumberConstant;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleAlarmEventDTO;
import com.jdx.rover.server.api.domain.dto.localview.LocalViewPlanningDTO;
import com.jdx.rover.server.api.domain.dto.localview.PlanningDecisionDTO;
import com.jdx.rover.server.api.domain.dto.localview.PlanningTrafficLightDTO;
import com.jdx.rover.server.api.domain.enums.guardian.AlarmTypeEnum;
import com.jdx.rover.server.business.config.ProducerTopicProperties;
import com.jdx.rover.server.business.manager.report.VehicleAlarmManager;
import com.jdx.rover.server.business.service.alarm.GateStuckService;
import com.jdx.rover.server.business.service.alarm.IntersectionStuckService;
import com.jdx.rover.server.business.service.alarm.VehicleStopTimeoutService;
import com.jdx.rover.server.domain.enums.HandleAlarmEnum;
import com.jdx.rover.server.repository.jmq.JmqProducerManager;
import com.jdx.rover.server.repository.redis.guardian.ReportAlarmRepository;
import jdx.rover.guardian.dto.LocalView;
import jdx.rover.guardian.dto.LocalView.LocalViewDTO;
import jdx.rover.guardian.dto.LocalView.PlanningDecision;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.function.BiFunction;
import java.util.function.Function;

/**
 * 车辆LocalView数据service
 *
 * <AUTHOR>
 * @version 1.0
 * @date  2022-01-01
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class LocalViewService {
    private final KafkaTemplate<String, String> kafkaTemplate;
    private final VehicleAlarmManager vehicleAlarmManager;
    private final ReportAlarmRepository reportAlarmRepository;
    private static ConcurrentMap<String, Long> vehicleHandleTimeMap = new ConcurrentHashMap<>(100);
    /**
     * jmq消息发送producer
     */
    private final JmqProducerManager jmqProducerManager;

    /**
     * 生产主题配置类
     */
    private final ProducerTopicProperties producerTopicProperties;

    /**
     * 一个小时的纳秒数
     */
    private static final long ONE_HOUR_NANOSECOND = 3600_000_000_000L;

    /**
     * 处理消息
     */
    public void receive(byte[] message) {
        try {
            LocalView.LocalViewDTO dto = LocalView.LocalViewDTO.parseFrom(message);
            receive(dto);
        } catch (Exception e) {
            log.error("解析local view数据失败!{}", null, e);
        }
    }

    /**
     * 处理消息
     */
    private void receive(LocalView.LocalViewDTO dto) {
        if (isNotSufficientIntervalTime(dto)) {
            return;
        }
        Map<String, VehicleAlarmEventDTO> alarmMap = reportAlarmRepository.get(dto.getAuthInfo().getVehicleId());
        // 路口遇阻
        handle(AlarmTypeEnum.VEHICLE_STOP_INTERSECTION_STUCK.getValue(), dto, alarmMap, IntersectionStuckService::isReportAlarm);
        // 遇阻停车
        handle(AlarmTypeEnum.VEHICLE_STOP_TIMEOUT.getValue(), dto, alarmMap, VehicleStopTimeoutService::isReportAlarm);
        // 过门遇阻
        handle(AlarmTypeEnum.GATE_STUCK.getValue(), dto, alarmMap, GateStuckService::isReportAlarm);
        // 停靠困难
//        handle(AlarmTypeEnum.PARK_HARD.getValue(), dto, alarmMap, ParkHardService::isReportAlarm);
        sendKafkaMsg(dto);
    }

    /**
     * 支持Function处理
     */
    private void handle(String alarmType, LocalView.LocalViewDTO dto, Map<String, VehicleAlarmEventDTO> alarmMap
            , Function<LocalView.LocalViewDTO, Boolean> function) {
        if (!function.apply(dto)) {
            vehicleAlarmManager.removeAlarm(dto.getAuthInfo().getVehicleId(), alarmType, alarmMap);
            return;
        }
        vehicleAlarmManager.addAlarm(dto.getAuthInfo().getVehicleId(), alarmType
                , new Date(dto.getTimestamp() / NumberConstant.MILLION), alarmMap);
    }

    /**
     * 支持BiFunction处理
     */
    private void handle(String alarmType, LocalView.LocalViewDTO dto, Map<String, VehicleAlarmEventDTO> alarmMap
            , BiFunction<LocalView.LocalViewDTO, Map<String, VehicleAlarmEventDTO>, HandleAlarmEnum> function) {
        HandleAlarmEnum handleAlarm = function.apply(dto, alarmMap);
        if (handleAlarm == HandleAlarmEnum.REMOVE) {
            vehicleAlarmManager.removeAlarm(dto.getAuthInfo().getVehicleId(), alarmType, alarmMap);
        } else if (handleAlarm == HandleAlarmEnum.ADD) {
            vehicleAlarmManager.addAlarm(dto.getAuthInfo().getVehicleId(), alarmType
                    , new Date(dto.getTimestamp() / NumberConstant.MILLION), alarmMap);
        }
    }


    /**
     * 不满足最小时间间隔
     *
     * @param dto
     * @return
     */
    private boolean isNotSufficientIntervalTime(LocalView.LocalViewDTO dto) {
        String vehicleName = dto.getAuthInfo().getVehicleId();
        long handleTime = vehicleHandleTimeMap.getOrDefault(vehicleName, 0L);
        // 两条数据间隔小于1s,不满足最小间隔
        if (dto.getTimestamp() - handleTime < 1_000_000_000) {
            return true;
        } else if (dto.getTimestamp() - System.currentTimeMillis() * NumberConstant.MILLION > ONE_HOUR_NANOSECOND) {
            // 数据时间大于服务器时间超过1小时,非法数据,不处理
            return true;
        }
        vehicleHandleTimeMap.put(vehicleName, dto.getTimestamp());
        return false;
    }

    /**
     * 推送消息数据
     *
     * @param dto 3D原始数据
     * @return
     */
    private void sendKafkaMsg(LocalViewDTO dto) {
        com.jdx.rover.server.api.domain.dto.localview.LocalViewDTO localViewDto = new com.jdx.rover.server.api.domain.dto.localview.LocalViewDTO();
        localViewDto.setVehicleName(dto.getAuthInfo().getVehicleId());
        localViewDto.setTimestamp(dto.getTimestamp());
        if (Objects.nonNull(dto.getCar())) {
            localViewDto.setVelocity(dto.getCar().getVelocity());
            localViewDto.setSteerAngle(dto.getCar().getSteerAngle());
            if (Objects.nonNull(dto.getCar().getDrivingMode())) {
                localViewDto.setDrivingMode(dto.getCar().getDrivingMode().name());
            }
        }
        LocalViewPlanningDTO planningDto = new LocalViewPlanningDTO();
        if (Objects.nonNull(dto.getPlanning())) {
            PlanningDecisionDTO decision = new PlanningDecisionDTO();
            PlanningDecision planningDecision = dto.getPlanning().getDecision();
            if (Objects.nonNull(planningDecision)) {
                decision.setIsInFrontOfGate(planningDecision.getIsInFrontOfGate());
                decision.setIsInIntersection(planningDecision.getIsInIntersection());
                decision.setIsInPark(planningDecision.getIsInPark());
                decision.setBehaviorDecisionList(null);
            }
            planningDto.setDecision(decision);
            List<PlanningTrafficLightDTO> interestedLight = new ArrayList<>();
            planningDto.setInterestedLight(interestedLight);
        }
        localViewDto.setPlanning(planningDto);
        jmqProducerManager.sendOrdered(producerTopicProperties.getServerDecreaseLocalView(), JsonUtils.writeValueAsString(localViewDto), localViewDto.getVehicleName());
    }
}
