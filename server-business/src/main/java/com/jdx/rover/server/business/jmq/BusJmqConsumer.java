/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */
package com.jdx.rover.server.business.jmq;

import com.jd.jmq.client.consumer.MessageListener;
import com.jd.jmq.common.message.Message;
import com.jdx.rover.server.business.service.BusService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 车辆上报总线数据监听
 *
 * <AUTHOR>
 * @date 2024-12-17
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class BusJmqConsumer implements MessageListener {
    /**
     * 车辆上报总线数据service
     */
    private final BusService busService;

    /**
     * 接收到消息后的处理
     *
     * @param messageList 消息
     */
    @Override
    public void onMessage(List<Message> messageList) {
        if (CollectionUtils.isEmpty(messageList)) {
            return;
        }
        for (Message message : messageList) {
            try {
                busService.receive(message.getByteBody());
            } catch (Exception e) {
                log.error("处理消息失败!{}", message, e);
            }
        }
    }
}