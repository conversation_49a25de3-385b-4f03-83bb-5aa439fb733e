package com.jdx.rover.server.business.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.server.api.domain.dto.mqtt.MqttRequestDTO;
import com.jdx.rover.server.api.domain.dto.vehicle.GridDataDTO;
import com.jdx.rover.server.repository.redis.guardian.VehicleGridRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * Guardian消息处理
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class AndroidInfoService {
    /**
     * 10分钟的毫秒表示
     */
    private static final int TEN_MINUTES = 10 * 60 * 1000;
    /**
     * 格口状态
     */
    private static final String GRID_STATUS = "grid_status";
    @Autowired
    private VehicleGridRepository vehicleGridRepository;

    public void receiveList(List<String> messageList) {
        if (CollectionUtils.isEmpty(messageList)) {
            return;
        }
        for (String message : messageList) {
            try {
                this.receive(message);
            } catch (Exception e) {
                log.error("安卓消息处理失败", e);
            }
            log.info("处理完成安卓消息={}", message);
        }
    }

    public void receive(String message) {
        MqttRequestDTO mqttRequestDTO = JsonUtils.readValue(message, MqttRequestDTO.class);
        // 如果请求时间-系统时间>10分钟,直接抛弃数据
        if (mqttRequestDTO.getHeader().getRequestTime() == null
                || mqttRequestDTO.getHeader().getRequestTime() - System.currentTimeMillis() > TEN_MINUTES) {
            log.warn("忽略请求时间大于系统时间10分钟!{}", message);
            return;
        }
        if (Objects.equals(GRID_STATUS, mqttRequestDTO.getHeader().getMessageType())) {
            handGridStatus(message);
        }
    }

    /**
     * 处理格口状态
     *
     * @param message
     */
    private void handGridStatus(String message) {
        TypeReference<MqttRequestDTO<GridDataDTO>> typeReference = new TypeReference<MqttRequestDTO<GridDataDTO>>() {
        };
        MqttRequestDTO<GridDataDTO> gridDataRequest = JsonUtils.readValue(message, typeReference);
        String vehicleName = gridDataRequest.getHeader().getVehicleName();
        Date recordTime = new Date(gridDataRequest.getHeader().getRequestTime());
        GridDataDTO gridRedis = vehicleGridRepository.get(vehicleName);
        if (gridRedis != null && gridRedis.getRecordTime() != null
                && gridRedis.getRecordTime().after(recordTime)) {
            log.warn("redis中格口状态新于请求中格口状态!车辆={},请求={},redis={}", vehicleName, recordTime, gridRedis.getRecordTime());
            return;
        }
        GridDataDTO gridDataRedis = new GridDataDTO();
        gridDataRedis.setVehicleName(vehicleName);
        gridDataRedis.setRecordTime(recordTime);
        gridDataRedis.setGridList(gridDataRequest.getData().getGridList());
        vehicleGridRepository.put(gridDataRedis);
    }
}
