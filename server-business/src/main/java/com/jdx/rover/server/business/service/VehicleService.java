package com.jdx.rover.server.business.service;

import com.google.common.collect.Lists;
import com.jdx.rover.server.api.domain.constants.KafkaTopicConstant;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleLauchStateInfoDTO;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleLocationAndPowerDTO;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleLocationDTO;
import com.jdx.rover.server.api.domain.dto.guardian.VehiclePncInfoDTO;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleRealtimeInfoDTO;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleRealtimeWheelInfoDTO;
import com.jdx.rover.server.api.domain.guardian.VehicleStateInfoEntity;
import com.jdx.rover.server.domain.enums.WheelPositionEnum;
import com.jdx.rover.server.domain.enums.WheelPressureStatusEnum;
import com.jdx.rover.server.repository.redis.RedissonUtils;
import com.jdx.rover.server.repository.redis.guardian.VehicleStateInfoRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @description: VehicleService
 * @author: wangguotai
 * @create: 2024-10-10 18:04
 **/
@Slf4j
@RequiredArgsConstructor
@Service
public class VehicleService {

    private final VehicleStateInfoRepository vehicleStateInfoRepository;

    /**
     * 获取车辆实时位置
     */
    public VehicleLocationDTO getVehicleLocation(String vehicleName) {
        String redisKey = KafkaTopicConstant.SERVER_GUARDIAN_REALTIME + "_" + vehicleName;
        VehicleRealtimeInfoDTO dto = RedissonUtils.getObject(redisKey);
        if (dto == null) {
            return null;
        }
        VehicleLocationDTO result = new VehicleLocationDTO();
        result.setVehicleName(vehicleName);
        result.setLat(dto.getLat());
        result.setLon(dto.getLon());
        result.setHeading(dto.getHeading());
        result.setRecordTime(dto.getRecordTime());
        return result;
    }

    /**
     * 获取车辆实时位置和电量信息
     */
    public VehicleLocationAndPowerDTO getLocationAndPower(String vehicleName) {
        String redisKey = KafkaTopicConstant.SERVER_GUARDIAN_REALTIME + "_" + vehicleName;
        VehicleRealtimeInfoDTO dto = RedissonUtils.getObject(redisKey);
        if (dto == null) {
            return null;
        }
        VehicleLocationAndPowerDTO result = new VehicleLocationAndPowerDTO();
        result.setVehicleName(vehicleName);
        result.setLat(dto.getLat());
        result.setLon(dto.getLon());
        result.setHeading(dto.getHeading());
        result.setPower(dto.getPower());
        result.setRecordTime(dto.getRecordTime());
        return result;
    }

    /**
     * 获取车辆实时胎压信息
     */
    public List<VehicleRealtimeWheelInfoDTO> getWheelInfo(String vehicleName) {
        String redisKey = KafkaTopicConstant.SERVER_GUARDIAN_REALTIME + "_" + vehicleName;
        VehicleRealtimeInfoDTO dto = RedissonUtils.getObject(redisKey);
        if (dto == null || dto.getWheelInfo() == null) {
            return Lists.newArrayList();
        }
        return dto.getWheelInfo().stream().map(wheelInfo -> {
            VehicleRealtimeWheelInfoDTO wheelInfoDto = new VehicleRealtimeWheelInfoDTO();
            wheelInfoDto.setPressure(wheelInfo.getPressure());
            wheelInfoDto.setPressureStatus(WheelPressureStatusEnum.of(wheelInfo.getPressureStatus()).getName());
            wheelInfoDto.setWheelPosition(WheelPositionEnum.of(wheelInfo.getWheelPosition()).getName());
            return wheelInfoDto;
        }).collect(Collectors.toList());
    }

    /**
     * 获取车辆实时胎压信息
     */
    public VehicleLauchStateInfoDTO getVehicleStartInitStateInfo(String vehicleName) {
        VehicleStateInfoEntity vehicleStateInfo = vehicleStateInfoRepository.getByVehicleName(vehicleName);
        VehicleLauchStateInfoDTO stateInfoDto = new VehicleLauchStateInfoDTO();
        if (vehicleStateInfo == null) {
            return stateInfoDto;
        }
        stateInfoDto.setRecordTime(vehicleStateInfo.getRecordTime());
        stateInfoDto.setVehicleState(vehicleStateInfo.getVehicleState());
        stateInfoDto.setVehicleStateMessage(vehicleStateInfo.getVehicleStateMessage());
        return stateInfoDto;
    }

    /**
     * 获取车辆实PNC信息
     */
    public VehiclePncInfoDTO getVehiclePnc(String vehicleName) {
        String redisKey = KafkaTopicConstant.SERVER_GUARDIAN_PNC + "_" + vehicleName;
        VehiclePncInfoDTO dto = RedissonUtils.getObject(redisKey);
        log.info("feign pnc vehicleName={}, result={}", vehicleName, dto);
        return dto;
    }
}