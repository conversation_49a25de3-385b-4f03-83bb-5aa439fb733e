/*
 * Copyright (c) 2025 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.server.business.manager.hardware;

import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.hardware.info.Abnormal;
import com.jdx.rover.hardware.info.DomainBoot;
import com.jdx.rover.hardware.info.HardwareInfo;
import com.jdx.rover.hardware.info.LidarStatus;
import com.jdx.rover.hardware.info.MessageType;
import com.jdx.rover.hardware.info.SimStatus;
import com.jdx.rover.server.api.domain.dto.hardware.status.DomainBootDTO;
import com.jdx.rover.server.api.domain.dto.hardware.status.HardwareStatusDTO;
import com.jdx.rover.server.api.domain.dto.hardware.status.LidarStatusDTO;
import com.jdx.rover.server.api.domain.dto.hardware.status.SimStatusDTO;
import com.jdx.rover.server.api.domain.dto.report.abnormal.VehicleAbnormalDTO;
import com.jdx.rover.server.api.domain.dto.report.alarm.VehicleAlarmEventDTO;
import com.jdx.rover.server.business.manager.VehicleSimCardManager;
import com.jdx.rover.server.business.config.ProducerTopicProperties;
import com.jdx.rover.server.business.manager.report.VehicleAbnormalReportManager;
import com.jdx.rover.server.common.utils.convert.ConvertListUtil;
import com.jdx.rover.server.repository.jmq.JmqProducerManager;
import com.jdx.rover.server.repository.redis.hardware.HardwareStatusRepository;
import com.jdx.rover.server.repository.redis.guardian.ReportAlarmRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 车辆硬件状态数据管理器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025-08-25
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class HardwareStatusManager {
    private final HardwareStatusRepository hardwareStatusRepository;
    private final VehicleAbnormalReportManager vehicleAbnormalReportManager;
    private final VehicleSimCardManager vehicleSimCardManager;
    private final ReportAlarmRepository reportAlarmRepository;

    /**
     * jmq消息发送producer
     */
    private final JmqProducerManager jmqProducerManager;

    /**
     * 生产主题配置类
     */
    private final ProducerTopicProperties producerTopicProperties;

    /**
     * 处理硬件信息
     *
     * @param hardwareInfo 硬件信息
     */
    public void processHardwareStatus(HardwareInfo hardwareInfo) {
        try {
            HardwareStatusDTO dto = convertToDTO(hardwareInfo);
            hardwareStatusRepository.set(dto);
            jmqProducerManager.sendOrderedAndLog(producerTopicProperties.getServerHardwareStatus(), JsonUtils.writeValueAsString(dto), dto.getVehicleName());

            // 处理SIM卡状态异常告警
            if (dto.getSimStatusList() != null && !dto.getSimStatusList().isEmpty()) {
                try {
                    Map<String, VehicleAlarmEventDTO> alarmMapDb = reportAlarmRepository.get(dto.getVehicleName());
                    vehicleSimCardManager.handleSimCardAbnormal(dto.getVehicleName(), dto.getSimStatusList(), null, alarmMapDb, dto.getRecordTime());
                } catch (Exception e) {
                    log.error("处理SIM卡状态异常告警失败，车辆：{}", dto.getVehicleName(), e);
                }
            }
        } catch (Exception e) {
            log.error("处理硬件状态数据失败: {}", hardwareInfo.getHeader().getSendName(), e);
        }
    }

    /**
     * 将Protobuf对象转换为DTO对象
     *
     * @param hardwareInfo Protobuf硬件信息对象
     * @return 硬件信息DTO对象
     */
    private HardwareStatusDTO convertToDTO(HardwareInfo hardwareInfo) {
        String vehicleName = hardwareInfo.getHeader().getSendName();
        Date recordTime = new Date(hardwareInfo.getHeader().getRequestTime());

        // 获取现有数据或创建新对象
        HardwareStatusDTO dto = Optional.ofNullable(hardwareStatusRepository.get(vehicleName))
                .orElse(new HardwareStatusDTO());

        // 设置基本信息
        dto.setVehicleName(vehicleName);
        dto.setRecordTime(recordTime);

        // 根据消息类型更新对应字段
        for (MessageType messageType : hardwareInfo.getMessageTypeList()) {
            updateDTOByMessageType(dto, hardwareInfo, messageType, recordTime);
        }

        return dto;
    }

    /**
     * 根据消息类型更新DTO对象的相应字段
     *
     * @param dto          硬件信息DTO对象
     * @param hardwareInfo Protobuf硬件信息对象
     * @param messageType  消息类型
     * @param recordTime   记录时间
     */
    private void updateDTOByMessageType(HardwareStatusDTO dto, HardwareInfo hardwareInfo,
                                        MessageType messageType, Date recordTime) {
        String vehicleName = dto.getVehicleName();
        switch (messageType) {
            case DOMAIN_BOOT -> dto.setDomainBootList(ConvertListUtil.convertList(hardwareInfo.getDomainBootList(),
                    item -> convertDomainBoot(item, recordTime)));
            case LIDAR_STATUS -> dto.setLidarStatusList(ConvertListUtil.convertList(hardwareInfo.getLidarStatusList(),
                    item -> convertLidarStatus(item, recordTime)));
            case SIM_STATUS -> dto.setSimStatusList(ConvertListUtil.convertList(hardwareInfo.getSimStatusList(),
                    item -> convertSimStatus(item, recordTime)));
            case ABNORMAL -> {
                List<VehicleAbnormalDTO> vehicleAbnormalList = ConvertListUtil.convertList(hardwareInfo.getAbnormalList(),
                        item -> convertAbnormal(item, recordTime, vehicleName));
                vehicleAbnormalReportManager.saveRedisAndSendJmq(vehicleAbnormalList, vehicleName);
            }
            default -> {
            }
        }
    }

    /**
     * 转换域控启动信息
     *
     * @param domainBoot 域控启动信息
     * @param recordTime 记录时间
     * @return 域控启动信息DTO
     */
    private DomainBootDTO convertDomainBoot(DomainBoot domainBoot, Date recordTime) {
        if (domainBoot == null) {
            return null;
        }

        DomainBootDTO dto = new DomainBootDTO();
        dto.setStartupTime(domainBoot.getStartupTime() > 0 ?
                new Date(domainBoot.getStartupTime()) : null);
        dto.setShutdownTime(domainBoot.getShutdownTime() > 0 ?
                new Date(domainBoot.getShutdownTime()) : null);
        dto.setDeviceId(domainBoot.getDeviceId());
        dto.setRecordTime(recordTime);
        return dto;
    }

    /**
     * 转换激光雷达状态信息
     *
     * @param lidarStatus 激光雷达状态信息
     * @param recordTime  记录时间
     * @return 激光雷达状态信息DTO
     */
    private LidarStatusDTO convertLidarStatus(LidarStatus lidarStatus, Date recordTime) {
        if (lidarStatus == null) {
            return null;
        }

        LidarStatusDTO dto = new LidarStatusDTO();
        dto.setStandbyMode(lidarStatus.getStandbyMode());
        dto.setClockStatus(lidarStatus.getClockStatus());
        dto.setPosition(lidarStatus.getPosition());
        dto.setDeviceId(lidarStatus.getDeviceId());
        dto.setRecordTime(recordTime);
        return dto;
    }


    /**
     * 转换SIM卡状态信息
     *
     * @param simStatus  SIM卡状态信息
     * @param recordTime 记录时间
     * @return SIM卡状态信息DTO
     */
    private SimStatusDTO convertSimStatus(SimStatus simStatus, Date recordTime) {
        if (simStatus == null) {
            return null;
        }

        SimStatusDTO dto = new SimStatusDTO();
        dto.setOnlineStatus(simStatus.getOnlineStatus().getNumber());
        dto.setOnlineTime(simStatus.getOnlineTime() > 0 ?
                new Date(simStatus.getOnlineTime()) : null);
        dto.setOfflineTime(simStatus.getOfflineTime() > 0 ?
                new Date(simStatus.getOfflineTime()) : null);
        dto.setMainCard(simStatus.getMainCard());
        dto.setDeviceId(simStatus.getDeviceId());
        dto.setRecordTime(recordTime);
        return dto;
    }


    /**
     * 转换异常信息
     *
     * @param abnormal   异常信息
     * @param recordTime 记录时间
     * @return 异常信息DTO
     */
    private VehicleAbnormalDTO convertAbnormal(Abnormal abnormal, Date recordTime, String vehicleName) {
        if (abnormal == null) {
            return null;
        }

        VehicleAbnormalDTO vehicleAbnormal = new VehicleAbnormalDTO();
        vehicleAbnormal.setVehicleName(vehicleName);
        vehicleAbnormal.setReportTime(recordTime.getTime());
        vehicleAbnormal.setStartTime(abnormal.getStartTime());
        vehicleAbnormal.setEndTime(abnormal.getEndTime());
        vehicleAbnormal.setModuleName(abnormal.getModuleName());
        vehicleAbnormal.setErrorCode(abnormal.getErrorCode());
        vehicleAbnormal.setErrorLevel(abnormal.getErrorLevel().name());
        vehicleAbnormal.setErrorMsg(abnormal.getErrorMessage());
        return vehicleAbnormal;
    }
}