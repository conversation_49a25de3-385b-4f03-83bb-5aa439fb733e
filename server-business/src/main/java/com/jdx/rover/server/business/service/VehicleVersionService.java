package com.jdx.rover.server.business.service;

import cn.hutool.core.collection.CollectionUtil;
import com.jdx.rover.common.utils.ParameterCheckUtility;
import com.jdx.rover.server.api.domain.dto.guardian.ModuleVersionInfoDTO;
import com.jdx.rover.server.api.domain.enums.OtaMoudleEnum;
import com.jdx.rover.server.api.domain.guardian.VehicleVersionInfoEntity;
import com.jdx.rover.server.repository.redis.guardian.VehicleVersionInfoRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 车端版本服务
 */
@Service
@Slf4j
public class VehicleVersionService {

  @Autowired
  private VehicleVersionInfoRepository vehicleVersionInfoRepository;

  public Map<String, ModuleVersionInfoDTO> getVehicleVersion(String vehicleName) {
    ParameterCheckUtility.checkNotNullNorEmptyAfterTrimming(vehicleName, "vehicleName");
    Map<String, VehicleVersionInfoEntity> result = vehicleVersionInfoRepository.getByVehicleName(vehicleName);
    Map<String, ModuleVersionInfoDTO> versionResult = new HashMap<>();
    if (CollectionUtil.isEmpty(result)) {
      return versionResult;
    }
    for (Map.Entry entry : result.entrySet()) {
      ModuleVersionInfoDTO moduleVersionInfoDto = new ModuleVersionInfoDTO();
      VehicleVersionInfoEntity entity = (VehicleVersionInfoEntity)entry.getValue();
      moduleVersionInfoDto.setModuleName(entity.getModuleName());
      moduleVersionInfoDto.setSerialId(entity.getSerialId());
      moduleVersionInfoDto.setCurVersion(entity.getCurVersion());
      moduleVersionInfoDto.setOtaVersion(entity.getOtaVersion());
      versionResult.put(entity.getModuleName(), moduleVersionInfoDto);
    }
    // 地图以compute单独上报为准
    VehicleVersionInfoEntity vehicleVersionInfoEntity = result.get("map-compute");
    if (!Objects.isNull(vehicleVersionInfoEntity)) {
      ModuleVersionInfoDTO moduleVersionInfoDTO = new ModuleVersionInfoDTO();
      moduleVersionInfoDTO.setModuleName(OtaMoudleEnum.MAP.getName());
      moduleVersionInfoDTO.setCurVersion(vehicleVersionInfoEntity.getCurVersion());
      versionResult.put(OtaMoudleEnum.MAP.getName(), moduleVersionInfoDTO);
    }
    return versionResult;
  }

}
