/*
 * Copyright (c) 2021 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.server.business.manager;

import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.server.api.domain.dto.guardian.PassNoSignalIntersectionDTO;
import com.jdx.rover.server.api.domain.dto.guardian.VehiclePncInfoDTO;
import com.jdx.rover.server.api.domain.dto.guardian.VehiclePncNavigationRoutingPositionDTO;
import com.jdx.rover.server.api.domain.dto.guardian.VehiclePncNavigationStopInfoDTO;
import com.jdx.rover.server.api.domain.dto.guardian.VehiclePncTrafficLightInfoDTO;
import com.jdx.rover.server.business.config.ProducerTopicProperties;
import com.jdx.rover.server.domain.vo.guardian.GuardianInfoAddVo;
import com.jdx.rover.server.domain.vo.guardian.NavigationRoutingPosition;
import com.jdx.rover.server.domain.vo.guardian.NavigationStopResult;
import com.jdx.rover.server.domain.vo.guardian.PassNoSignalIntersectionVO;
import com.jdx.rover.server.domain.vo.guardian.TrafficLightInfo;
import com.jdx.rover.server.repository.jmq.JmqProducerManager;
import com.jdx.rover.server.repository.redis.guardian.VehiclePncRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 拆分导航信息,并写入redis,对外发送kafka消息
 *
 * <AUTHOR>
 * @date  2022-01-01
 */
@Service
@Slf4j
public class GuardianPncManager {
  @Autowired
  private KafkaTemplate<String, String> kafkaTemplate;
  @Autowired
  private VehiclePncRepository vehiclePncRepository;
  /**
   * jmq消息发送producer
   */
  @Autowired
  private JmqProducerManager jmqProducerManager;

  /**
   * 生产主题配置类
   */
  @Autowired
  private ProducerTopicProperties producerTopicProperties;

  public VehiclePncInfoDTO handle(GuardianInfoAddVo vo) {
    VehiclePncInfoDTO vehiclePncInfoDTO = new VehiclePncInfoDTO();
    vehiclePncInfoDTO.setRecordTime(vo.getReportTime());
    vehiclePncInfoDTO.setVehicleName(vo.getVehicleName());
    // PNC当前任务类型
    vehiclePncInfoDTO.setType(vo.getNavigationPncStatusRequest().getPlanningStatus().getType());
    // 路径重新规划标志
    vehiclePncInfoDTO.setNaviRefreshId(vo.getNavigationPncStatusRequest().getRoutingStatus().getNaviRefreshId());

    // 红绿灯
    vehiclePncInfoDTO.setIntersectionStatus(vo.getNavigationPncStatusRequest().getPlanningStatus().getIntersectionStatus());
    List<VehiclePncTrafficLightInfoDTO> trafficLightInfoList = getVehiclePncTrafficLightInfoEntityList(vo);
    vehiclePncInfoDTO.setTrafficLightInfo(trafficLightInfoList);

    // 规划总距离
    vehiclePncInfoDTO.setGlobalMileage(vo.getNavigationPncStatusRequest().getRoutingStatus().getGlobalMileage());

    List<VehiclePncNavigationStopInfoDTO> navigationStopList = getVehiclePncNavigationStopInfoEntityList(vo);
    vehiclePncInfoDTO.setNavigationStop(navigationStopList);

    // 通过无信号路口
    PassNoSignalIntersectionVO noSignal = vo.getNavigationPncStatusRequest().getPlanningStatus().getPassNoSignalIntersection();
    if (noSignal != null) {
      PassNoSignalIntersectionDTO passNoSignalIntersectionDTO = new PassNoSignalIntersectionDTO();
      passNoSignalIntersectionDTO.setStatus(noSignal.getStatus());
      passNoSignalIntersectionDTO.setGroupLaneIdList(noSignal.getGroupLaneIdList());
      vehiclePncInfoDTO.setPassNoSignalIntersection(passNoSignalIntersectionDTO);
    }

    VehiclePncInfoDTO redisObj = vehiclePncRepository.get(vehiclePncInfoDTO.getVehicleName());
    if (!Objects.equals(vehiclePncInfoDTO, redisObj)) {
      vehiclePncRepository.save(vehiclePncInfoDTO);
      String vehiclePncInfoEntityStr = JsonUtils.writeValueAsString(vehiclePncInfoDTO);
      jmqProducerManager.sendOrdered(producerTopicProperties.getServerGuardianPnc(), vehiclePncInfoEntityStr, vo.getVehicleName());
    }
    return vehiclePncInfoDTO;
  }

  /**
   * 规划停靠点信息
   *
   * @param vo
   * @return
   */
  private List<VehiclePncNavigationStopInfoDTO> getVehiclePncNavigationStopInfoEntityList(GuardianInfoAddVo vo) {
    List<VehiclePncNavigationStopInfoDTO> result = new ArrayList<>();
    if (vo.getNavigationPncStatusRequest() == null || vo.getNavigationPncStatusRequest().getRoutingStatus() == null
        || CollectionUtils.isEmpty(vo.getNavigationPncStatusRequest().getRoutingStatus().getStopResultList())) {
      return result;
    }

    List<NavigationStopResult> voNavigationStopResultList = vo.getNavigationPncStatusRequest().getRoutingStatus().getStopResultList();
    if (CollectionUtils.isEmpty(voNavigationStopResultList)) {
      return result;
    }
    for (NavigationStopResult source : voNavigationStopResultList) {
      VehiclePncNavigationStopInfoDTO destination = new VehiclePncNavigationStopInfoDTO();
      destination.setStopId(source.getStopId());
      destination.setRoutingMileage(source.getRoutingMileage());
      List<VehiclePncNavigationRoutingPositionDTO> routingList = new ArrayList<>();
      List<NavigationRoutingPosition> routingPositionList = source.getRoutingPositionList();
      if (!CollectionUtils.isEmpty(routingPositionList)) {
        for (NavigationRoutingPosition navigationRoutingPosition : routingPositionList) {
          VehiclePncNavigationRoutingPositionDTO entity = new VehiclePncNavigationRoutingPositionDTO();
          entity.setLat(navigationRoutingPosition.getLatitude());
          entity.setLon(navigationRoutingPosition.getLongitude());
          routingList.add(entity);
        }
      }
      destination.setRouting(routingList);
      result.add(destination);
    }
    return result;
  }

  /**
   * 红绿灯信息
   *
   * @param vo
   * @return
   */
  private List<VehiclePncTrafficLightInfoDTO> getVehiclePncTrafficLightInfoEntityList(GuardianInfoAddVo vo) {
    List<VehiclePncTrafficLightInfoDTO> trafficLightInfoList = new ArrayList<>();
    List<TrafficLightInfo> voTrafficLightInfoList = vo.getNavigationPncStatusRequest().getPlanningStatus().getTrafficLightInfo();
    if (CollectionUtils.isEmpty(voTrafficLightInfoList)) {
      return trafficLightInfoList;
    }
    for (TrafficLightInfo source : voTrafficLightInfoList) {
      VehiclePncTrafficLightInfoDTO destination = new VehiclePncTrafficLightInfoDTO();
      destination.setId(source.getId());
      destination.setTrafficLightSource(source.getTrafficLightSourceEnum().getSource());
      trafficLightInfoList.add(destination);
    }
    return trafficLightInfoList;
  }
}
