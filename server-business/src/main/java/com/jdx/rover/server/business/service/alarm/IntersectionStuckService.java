package com.jdx.rover.server.business.service.alarm;

import cn.hutool.extra.spring.SpringUtil;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleAlarmEventDTO;
import com.jdx.rover.server.api.domain.dto.guardian.VehiclePncInfoDTO;
import com.jdx.rover.server.api.domain.enums.guardian.AlarmTypeEnum;
import com.jdx.rover.server.business.manager.VehicleStateInfoManager;
import com.jdx.rover.server.business.manager.localview.PlanningManager;
import com.jdx.rover.server.domain.enums.HandleAlarmEnum;
import com.jdx.rover.server.repository.redis.guardian.VehiclePncRepository;
import jdx.rover.guardian.dto.LocalView;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.queue.CircularFifoQueue;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 路口遇阻
 *
 * <AUTHOR>
 */
@Slf4j
public class IntersectionStuckService {

    private static final int MAX_INDEX = 90;
    private static final int MAX_SIZE = MAX_INDEX + 1;
    private static final Map<String, CircularFifoQueue<IntersectionStuckService.VehicleLocation>> vehicleLocationQueueMap = new HashMap<>();
    private static final Map<String, LogicStrategyEnum> vehicleLogicStrategyMap = new HashMap<>();

    /**
     * 缓存车辆位置
     */
    public static IntersectionStuckService.VehicleLocation cacheLocation(LocalView.LocalViewDTO dto) {
        String vehicleName = dto.getAuthInfo().getVehicleId();
        CircularFifoQueue<IntersectionStuckService.VehicleLocation> vehicleLocationQueue = vehicleLocationQueueMap.get(vehicleName);
        if (Objects.isNull(vehicleLocationQueue)) {
            vehicleLocationQueue = new CircularFifoQueue<>(MAX_SIZE);
            vehicleLocationQueueMap.put(vehicleName, vehicleLocationQueue);
        }
        return addVehicleLocation(dto, vehicleLocationQueue);
    }

    /**
     * 清空缓存的车辆位置
     */
    public static void clearLocation(LocalView.LocalViewDTO dto) {
        String vehicleName = dto.getAuthInfo().getVehicleId();
        CircularFifoQueue<IntersectionStuckService.VehicleLocation> vehicleLocationQueue = vehicleLocationQueueMap.get(vehicleName);
        if (Objects.isNull(vehicleLocationQueue) || vehicleLocationQueue.isEmpty()) {
            return;
        }
        vehicleLocationQueue.clear();
    }

    /**
     * 是否路口遇阻
     *
     * @param dto
     * @return
     */
    public static HandleAlarmEnum isReportAlarm(LocalView.LocalViewDTO dto, Map<String, VehicleAlarmEventDTO> alarmMap) {
        String vehicleName = dto.getAuthInfo().getVehicleId();
        if (StringUtils.isBlank(vehicleName)) {
            return HandleAlarmEnum.IGNORE;
        }
        VehicleAlarmEventDTO alarmDb = alarmMap.get(AlarmTypeEnum.VEHICLE_STOP_INTERSECTION_STUCK.getValue());
        LocalView.PlanningDecision decision = dto.getPlanning().getDecision();

        // 只有路口内或者看灯时才处理或距离停止线超过两米
        if (!decision.getIsInIntersection() && !PlanningManager.isStopTrafficLight(dto) && !isStopLineWithDistance(2, dto)) {
            // 车辆不再路口区域且未触发红绿灯停车,清空缓存位置队列,消除报警;清除上次逻辑策略缓存
            clearLocation(dto);
            vehicleLogicStrategyMap.remove(vehicleName);
            if (!Objects.isNull(alarmDb)) {
                log.info("车辆不再路口区域且未触发红绿灯停车且不在停止线2m范围内,消除路口遇阻报警!车辆={},告警={}", vehicleName, JsonUtils.writeValueAsString(alarmDb));
                return HandleAlarmEnum.REMOVE;
            }
            return HandleAlarmEnum.IGNORE;
        }
        cacheLocation(dto);
        CircularFifoQueue<IntersectionStuckService.VehicleLocation> vehicleLocationQueue = vehicleLocationQueueMap.get(vehicleName);
        if (!Objects.isNull(alarmDb)) {
            // 3秒内车辆行驶超1米，消失
            if (vehicleLocationQueue.size() > 3 && computeDistance(vehicleLocationQueue, 3, vehicleLocationQueue.size()) > 1) {
                log.info("3秒内车辆行驶超1米,消失路口遇阻报警!车辆={},告警={}", vehicleName, JsonUtils.writeValueAsString(alarmDb));
                return HandleAlarmEnum.REMOVE;
            }
        } else if (isAddAlarm(vehicleLocationQueue, dto, vehicleName)) {
            return HandleAlarmEnum.ADD;
        }
        return HandleAlarmEnum.IGNORE;
    }

    private static boolean isAddAlarm(CircularFifoQueue<IntersectionStuckService.VehicleLocation> vehicleLocationQueue, LocalView.LocalViewDTO dto, String vehicleName) {
        VehiclePncInfoDTO vehiclePncInfoDTO = SpringUtil.getBean(VehiclePncRepository.class).get(vehicleName);
        if (!VehicleStateInfoManager.hasPlaning(vehiclePncInfoDTO)) {
            //没有PNC路线时,清除缓存,保持原告警状态
            clearLocation(dto);
            vehicleLogicStrategyMap.remove(vehicleName);
            return false;
        }
        LogicStrategyEnum logicStrategy = getLogicStrategy(dto);

        //与上次逻辑不一致,重新计时
        if (vehicleLogicStrategyMap.containsKey(vehicleName) && !Objects.equals(logicStrategy, vehicleLogicStrategyMap.get(vehicleName))) {
            vehicleLocationQueue.clear();
            vehicleLogicStrategyMap.remove(vehicleName);
            return false;
        }
        vehicleLogicStrategyMap.put(vehicleName, logicStrategy);
        if (logicStrategy == LogicStrategyEnum.STRATEGY_ONE) {
            if (vehicleLocationQueue.size() > 8) {
                // 持续8s车速为0，新增
                if (isSpeedEqualZero(vehicleLocationQueue, 8, vehicleLocationQueue.size())) {
                    log.info("持续8s车速为0,新增路口遇阻报警!车辆={}", vehicleName);
                    return true;
                }
            }

            if (vehicleLocationQueue.size() <= 15) {
                return false;
            }
            // 持续15s行驶距离小于0.25m，新增
            if (computeDistance(vehicleLocationQueue, 15, vehicleLocationQueue.size()) < 0.25) {
                log.info("持续15s行驶距离小于0.25m,新增路口遇阻报警!车辆={}", vehicleName);
                return true;
            }
            // 持续15S车速0.2m/s以下，新增
            if (isSpeedLess(vehicleLocationQueue, 15, 0.2, vehicleLocationQueue.size())) {
                log.info("持续15s车速0.2m/s以下,新增路口遇阻报警!车辆={}", vehicleName);
                return true;
            }
        } else if (logicStrategy == LogicStrategyEnum.STRATEGY_TWO) {
            if (!vehicleLocationQueue.isAtFullCapacity()) {
                // 队列不满,暂不处理
                log.info("初始队列不满,暂不处理路口遇阻!车辆={},大小={}", vehicleName, vehicleLocationQueue.size());
                return false;
            }
            // 持续90s行驶距离小于1.5m，新增
            if (computeDistance(vehicleLocationQueue, 90, vehicleLocationQueue.size()) < 1.5) {
                log.info("持续90s行驶距离小于1.5m,新增路口遇阻报警!车辆={}", vehicleName);
                return true;
            }
            // 持续90S车速0.5m/s以下，新增
            if (isSpeedLess(vehicleLocationQueue, 90, 0.5, vehicleLocationQueue.size())) {
                log.info("持续90S车速0.5m/s以下,新增路口遇阻报警!车辆={}", vehicleName);
                return true;
            }

        }
        return false;
    }

    /**
     * 获取计算策略
     * @param dto
     * @return
     */
    public static LogicStrategyEnum getLogicStrategy(LocalView.LocalViewDTO dto) {
        List<LocalView.PlanningTrafficLight> interestedLightList = dto.getPlanning().getInterestedLightList();
        if (interestedLightList.isEmpty()) {
            if (dto.getTrafficLightList().isEmpty()){
                return LogicStrategyEnum.STRATEGY_ONE;
            }
            //planning感兴趣灯为空 取第一个信号灯
            LocalView.TrafficLight trafficLight = dto.getTrafficLightList().get(0);
            if (Objects.equals(trafficLight.getLight(), LocalView.TrafficLight.Light.RED) || Objects.equals(trafficLight.getLight(), LocalView.TrafficLight.Light.YELLOW)) {
                return LogicStrategyEnum.STRATEGY_TWO;
            } else {
                return LogicStrategyEnum.STRATEGY_ONE;
            }
        }
        //获取planning感兴趣灯
        LocalView.PlanningTrafficLight planningTrafficLight = interestedLightList.get(0);
        for (LocalView.TrafficLight trafficLight : dto.getTrafficLightList()) {
            if (Objects.equals(planningTrafficLight.getId(), trafficLight.getId())) {
                if (Objects.equals(trafficLight.getLight(), LocalView.TrafficLight.Light.RED) || Objects.equals(trafficLight.getLight(), LocalView.TrafficLight.Light.YELLOW)) {
                    return LogicStrategyEnum.STRATEGY_TWO;
                } else {
                    return LogicStrategyEnum.STRATEGY_ONE;
                }
            }
        }
        return LogicStrategyEnum.STRATEGY_ONE;
    }

    /**
     * 判断最近的停止线是否在指定距离内
     * @param distinct
     */
    public static boolean isStopLineWithDistance(int distinct, LocalView.LocalViewDTO dto){
        if (dto.getStopLineList().isEmpty()) {
            return false;
        }
        double pointX = dto.getOriginCenter().getX();
        double pointY = dto.getOriginCenter().getY();
        for (LocalView.StopLine stopLine : dto.getStopLineList()) {
            List<LocalView.Point3D> pointList = stopLine.getPointList();
            double segmentStartX = pointList.get(0).getX();
            double segmentStartY = pointList.get(0).getY();

            double segmentEndX = pointList.get(pointList.size() - 1).getX();
            double segmentEndY = pointList.get(pointList.size() - 1).getY();
            if (distinct > distanceToSegment(pointX, pointY, segmentStartX, segmentStartY, segmentEndX, segmentEndY)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 计算点到线段的距离
     * @param x0
     * @param y0
     * @param x1
     * @param y1
     * @param x2
     * @param y2
     * @return
     */
    public static double distanceToSegment(double x0, double y0, double x1, double y1, double x2, double y2) {
        double dx = x2 - x1;
        double dy = y2 - y1;

        if (dx == 0 && dy == 0) {
            // A 和 B 其实是同一个点
            return Math.sqrt(Math.pow(x0 - x1, 2) + Math.pow(y0 - y1, 2));
        }

        double t = ((x0 - x1) * dx + (y0 - y1) * dy) / (dx * dx + dy * dy);

        if (t < 0) {
            // 最近点是 A
            return Math.sqrt(Math.pow(x0 - x1, 2) + Math.pow(y0 - y1, 2));
        } else if (t > 1) {
            // 最近点是 B
            return Math.sqrt(Math.pow(x0 - x2, 2) + Math.pow(y0 - y2, 2));
        } else {
            // 最近点是线段上的点 Q
            double qx = x1 + t * dx;
            double qy = y1 + t * dy;
            return Math.sqrt(Math.pow(x0 - qx, 2) + Math.pow(y0 - qy, 2));
        }
    }

    /**
     * 是否非正常看灯
     *
     * @param dto
     * @return
     */
    private static boolean isAbnormalLight(LocalView.LocalViewDTO dto) {
        // 路口没有看灯
        if (CollectionUtils.isEmpty(dto.getPlanning().getInterestedLightList())) {
            return true;
        }
        boolean isAbnormalLight = true;
        for (LocalView.TrafficLight trafficLight : dto.getTrafficLightList()) {
            // 不是正常红绿黄灯,直接忽略
            if (!Objects.equals(trafficLight.getLight(), LocalView.TrafficLight.Light.RED)
                    && !Objects.equals(trafficLight.getLight(), LocalView.TrafficLight.Light.GREEN)
                    && !Objects.equals(trafficLight.getLight(), LocalView.TrafficLight.Light.YELLOW)) {
                continue;
            }
            for (LocalView.PlanningTrafficLight planningTrafficLight : dto.getPlanning().getInterestedLightList()) {
                if (Objects.equals(planningTrafficLight.getId(), trafficLight.getId())) {
                    isAbnormalLight = false;
                    break;
                }
            }
        }
        return isAbnormalLight;
    }

    /**
     * 计算指定时间段内距离
     *
     * @param timeSecond
     * @return
     */
    public static double computeDistance(CircularFifoQueue<IntersectionStuckService.VehicleLocation> vehicleLocationQueue, int timeSecond, int index) {
        double distance = 0;
        for (int i = 1; i < timeSecond; i++) {
            IntersectionStuckService.VehicleLocation start = vehicleLocationQueue.get(index - i - 1);
            IntersectionStuckService.VehicleLocation end = vehicleLocationQueue.get(index - i);
            // 近似计算,提高效率,经纬度相减,绝对值求和
            distance += Math.abs(end.getLon() - start.getLon());
            distance += Math.abs(end.getLat() - start.getLat());
        }
        return distance;
    }

    /**
     * 计算指定时间段内速度是否小于某个值
     *
     * @param timeSecond
     * @return
     */
    public static boolean isSpeedLess(CircularFifoQueue<IntersectionStuckService.VehicleLocation> vehicleLocationQueue, int timeSecond, double speed, int index) {
        for (int i = 0; i < timeSecond; i++) {
            IntersectionStuckService.VehicleLocation start = vehicleLocationQueue.get(index - i - 1);
            if (Math.abs(start.getSpeed()) > speed) {
                return false;
            }
        }
        return true;
    }

    /**
     * 计算指定时间段内速度是否为0
     *
     * @param timeSecond
     * @return
     */
    public static boolean isSpeedEqualZero(CircularFifoQueue<IntersectionStuckService.VehicleLocation> vehicleLocationQueue, int timeSecond, int index) {
        for (int i = 0; i < timeSecond; i++) {
            IntersectionStuckService.VehicleLocation start = vehicleLocationQueue.get(index - i - 1);
            if (start.getSpeed() != 0) {
                return false;
            }
        }
        return true;
    }

    private static IntersectionStuckService.VehicleLocation addVehicleLocation(LocalView.LocalViewDTO dto, CircularFifoQueue<IntersectionStuckService.VehicleLocation> vehicleLocationQueue) {
        double x = dto.getOrigin().getX() + dto.getCar().getOrientation().getX();
        double y = dto.getOrigin().getY() + dto.getCar().getOrientation().getY();
        IntersectionStuckService.VehicleLocation vehicleLocation = new IntersectionStuckService.VehicleLocation(y, x, dto.getCar().getVelocity(), dto.getTimestamp());
        vehicleLocationQueue.add(vehicleLocation);
        return vehicleLocation;
    }

    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class VehicleLocation {

        /**
         * 纬度
         */
        private Double lat;

        /**
         * 经度
         */
        private Double lon;

        /**
         * 速度
         */
        private Double speed;

        /**
         * 记录时间,单位纳秒ns
         */
        private long recordTime;
    }

    public enum LogicStrategyEnum {
        STRATEGY_ONE,
        STRATEGY_TWO
    }
}
