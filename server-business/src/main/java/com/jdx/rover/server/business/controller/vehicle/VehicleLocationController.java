/*
 * Copyright (c) 2022-2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.server.business.controller.vehicle;

import com.jdx.rover.server.api.domain.constants.KafkaTopicConstant;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleLocationDTO;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleRealtimeInfoDTO;
import com.jdx.rover.server.repository.redis.RedissonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@Slf4j
public class VehicleLocationController {
  /**
   * 获取车辆实时位置
   *
   * @param vehicleName
   */
  @GetMapping(value = "/server/business/vehicle/location/{vehicleName}")
  public VehicleLocationDTO getVehicleLocation(@PathVariable("vehicleName") String vehicleName) {
    String redisKey = KafkaTopicConstant.SERVER_GUARDIAN_REALTIME + "_" + vehicleName;
    VehicleRealtimeInfoDTO dto = RedissonUtils.getObject(redisKey);
    if (dto == null) {
      return null;
    }
    VehicleLocationDTO result = new VehicleLocationDTO();
    result.setVehicleName(vehicleName);
    result.setLat(dto.getLat());
    result.setLon(dto.getLon());
    result.setHeading(dto.getHeading());
    result.setRecordTime(dto.getRecordTime());
    return result;
  }
}
