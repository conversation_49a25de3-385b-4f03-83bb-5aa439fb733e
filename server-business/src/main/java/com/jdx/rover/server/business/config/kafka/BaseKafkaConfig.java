package com.jdx.rover.server.business.config.kafka;

import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.kafka.KafkaProperties;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.config.KafkaListenerContainerFactory;
import org.springframework.kafka.core.ConsumerFactory;
import org.springframework.kafka.core.DefaultKafkaConsumerFactory;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.core.ProducerFactory;
import org.springframework.kafka.listener.ConcurrentMessageListenerContainer;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
public abstract class BaseKafkaConfig {
  @Autowired
  private KafkaProperties kafkaProperties;

  public KafkaTemplate<String, String> kafkaTemplate(Class serializerClass) {
    return new KafkaTemplate<>(producerFactory(serializerClass));
  }

  public KafkaListenerContainerFactory<ConcurrentMessageListenerContainer<Integer, String>> kafkaContainerFactory(Class deserializerClass) {
    ConcurrentKafkaListenerContainerFactory<Integer, String> factory = new ConcurrentKafkaListenerContainerFactory<>();
    factory.setConsumerFactory(consumerFactory(deserializerClass));
    // 设置为批量监听
    factory.setBatchListener(true);
    Integer concurrency = kafkaProperties.getListener().getConcurrency();
    if (concurrency != null && concurrency.intValue() > 1) {
      factory.setConcurrency(concurrency);
    }
    return factory;
  }

  private ProducerFactory<String, String> producerFactory(Class serializerClass) {
    return new DefaultKafkaProducerFactory<>(producerConfigs(serializerClass));
  }

  public ConsumerFactory<Integer, String> consumerFactory(Class deserializerClass) {
    return new DefaultKafkaConsumerFactory<>(consumerConfigs(deserializerClass));
  }

  private Map<String, Object> producerConfigs(Class serializerClass) {
    Map<String, Object> props = new HashMap<>();
    props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, kafkaProperties.getBootstrapServers());
    props.put(ProducerConfig.RETRIES_CONFIG, 5);
    props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
    props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, serializerClass);
    return props;
  }

  private Map<String, Object> consumerConfigs(Class deserializerClass) {
    Map<String, Object> props = new HashMap<>();
    props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, kafkaProperties.getBootstrapServers());
    props.put(ConsumerConfig.GROUP_ID_CONFIG, kafkaProperties.getConsumer().getGroupId());
    props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
    props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, deserializerClass);
    return props;
  }
}
