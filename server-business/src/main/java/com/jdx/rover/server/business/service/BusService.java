package com.jdx.rover.server.business.service;

import com.google.protobuf.Descriptors;
import com.google.protobuf.MessageOrBuilder;
import com.jdx.rover.server.api.domain.constants.KafkaTopicConstant;
import com.jdx.rover.server.business.config.ProducerTopicProperties;
import com.jdx.rover.server.common.utils.proto.ProtoUtils;
import com.jdx.rover.server.domain.enums.LogConfigEnum;
import com.jdx.rover.server.repository.jmq.JmqProducerManager;
import jdx.rover.dto.proto.BusDto;
import jdx.rover.dto.proto.BusHeader;
import jdx.rover.dto.proto.BusMonitor;
import jdx.rover.dto.proto.BusObu;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 车辆上报总线数据service
 *
 * <AUTHOR>
 * @date  2022-01-01
 */
@Service
@Slf4j
public class BusService {

  @Autowired
  private KafkaTemplate<String, String> kafkaTemplate;
  /**
   * jmq消息发送producer
   */
  @Autowired
  private JmqProducerManager jmqProducerManager;

  /**
   * 生产主题配置类
   */
  @Autowired
  private ProducerTopicProperties producerTopicProperties;

  /**
   * 处理消息
   */
  public void receive(byte[] message) {
    try {
      BusDto.BusDTO busDTO = BusDto.BusDTO.parseFrom(message);
      receive(busDTO);
    } catch (Exception e) {
      log.error("解析bus数据失败!{}", null, e);
    }
  }

  /**
   * 处理消息
   */
  private void receive(BusDto.BusDTO busDTO) {
    BusHeader.RequestHeader busHeader = busDTO.getRequestHeader();
    List<BusHeader.MessageTypeRequest> requestList = busHeader.getMessageTypeRequestList();
    for (BusHeader.MessageTypeRequest messageTypeRequest : requestList) {
      // 处理OBU数据
      sendObu(busDTO, messageTypeRequest);
//      sendMonitorFeature(busDTO, messageTypeRequest);
    }

    if (!LogConfigEnum.BUS.isValue()) {
      // 日志开关关闭,直接返回,不继续打印日志
      return;
    }
    for (BusHeader.MessageTypeRequest messageTypeRequest : requestList) {
      // 打印非心跳日志
      if (!BusHeader.MessageType.HEARTBEAT.equals(messageTypeRequest.getMessageType())) {
        log.info("收到bus2数据={}", ProtoUtils.protoToJson(busDTO));
        break;
      }
    }
  }

  /**
   * 发送OBU数据
   *
   * @param busDTO
   * @param messageTypeRequest
   */
  private void sendObu(BusDto.BusDTO busDTO, BusHeader.MessageTypeRequest messageTypeRequest) {
    if (!BusHeader.MessageType.OBU.equals(messageTypeRequest.getMessageType())) {
      return;
    }
    // 处理OBU数据
    BusObu.Obu obuData = busDTO.getObu();
    int number = obuData.getDataCase().getNumber();
    Descriptors.FieldDescriptor fieldDescriptor = obuData.getDescriptorForType().findFieldByNumber(number);
    MessageOrBuilder messageOrBuilder = (MessageOrBuilder) obuData.getField(fieldDescriptor);
    String jsonStr = ProtoUtils.protoToJson(messageOrBuilder);
    jmqProducerManager.sendOrdered(producerTopicProperties.getServerBusObu(), jsonStr, busDTO.getRequestHeader().getVehicleName());
    kafkaTemplate.send(KafkaTopicConstant.SERVER_BUS_OBU, busDTO.getRequestHeader().getVehicleName(), jsonStr);
  }

  /**
   * 发送特征数据
   *
   * @param busDTO
   * @param messageTypeRequest
   */
  private void sendMonitorFeature(BusDto.BusDTO busDTO, BusHeader.MessageTypeRequest messageTypeRequest) {
    BusMonitor.Monitor monitor = busDTO.getMonitor();
    if (monitor == null || !BusHeader.MessageType.MONITOR.equals(messageTypeRequest.getMessageType())
            || !BusMonitor.MonitorType.FEATURE_MSG.equals(monitor.getMonitorType())) {
      return;
    }
    // 处理特征数据
    BusMonitor.FeatureData featureData = monitor.getFeatureData();
    if (featureData != null) {
      kafkaTemplate.send(KafkaTopicConstant.SERVER_BUS_MONITOR_FEATURE, busDTO.getRequestHeader().getVehicleName()
              , ProtoUtils.protoToJson(featureData));
    }
  }
}