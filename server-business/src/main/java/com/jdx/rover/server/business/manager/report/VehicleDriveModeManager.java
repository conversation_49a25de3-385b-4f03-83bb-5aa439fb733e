/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/
package com.jdx.rover.server.business.manager.report;

import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.server.api.domain.dto.report.drivemode.VehicleDriveModeDTO;
import com.jdx.rover.server.business.config.ProducerTopicProperties;
import com.jdx.rover.server.repository.jmq.JmqProducerManager;
import jdx.rover.report.dto.proto.ReportDto;
import jdx.rover.report.dto.proto.ReportHeader;
import jdx.rover.report.statistics.proto.ReportStatistics;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 收到驾驶模式信息,对外发送kafka消息
 *
 * <AUTHOR>
 * @version 1.0
 * @date  2022-01-01
 */
@Service
@Slf4j
public class VehicleDriveModeManager {
    @Autowired
    private KafkaTemplate<String, String> kafkaTemplate;
    /**
     * jmq消息发送producer
     */
    @Autowired
    private JmqProducerManager jmqProducerManager;

    /**
     * 生产主题配置类
     */
    @Autowired
    private ProducerTopicProperties producerTopicProperties;

    /**
     * 处理启动信息
     */
    public void handle(ReportDto.ReportDTO reportDTO, ReportHeader.MessageTypeRequest messageTypeRequest) {
        if (!ReportHeader.MessageType.DRIVING_MODE.equals(messageTypeRequest.getMessageType())) {
            return;
        }
        if (CollectionUtils.isEmpty(reportDTO.getReportStatisticsData().getDrivingModeDataList())) {
            return;
        }
        // 处理驾驶模式数据
        for (ReportStatistics.DrivingModeData drivingModeData : reportDTO.getReportStatisticsData().getDrivingModeDataList()) {
            VehicleDriveModeDTO vehicleDriveMode = new VehicleDriveModeDTO();

            vehicleDriveMode.setVehicleName(reportDTO.getRequestHeader().getVehicleName());
            vehicleDriveMode.setRuntimeUuid(reportDTO.getReportStatisticsData().getRuntimeUuid());
            vehicleDriveMode.setReportTime(reportDTO.getRequestHeader().getRequestTime());
            vehicleDriveMode.setDriveMode(drivingModeData.getDrivingMode().name());
            vehicleDriveMode.setStartTime(drivingModeData.getStartTime());
            vehicleDriveMode.setEndTime(drivingModeData.getEndTime());
            List<VehicleDriveModeDTO.Point> pointList = new ArrayList<>(drivingModeData.getPointListCount());
            drivingModeData.getPointListList().forEach(item -> {
                VehicleDriveModeDTO.Point dto = new VehicleDriveModeDTO.Point();
                dto.setLongitude(item.getLongitude());
                dto.setLatitude(item.getLatitude());
                pointList.add(dto);
            });
            vehicleDriveMode.setPointList(pointList);
            String jsonStr = JsonUtils.writeValueAsString(vehicleDriveMode);
            jmqProducerManager.sendOrdered(producerTopicProperties.getServerReportDriveMode(), jsonStr, reportDTO.getRequestHeader().getVehicleName());
            log.info("发送驾驶模式信息={}", jsonStr);
        }

    }
}
