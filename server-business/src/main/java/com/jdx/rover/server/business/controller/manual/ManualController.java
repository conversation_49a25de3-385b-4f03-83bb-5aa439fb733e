/*
 * Copyright (c) 2023 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.server.business.controller.manual;

import com.google.common.collect.Lists;
import com.jdx.rover.common.utils.login.LoginUtils;
import com.jdx.rover.server.api.domain.dto.report.abnormal.VehicleAbnormalDTO;
import com.jdx.rover.server.business.manager.metadata.MetadataVehicleApiKeyManager;
import com.jdx.rover.server.business.manager.report.VehicleAlarmManager;
import com.jdx.rover.server.repository.redis.guardian.ReportAbnormalRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 人工操作
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@RestController
@RequestMapping(value = "/server/business/manual")
@Slf4j
public class ManualController {
    private final ReportAbnormalRepository reportAbnormalRepository;
    private final VehicleAlarmManager vehicleAlarmManager;
    private final MetadataVehicleApiKeyManager metadataVehicleApiKeyManager;


    /**
     * 人工删除异常
     */
    @GetMapping(value = "/delete/abnormal/{vehicleName}/{errorCode}/{moduleName}")
    public String deleteAbnormal(@PathVariable("vehicleName") String vehicleName
            , @PathVariable("errorCode") String errorCode, @PathVariable("moduleName") String moduleName) {
        log.info("{}人工删除车辆[{}]异常,模块[{}],错误码[{}]", LoginUtils.getUsername(""), vehicleName, moduleName, errorCode);
        reportAbnormalRepository.remove(vehicleName, errorCode, moduleName);

        List<VehicleAbnormalDTO> abnormalList = Lists.newArrayList(reportAbnormalRepository.get(vehicleName).values());
        vehicleAlarmManager.saveAlarmRedis(abnormalList);

        log.info("{}人工删除车辆[{}]异常,模块[{}],错误码[{}]成功", LoginUtils.getUsername(""), vehicleName, moduleName, errorCode);
        return "delete abnormal success!";
    }

    /**
     * 重新发送告警
     */
    @GetMapping(value = "/repeat/send/alarm/{vehicleName}")
    public String repeatSendAlarm(@PathVariable("vehicleName") String vehicleName) {
        log.info("{}人工重新发送车辆[{}]告警", LoginUtils.getUsername(""), vehicleName);

        vehicleAlarmManager.sendKafka(vehicleName);

        log.info("{}人工重新发送车辆[{}]告警成功", LoginUtils.getUsername(""), vehicleName);
        return "repeat send alarm success!";
    }

    /**
     * 初始化API密钥缓存map
     */
    @GetMapping(value = "/initVehicleApiKeyMap")
    public String initVehicleApiKeyMap() {
        log.info("{}人工初始化API密钥缓存map", LoginUtils.getUsername(""));

        metadataVehicleApiKeyManager.initVehicleApiKeyMap();

        log.info("{}人工初始化API密钥缓存map成功", LoginUtils.getUsername(""));
        return "initVehicleApiKeyMap success!";
    }
}
