/*
 * Copyright (c) 2022-2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.server.business.controller.vehicle;

import com.jdx.rover.common.utils.ParameterCheckUtility;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleAlarmEventDTO;
import com.jdx.rover.server.business.manager.report.VehicleAlarmManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * 增加删除告警
 *
 * <AUTHOR>
 */
@RestController
@Slf4j
@RequiredArgsConstructor
public class VehicleAlarmController {
    private final VehicleAlarmManager vehicleAlarmManager;

    /**
     * 新增告警
     *
     * @param vehicleName 车辆名称
     * @return
     */
    @PostMapping(value = "/server/business/vehicle/alarm/add/{vehicleName}")
    public VehicleAlarmEventDTO addAlarm(@PathVariable("vehicleName") String vehicleName
            , @RequestBody VehicleAlarmEventDTO alarm) {
        ParameterCheckUtility.checkNotNull(alarm.getType(), "getType");
        ParameterCheckUtility.checkNotNull(alarm.getReportTime(), "getReportTime");
        ParameterCheckUtility.checkNotNull(alarm.getErrorCode(), "getErrorCode");
        ParameterCheckUtility.checkNotNull(alarm.getErrorLevel(), "getErrorLevel");
        vehicleAlarmManager.addAlarm(vehicleName, alarm);
        log.info("addAlarm vehicleName={}, result={}", vehicleName, alarm);
        return alarm;
    }

    /**
     * 删除告警
     *
     * @param vehicleName 车辆名称
     * @return
     */
    @PostMapping(value = "/server/business/vehicle/alarm/remove/{vehicleName}/{alarmType}")
    public String removeAlarm(@PathVariable("vehicleName") String vehicleName
            , @PathVariable("alarmType") String alarmType) {
        vehicleAlarmManager.removeAlarm(vehicleName, alarmType);
        log.info("removeAlarm vehicleName={}, result={}", vehicleName, alarmType);
        return alarmType;
    }
}
