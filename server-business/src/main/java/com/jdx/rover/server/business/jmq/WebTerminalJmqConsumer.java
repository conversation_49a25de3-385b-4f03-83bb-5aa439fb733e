/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */
package com.jdx.rover.server.business.jmq;

import com.jd.jmq.client.consumer.MessageListener;
import com.jd.jmq.common.message.Message;
import com.jdx.rover.server.business.service.WebTerminalService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * web terminal消息接收Listener
 *
 * <AUTHOR>
 * @date 2024-12-17
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class WebTerminalJmqConsumer implements MessageListener {
    /**
     * web terminal 消息处理
     */
    private final WebTerminalService webTerminalService;

    /**
     * 接收到消息后的处理
     *
     * @param messageList 消息列表
     */
    @Override
    public void onMessage(List<Message> messageList) {
        webTerminalService.onMessage(messageList);
    }
}
