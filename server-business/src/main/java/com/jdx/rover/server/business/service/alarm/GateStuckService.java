package com.jdx.rover.server.business.service.alarm;

import cn.hutool.extra.spring.SpringUtil;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleAlarmEventDTO;
import com.jdx.rover.server.api.domain.dto.guardian.VehiclePncInfoDTO;
import com.jdx.rover.server.api.domain.enums.guardian.AlarmTypeEnum;
import com.jdx.rover.server.business.manager.VehicleStateInfoManager;
import com.jdx.rover.server.domain.enums.HandleAlarmEnum;
import com.jdx.rover.server.repository.redis.guardian.VehiclePncRepository;
import jdx.rover.guardian.dto.LocalView;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.queue.CircularFifoQueue;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 过门遇阻
 * <AUTHOR>
 */
@Slf4j
public class GateStuckService {


    private static final int MAX_INDEX = 30;
    private static final int MAX_SIZE = MAX_INDEX + 1;
    private static final Map<String, CircularFifoQueue<VehicleStopTimeoutService.VehicleLocation>> vehicleLocationQueueMap = new HashMap<>();
    /**
     * 是否路口遇阻
     *
     * @param dto
     * @return
     */
    public static boolean isReportAlarm(LocalView.LocalViewDTO dto) {
        LocalView.PlanningDecision decision = dto.getPlanning().getDecision();
        // 只有路口内才处理
        if (!decision.getIsInFrontOfGate()) {
            return false;
        }
        // 停车事件小于30s,不报警
        if (decision.getStopTime() < 10) {
            return false;
        }
        return true;
    }

//
//    /**
//     * 缓存车辆位置
//     */
//    public static VehicleStopTimeoutService.VehicleLocation cacheLocation(LocalView.LocalViewDTO dto) {
//        String vehicleName = dto.getAuthInfo().getVehicleId();
//        CircularFifoQueue<VehicleStopTimeoutService.VehicleLocation> vehicleLocationQueue = vehicleLocationQueueMap.get(vehicleName);
//        if (Objects.isNull(vehicleLocationQueue)) {
//            vehicleLocationQueue = new CircularFifoQueue<>(MAX_SIZE);
//            vehicleLocationQueueMap.put(vehicleName, vehicleLocationQueue);
//        }
//        return addVehicleLocation(dto, vehicleLocationQueue);
//    }
//
//    /**
//     * 清空缓存的车辆位置
//     */
//    public static void clearLocation(LocalView.LocalViewDTO dto) {
//        String vehicleName = dto.getAuthInfo().getVehicleId();
//        CircularFifoQueue<VehicleStopTimeoutService.VehicleLocation> vehicleLocationQueue = vehicleLocationQueueMap.get(vehicleName);
//        if (Objects.isNull(vehicleLocationQueue) || vehicleLocationQueue.isEmpty()) {
//            return;
//        }
//        vehicleLocationQueue.clear();
//    }
//
//    /**
//     * 是否过杆遇阻
//     * 上报条件
//     * 仅当PNC有规划路线
//     * <p>
//     * 持续15s行驶距离小于0.25m
//     * 持续15S车速0.2m/s以下
//     * 持续8s车速为0
//     * <p>
//     * 消失条件
//     * 3秒内车辆行驶超1米，消失
//     * 当车辆pnc规划路线消失时，消失
//     *
//     * @param dto
//     * @return -1 删除报警 0暂不处理 1新增报警
//     */
//    public static HandleAlarmEnum isReportAlarm(LocalView.LocalViewDTO dto, Map<String, VehicleAlarmEventDTO> alarmMap) {
//        String vehicleName = dto.getAuthInfo().getVehicleId();
//        if (StringUtils.isBlank(vehicleName)) {
//            return HandleAlarmEnum.IGNORE;
//        }
//        VehicleAlarmEventDTO alarmDb = alarmMap.get(AlarmTypeEnum.GATE_STUCK.getValue());
//        // 只有路口内才处理
//        LocalView.PlanningDecision decision = dto.getPlanning().getDecision();
//        if (!decision.getIsInFrontOfGate()) {
//            if (!Objects.isNull(alarmDb)) {
//                log.info("车辆离开Gate区域,消除过杆遇阻报警!车辆={},告警={}", vehicleName, JsonUtils.writeValueAsString(alarmDb));
//                return HandleAlarmEnum.REMOVE;
//            }
//            return HandleAlarmEnum.IGNORE;
//        }
//        //是否有规划路线
//        VehiclePncInfoDTO vehiclePncInfoDTO = SpringUtil.getBean(VehiclePncRepository.class).get(vehicleName);
//        if (!VehicleStateInfoManager.hasPlaning(vehiclePncInfoDTO)) {
//            // 当车辆pnc规划路线消失时,清空缓存位置队列，消除报警
//            clearLocation(dto);
//            if (!Objects.isNull(alarmDb)) {
//                log.info("车辆pnc规划路线消失时,消除过杆遇阻报警!车辆={},告警={}", vehicleName, JsonUtils.writeValueAsString(alarmDb));
//                return HandleAlarmEnum.REMOVE;
//            }
//            return HandleAlarmEnum.IGNORE;
//        }
////        LocalView.BehaviorDecision crossGateBehaviorDecision = null;
////        List<LocalView.BehaviorDecision> decisionList = dto.getPlanning().getDecision().getBehaviorDecisionList().getBehaviorDecisionList();
////        if (!CollectionUtils.isEmpty(decisionList)) {
////            for (LocalView.BehaviorDecision behaviorDecision : decisionList) {
////                if (behaviorDecision == null || !Objects.equals(behaviorDecision.getType().name(), LocalView.BehaviorType.CROSS_GATE.name())) {
////                    continue;
////                }
////                crossGateBehaviorDecision = behaviorDecision;
////            }
////        }
//        cacheLocation(dto);
//        CircularFifoQueue<VehicleStopTimeoutService.VehicleLocation> vehicleLocationQueue = vehicleLocationQueueMap.get(vehicleName);
//        if (!Objects.isNull(alarmDb)) {
//            // 3秒内车辆行驶超1米，消失
//            if (vehicleLocationQueue.size() > 3 && computeDistance(vehicleLocationQueue, 3, vehicleLocationQueue.size()) > 1) {
//                log.info("3秒内车辆行驶超1米,消失过杆遇阻报警!车辆={},告警={}", vehicleName, JsonUtils.writeValueAsString(alarmDb));
//                return HandleAlarmEnum.REMOVE;
//            }
//        } else if (isAddAlarm(dto, vehicleLocationQueue, vehicleName)) {
//            return HandleAlarmEnum.ADD;
//        }
//        return HandleAlarmEnum.IGNORE;
//    }
//
//    public static boolean isAddAlarm(LocalView.LocalViewDTO dto, CircularFifoQueue<VehicleStopTimeoutService.VehicleLocation> vehicleLocationQueue, String vehicleName) {
//        boolean isHaveCrossGateStop = false;
//        List<LocalView.BehaviorDecision> decisionList = dto.getPlanning().getDecision().getBehaviorDecisionList().getBehaviorDecisionList();
//        if (!CollectionUtils.isEmpty(decisionList)) {
//            for (LocalView.BehaviorDecision behaviorDecision : decisionList) {
//                if (behaviorDecision != null && Objects.equals(behaviorDecision.getState().name(), LocalView.BehaviorState.CROSS_GATE_HARD.name())) {
//                    return true;
//                }
//                if (behaviorDecision != null && Objects.equals(behaviorDecision.getState().name(), LocalView.BehaviorState.CROSS_GATE_STOP.name())) {
//                    isHaveCrossGateStop = true;
//                }
//            }
//        }
//        if (isHaveCrossGateStop) {
//            if (!vehicleLocationQueue.isAtFullCapacity()) {
//                // 队列不满,暂不处理
//                log.info("初始队列不满,暂不处理过杆遇阻!车辆={},大小={}", vehicleName, vehicleLocationQueue.size());
//                return false;
//            }
//            // 持续30S车速0.2m/s以下，新增
//            if (isSpeedLess(vehicleLocationQueue, 30, 0.2, vehicleLocationQueue.size())) {
//                log.info("持续30s车速0.2m/s以下,新增过杆遇阻报警!车辆={}", vehicleName);
//                return true;
//            }
//            // 持续30s行驶距离小于0.25m，新增
//            if (computeDistance(vehicleLocationQueue, 30, vehicleLocationQueue.size()) < 0.25) {
//                log.info("持续30s行驶距离小于0.25m,新增过杆遇阻报警!车辆={}", vehicleName);
//                return true;
//            }
//        } else {
//            if (vehicleLocationQueue.size() <= 10) {
//                // 队列不满10,暂不处理
//                log.info("初始队列不满,暂不处理过杆遇阻!车辆={},大小={}", vehicleName, vehicleLocationQueue.size());
//                return false;
//            }
//            // 持续10S车速0.2m/s以下，新增
//            if (isSpeedLess(vehicleLocationQueue, 10, 0.2, vehicleLocationQueue.size())) {
//                log.info("持续10s车速0.2m/s以下,新增过杆遇阻报警!车辆={}", vehicleName);
//                return true;
//            }
//            // 持续10s行驶距离小于0.25m，新增
//            if (computeDistance(vehicleLocationQueue, 10, vehicleLocationQueue.size()) < 0.25) {
//                log.info("持续10s行驶距离小于0.25m,新增过杆遇阻报警!车辆={}", vehicleName);
//                return true;
//            }
//        }
//        return false;
//    }
//
//    /**
//     * 计算指定时间段内距离
//     *
//     * @param timeSecond
//     * @return
//     */
//    private static double computeDistance(CircularFifoQueue<VehicleStopTimeoutService.VehicleLocation> vehicleLocationQueue, int timeSecond, int index) {
//        double distance = 0;
//        for (int i = 1; i < timeSecond; i++) {
//            VehicleStopTimeoutService.VehicleLocation start = vehicleLocationQueue.get(index - i - 1);
//            VehicleStopTimeoutService.VehicleLocation end = vehicleLocationQueue.get(index - i);
//            // 近似计算,提高效率,经纬度相减,绝对值求和
//            distance += Math.abs(end.getLon() - start.getLon());
//            distance += Math.abs(end.getLat() - start.getLat());
//        }
//        return distance;
//    }
//
//    /**
//     * 计算指定时间段内速度是否小于某个值
//     *
//     * @param timeSecond
//     * @return
//     */
//    private static boolean isSpeedLess(CircularFifoQueue<VehicleStopTimeoutService.VehicleLocation> vehicleLocationQueue, int timeSecond, double speed, int index) {
//        for (int i = 0; i < timeSecond; i++) {
//            VehicleStopTimeoutService.VehicleLocation start = vehicleLocationQueue.get(index - i - 1);
//            if (Math.abs(start.getSpeed()) > speed) {
//                return false;
//            }
//        }
//        return true;
//    }
//
//    private static VehicleStopTimeoutService.VehicleLocation addVehicleLocation(LocalView.LocalViewDTO dto, CircularFifoQueue<VehicleStopTimeoutService.VehicleLocation> vehicleLocationQueue) {
//        double x = dto.getOrigin().getX() + dto.getCar().getOrientation().getX();
//        double y = dto.getOrigin().getY() + dto.getCar().getOrientation().getY();
//        VehicleStopTimeoutService.VehicleLocation vehicleLocation = new VehicleStopTimeoutService.VehicleLocation(y, x, dto.getCar().getVelocity(), dto.getTimestamp());
//        vehicleLocationQueue.add(vehicleLocation);
//        return vehicleLocation;
//    }
//
//    @AllArgsConstructor
//    @NoArgsConstructor
//    @Data
//    public static class VehicleLocation {
//
//        /**
//         * 纬度
//         */
//        private Double lat;
//
//        /**
//         * 经度
//         */
//        private Double lon;
//
//        /**
//         * 速度
//         */
//        private Double speed;
//
//        /**
//         * 记录时间,单位纳秒ns
//         */
//        private long recordTime;
//    }
}
