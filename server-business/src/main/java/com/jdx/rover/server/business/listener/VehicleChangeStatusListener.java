/*
 * Copyright (c) 2023 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.server.business.listener;

import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.server.api.domain.constants.KafkaTopicConstant;
import com.jdx.rover.server.api.domain.dto.status.ChangeStatusDTO;
import com.jdx.rover.server.business.manager.VehicleLocalizationManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 地图模块版本监听器.
 * 接收kafka发送的地图模块版本信息，并存入redis中.
 *
 * <AUTHOR>
 * @date 2024-03-16
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class VehicleChangeStatusListener {
    /**
     * 车辆定位处理服务
     */
    private final VehicleLocalizationManager vehicleLocalizationManager;

    /**
     * 消费kafka消息
     * @param messageList
     */
//    @KafkaListener(topics = {KafkaTopicConstant.SERVER_VEHICLE_CHANGE_STATUS})
    public void onMessage(List<String> messageList) {
        if (CollectionUtils.isEmpty(messageList)) {
            return;
        }

        messageList.forEach(msg -> {
            try {
                handleOneMessage(msg);
            } catch (Exception e) {
                log.error("解析{}数据失败: {}", KafkaTopicConstant.SERVER_VEHICLE_CHANGE_STATUS, msg, e);
            }
        });
    }

    /**
     * 处理消息
     * @param message
     */
    public void handleOneMessage(String message) {
        log.info("端云处理车辆状态变更{}", message);
        ChangeStatusDTO dto = JsonUtils.readValue(message, ChangeStatusDTO.class);
        ChangeStatusDTO.ChangeTypeEnum changeTypeEnum = ChangeStatusDTO.ChangeTypeEnum.of(dto.getChangeType());
        switch (changeTypeEnum) {
            case SCENE_SIGNAL: {
                vehicleLocalizationManager.handleSceneSignalAlarm(dto);
                break;
            }
            default:
                log.info("未匹配的消息{}", dto);
        }
    }


}