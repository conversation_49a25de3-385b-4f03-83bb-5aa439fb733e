package com.jdx.rover.server.business.job;

import com.jdx.rover.server.business.manager.VehicleAbnormalManager;
import com.wangyin.schedule.client.job.ScheduleContext;
import com.wangyin.schedule.client.job.ScheduleFlowTask;
import com.wangyin.schedule.client.job.TaskResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class StopReportExceptionJob implements ScheduleFlowTask {

  @Autowired
  private VehicleAbnormalManager vehicleAbnormalManager;

  @Override
  public TaskResult doTask(ScheduleContext scheduleContext) {
    vehicleAbnormalManager.handStopReportException();
//    log.info("处理关闭停止上报异常完成!");
    return TaskResult.success();
  }
}