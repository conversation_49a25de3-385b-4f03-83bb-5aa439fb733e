package com.jdx.rover.server.business.config.kafka;

import org.apache.kafka.common.serialization.ByteArrayDeserializer;
import org.apache.kafka.common.serialization.ByteArraySerializer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.config.KafkaListenerContainerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.listener.ConcurrentMessageListenerContainer;

/**
 * <AUTHOR>
 */
@Configuration
public class ByteKafkaConfig extends BaseKafkaConfig {
  @Bean
  public KafkaTemplate<String, String> byteKafkaTemplate() {
    return super.kafkaTemplate(ByteArraySerializer.class);
  }

  @Bean
  public KafkaListenerContainerFactory<ConcurrentMessageListenerContainer<Integer, String>> byteKafkaContainerFactory() {
    return super.kafkaContainerFactory(ByteArrayDeserializer.class);
  }
}
