/*
 * Copyright (c) 2023 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.server.business.listener;

import com.jdx.rover.server.business.service.ReportService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 车辆上报总线数据Kafka监听
 *
 * <AUTHOR>
 * @date  2022-01-01
 */
@Service
@Slf4j
public class ReportKafkaListener {

  @Autowired
  private ReportService reportService;

//  @KafkaListener(topics = {"server_report_origin"}, containerFactory = "secondKafkaContainerFactory")
  public void onMessage(List<byte[]> messageList) {
    if (CollectionUtils.isEmpty(messageList)) {
      return;
    }
    for (byte[] message : messageList) {
      reportService.receive(message);
    }
  }
}