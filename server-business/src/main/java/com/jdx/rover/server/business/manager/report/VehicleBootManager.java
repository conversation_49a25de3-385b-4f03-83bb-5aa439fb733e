/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/
package com.jdx.rover.server.business.manager.report;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.google.common.collect.Iterables;
import com.google.common.collect.Lists;
import com.jdx.rover.common.constant.NumberConstant;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleAlarmEventDTO;
import com.jdx.rover.server.api.domain.dto.report.abnormal.VehicleAbnormalDTO;
import com.jdx.rover.server.api.domain.dto.report.boot.ModuleBootDTO;
import com.jdx.rover.server.api.domain.dto.report.boot.ModuleBootDetailDTO;
import com.jdx.rover.server.api.domain.dto.report.boot.NodeBootDTO;
import com.jdx.rover.server.api.domain.dto.report.boot.VehicleBootDTO;
import com.jdx.rover.server.api.domain.enums.guardian.AlarmTypeEnum;
import com.jdx.rover.server.api.domain.enums.report.BootStatusEnum;
import com.jdx.rover.server.business.config.ProducerTopicProperties;
import com.jdx.rover.server.common.utils.proto.ProtoUtils;
import com.jdx.rover.server.domain.dto.vehicle.ReportBootDTO;
import com.jdx.rover.server.domain.enums.GuardianErrorEnum;
import com.jdx.rover.server.domain.enums.GuardianErrorLevelEnum;
import com.jdx.rover.server.repository.jmq.JmqProducerManager;
import com.jdx.rover.server.repository.redis.guardian.ReportAbnormalRepository;
import com.jdx.rover.server.repository.redis.guardian.ReportAlarmRepository;
import com.jdx.rover.server.repository.redis.report.ReportBootRepository;
import com.jdx.rover.server.repository.shadow.ShadowEventEnum;
import com.jdx.rover.server.repository.shadow.ShadowEventUtils;
import com.jdx.rover.shadow.boot.starter.util.EventUtil;
import com.jdx.rover.shadow.boot.starter.vo.EventRecordVO;
import jdx.rover.report.dto.proto.ReportDto;
import jdx.rover.report.dto.proto.ReportHeader;
import jdx.rover.report.statistics.proto.ReportStatistics;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 收到启动信息,对外发送kafka消息
 *
 * <AUTHOR>
 * @version 1.0
 * @date  2022-01-01
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class VehicleBootManager {
    private static final long START_UP_TIMEOUT = 5 * 60 * 1000;

    private final KafkaTemplate<String, String> kafkaTemplate;
    private final ReportAlarmRepository reportAlarmRepository;
    private final VehicleAlarmManager vehicleAlarmManager;
    private final ReportBootRepository reportBootRepository;
    private final ReportAbnormalRepository reportAbnormalRepository;

    private final VehicleBootAlarmManager vehicleBootAlarmManager;
    /**
     * jmq消息发送producer
     */
    private final JmqProducerManager jmqProducerManager;

    /**
     * 生产主题配置类
     */
    private final ProducerTopicProperties producerTopicProperties;

    /**
     * 处理启动信息
     */
    public void handle(ReportDto.ReportDTO reportDTO, ReportHeader.MessageTypeRequest messageTypeRequest) {
        if (!ReportHeader.MessageType.BOOT.equals(messageTypeRequest.getMessageType())) {
            return;
        }
        String vehicleName = reportDTO.getRequestHeader().getVehicleName();
        ReportBootDTO reportBootDb = reportBootRepository.get(vehicleName);
        // 处理启动数据
        ReportStatistics.VehicleBoot vehicleBootProto = reportDTO.getReportStatisticsData().getVehicleBoot();
        if (!Objects.isNull(reportBootDb) && Objects.equals(reportBootDb.getRuntimeUuid(), reportDTO.getReportStatisticsData().getRuntimeUuid()) &&
                Objects.equals(reportBootDb.getBootId(), vehicleBootProto.getBootId()) &&
                reportBootDb.getReportTime() >= reportDTO.getRequestHeader().getRequestTime()) {
            log.info("redis时间大于等于当前数据时间,忽略处理!reportBootDb={},reportDTO={}", reportBootDb, ProtoUtils.protoToJson(reportDTO));
            return;
        }

        VehicleBootDTO vehicleBoot = new VehicleBootDTO();
        vehicleBoot.setVehicleName(vehicleName);
        vehicleBoot.setRuntimeUuid(reportDTO.getReportStatisticsData().getRuntimeUuid());
        vehicleBoot.setBootId(vehicleBootProto.getBootId());
        vehicleBoot.setStartTime(vehicleBootProto.getStartTime());
        vehicleBoot.setEndTime(vehicleBootProto.getEndTime());
        vehicleBoot.setAllBootStatus(vehicleBootProto.getAllBootStatus().name());
        vehicleBoot.setReportTime(reportDTO.getRequestHeader().getRequestTime());
        List<NodeBootDTO> nodeBootList = new ArrayList<>(vehicleBootProto.getNodeBootCount());
        vehicleBootProto.getNodeBootList().forEach(node -> {
            NodeBootDTO dto = new NodeBootDTO();
            dto.setNodeName(node.getNodeName());
            dto.setIp(node.getIp());
            dto.setStartTime(node.getStartTime());
            dto.setEndTime(node.getEndTime());
            dto.setBootStatus(node.getBootStatus().name());

            // 设置节点启动模块
            List<ModuleBootDTO> moduleBootList = new ArrayList<>(node.getModuleBootCount());
            node.getModuleBootList().forEach(module -> {
                ModuleBootDTO moduleDTO = buildModuleBootDTO(module);
                moduleBootList.add(moduleDTO);
            });
            dto.setModuleBootList(moduleBootList);

            // 设置硬件启动模块
            ModuleBootDTO hardwareModule = buildModuleBootDTO(node.getHardwareModule());
            dto.setHardwareModule(hardwareModule);

            nodeBootList.add(dto);
        });
        vehicleBoot.setNodeBootList(nodeBootList);
        String jsonStr = JsonUtils.writeValueAsString(vehicleBoot);
        jmqProducerManager.sendOrdered(producerTopicProperties.getServerReportBoot(), jsonStr, vehicleName);

        saveRedis(vehicleBoot, reportBootDb);

        removeLastBootAbnormal(vehicleBoot, reportBootDb);

        removeLastBootAlarm(vehicleBoot, reportBootDb);

        vehicleBootAlarmManager.sendAlarmAdapter(vehicleBoot);

        sendShadowEvent(vehicleBoot);

        log.info("发送启动信息={}", jsonStr);
    }

    /**
     * 删除上次启动的异常
     *
     * @param vehicleBoot
     * @param reportBootDb
     */
    private void removeLastBootAbnormal(VehicleBootDTO vehicleBoot, ReportBootDTO reportBootDb) {
        if (!isRestart(vehicleBoot, reportBootDb)) {
            return;
        }
        String vehicleName = vehicleBoot.getVehicleName();
        Map<String, VehicleAbnormalDTO> abnormalMapDb = reportAbnormalRepository.get(vehicleName);
        if (CollectionUtils.isEmpty(abnormalMapDb)) {
            return;
        }

        List<VehicleAbnormalDTO> removeList = new ArrayList<>(abnormalMapDb.size());
        long startBootTime = vehicleBoot.getStartTime();
        for (VehicleAbnormalDTO abnormalDb : abnormalMapDb.values()) {
            if (abnormalDb.getReportTime() > startBootTime) {
                continue;
            }
            VehicleAbnormalDTO vehicleAbnormal = createVehicleAbnormalDTO(startBootTime, abnormalDb);
            removeList.add(vehicleAbnormal);
        }
        if (CollectionUtils.isEmpty(removeList)) {
            return;
        }
        reportAbnormalRepository.remove(removeList);
        String jsonStr = JsonUtils.writeValueAsString(removeList);
        jmqProducerManager.sendOrdered(producerTopicProperties.getServerReportAbnormal(), jsonStr, vehicleName);
        log.info("清除上次开机异常信息={}", jsonStr);
    }

    /**
     * 删除上次启动的告警
     *
     * @param vehicleBoot
     * @param reportBootDb
     */
    private void removeLastBootAlarm(VehicleBootDTO vehicleBoot, ReportBootDTO reportBootDb) {
        if (!isRestart(vehicleBoot, reportBootDb)) {
            return;
        }
        String vehicleName = vehicleBoot.getVehicleName();
        Map<String, VehicleAlarmEventDTO> alarmMapDb = reportAlarmRepository.get(vehicleName);
        if (CollectionUtils.isEmpty(alarmMapDb)) {
            return;
        }

        List<String> removeList = new ArrayList<>(alarmMapDb.size());
        for (VehicleAlarmEventDTO alarmDb : alarmMapDb.values()) {
            if (Objects.equals(alarmDb.getRuntimeUuid(), vehicleBoot.getRuntimeUuid().toString())) {
                continue;
            }
            removeList.add(alarmDb.getType());
        }
        if (CollectionUtils.isEmpty(removeList)) {
            return;
        }
        reportAlarmRepository.remove(vehicleName, removeList);
        String jsonStr = JsonUtils.writeValueAsString(removeList);
        vehicleAlarmManager.sendKafka(vehicleName);
        log.info("清除上次开机告警信息={}", jsonStr);
    }

    /**
     * 创建异常对象
     *
     * @param startBootTime
     * @param abnormalDb
     * @return
     */
    private static VehicleAbnormalDTO createVehicleAbnormalDTO(long startBootTime, VehicleAbnormalDTO abnormalDb) {
        VehicleAbnormalDTO vehicleAbnormal = new VehicleAbnormalDTO();
        vehicleAbnormal.setVehicleName(abnormalDb.getVehicleName());
        vehicleAbnormal.setRuntimeUuid(abnormalDb.getRuntimeUuid());
        vehicleAbnormal.setReportTime(abnormalDb.getReportTime());
        vehicleAbnormal.setStartTime(abnormalDb.getStartTime());
        vehicleAbnormal.setEndTime(startBootTime);
        vehicleAbnormal.setModuleName(abnormalDb.getModuleName());
        vehicleAbnormal.setErrorCode(String.valueOf(abnormalDb.getErrorCode()));
        vehicleAbnormal.setErrorLevel(abnormalDb.getErrorLevel());
        vehicleAbnormal.setErrorMsg(abnormalDb.getErrorMsg());
        return vehicleAbnormal;
    }

    /**
     * 构建模块启动对象
     *
     * @param moduleBoot
     * @return
     */
    private ModuleBootDTO buildModuleBootDTO(ReportStatistics.ModuleBoot moduleBoot) {
        if (Objects.isNull(moduleBoot) || moduleBoot.getModuleBootDetailCount() == 0) {
            return null;
        }
        ModuleBootDTO moduleDTO = new ModuleBootDTO();
        moduleDTO.setBootCount(moduleBoot.getBootCount());
        List<ModuleBootDetailDTO> moduleBootDetailList = new ArrayList<>(moduleBoot.getModuleBootDetailCount());
        moduleBoot.getModuleBootDetailList().forEach(moduleBootDetail -> {
            ModuleBootDetailDTO moduleBootDetailDTO = new ModuleBootDetailDTO();
            moduleBootDetailDTO.setName(moduleBootDetail.getName());
            moduleBootDetailDTO.setStartTime(moduleBootDetail.getStartTime());
            moduleBootDetailDTO.setEndTime(moduleBootDetail.getEndTime());
            moduleBootDetailDTO.setBootStatus(moduleBootDetail.getBootStatus().name());
            moduleBootDetailDTO.setErrorCode(moduleBootDetail.getErrorCode());
            moduleBootDetailDTO.setErrorMsg(moduleBootDetail.getErrorMsg());
            moduleBootDetailList.add(moduleBootDetailDTO);
        });
        moduleDTO.setModuleBootDetailList(moduleBootDetailList);
        return moduleDTO;
    }

    /**
     * 发送影子事件
     *
     * @param vehicleBoot
     */
    private void sendShadowEvent(VehicleBootDTO vehicleBoot) {
        ShadowEventEnum shadowEventEnum = getShadowEventEnum(vehicleBoot);
        if (Objects.isNull(shadowEventEnum)) {
            return;
        }
        EventRecordVO eventRecordVO = ShadowEventUtils.buildCommonEvent(vehicleBoot.getVehicleName(), shadowEventEnum);
        List<String> paramList = new ArrayList<>(3);
        String startTime = DateUtil.format(new Date(vehicleBoot.getStartTime() / NumberConstant.MILLION), DatePattern.NORM_DATETIME_MS_FORMAT);
        paramList.add(vehicleBoot.getVehicleName());
        paramList.add(startTime);
        if (!Objects.isNull(vehicleBoot.getEndTime())) {
            long useTime = (vehicleBoot.getEndTime() - vehicleBoot.getStartTime()) / 1_000_000_000;
            paramList.add(useTime + "");
        }
        if (Objects.equals(vehicleBoot.getAllBootStatus(), BootStatusEnum.START_FAILED.name())) {
            StringBuilder errorMsg = new StringBuilder();
            vehicleBoot.getNodeBootList().forEach(node -> {
                if (!Objects.equals(node.getBootStatus(), BootStatusEnum.START_FAILED.name())) {
                    return;
                }
                ModuleBootDTO moduleBoot = Iterables.getLast(node.getModuleBootList());
                for (int i = moduleBoot.getModuleBootDetailList().size() - 1; i > 0; i--) {
                    ModuleBootDetailDTO moduleBootDetailDTO = moduleBoot.getModuleBootDetailList().get(i);
                    if (Objects.equals(node.getBootStatus(), BootStatusEnum.START_FAILED.name())) {
                        if (errorMsg.length() > 0) {
                            errorMsg.append(",");
                        }
                        errorMsg.append("node:").append(node.getNodeName())
                                .append(",module:").append(moduleBootDetailDTO.getName())
                                .append(",errorCode:").append(moduleBootDetailDTO.getErrorCode())
                                .append(",errorMsg:").append(moduleBootDetailDTO.getErrorMsg());
                        break;
                    }
                }
            });
            paramList.add(errorMsg.toString());
        }
        eventRecordVO.setBodyArray(paramList.toArray(new String[0]));
        EventUtil.sendEvent(eventRecordVO);
    }

    /**
     * 获取影子事件事件分类
     *
     * @param vehicleBoot
     * @return
     */
    private ShadowEventEnum getShadowEventEnum(VehicleBootDTO vehicleBoot) {
        ShadowEventEnum shadowEventEnum = null;
        if (Objects.equals(vehicleBoot.getAllBootStatus(), BootStatusEnum.START_FAILED.name())) {
            if (vehicleBoot.getBootId() == 1) {
                shadowEventEnum = ShadowEventEnum.SERVER_VEHICLE_ROVER_BOOT_FAIL;
            } else {
                shadowEventEnum = ShadowEventEnum.SERVER_VEHICLE_ROVER_HALFWAY_BOOT_FAIL;
            }
        } else if (Objects.equals(vehicleBoot.getAllBootStatus(), BootStatusEnum.START_SUCCESS.name())) {
            if (vehicleBoot.getBootId() == 1) {
                shadowEventEnum = ShadowEventEnum.SERVER_VEHICLE_ROVER_BOOT_SUCCESS;
            } else {
                shadowEventEnum = ShadowEventEnum.SERVER_VEHICLE_ROVER_HALFWAY_BOOT_SUCCESS;
            }
        }
//        暂时不处理非成功失败事件
//        else {
//            if (vehicleBoot.getBootId() == 1) {
//                shadowEventEnum = ShadowEventEnum.SERVER_VEHICLE_ROVER_BOOT_START;
//            } else {
//                shadowEventEnum = ShadowEventEnum.SERVER_VEHICLE_ROVER_HALFWAY_BOOT_START;
//            }
//        }
        return shadowEventEnum;
    }

    private void sendAlarm(VehicleBootDTO vehicleBoot) {
        long startUpTimeout = vehicleBoot.getStartTime() / NumberConstant.MILLION + START_UP_TIMEOUT;
        String vehicleName = vehicleBoot.getVehicleName();
        Map<String, VehicleAlarmEventDTO> alarmMapDb = reportAlarmRepository.get(vehicleName);
        // 启动5分钟以内不报警
        if (Objects.equals(vehicleBoot.getAllBootStatus(), BootStatusEnum.STARTING.name())
                && startUpTimeout > System.currentTimeMillis()) {
            removeBootAlarm(vehicleName, alarmMapDb, GuardianErrorEnum.BOOT_TIMEOUT);
            removeBootAlarm(vehicleName, alarmMapDb, GuardianErrorEnum.BOOT_FAIL);
            return;
        }
        // 启动成功删除所有启动异常
        if (Objects.equals(vehicleBoot.getAllBootStatus(), BootStatusEnum.START_SUCCESS.name())) {
            removeBootAlarm(vehicleName, alarmMapDb, GuardianErrorEnum.BOOT_TIMEOUT);
            removeBootAlarm(vehicleName, alarmMapDb, GuardianErrorEnum.BOOT_FAIL);
            if (!CollectionUtils.isEmpty(alarmMapDb)) {
                vehicleAlarmManager.sendKafka(vehicleName);
            }
            return;
        }

        if (Objects.equals(vehicleBoot.getAllBootStatus(), BootStatusEnum.START_FAILED.name())) {
            reportAlarmRepository.remove(vehicleName, GuardianErrorEnum.BOOT_TIMEOUT.getAlarmType());
            VehicleAlarmEventDTO vehicleAlarmEventDTO = new VehicleAlarmEventDTO();
            vehicleAlarmEventDTO.setType(AlarmTypeEnum.BOOT_FAIL.getValue());
            vehicleAlarmEventDTO.setReportTime(new Date(vehicleBoot.getReportTime() / NumberConstant.MILLION));
            vehicleAlarmEventDTO.setRuntimeUuid(String.valueOf(vehicleBoot.getRuntimeUuid()));
            vehicleBoot.getNodeBootList().forEach(node -> {
                ModuleBootDTO moduleBoot = Iterables.getLast(node.getModuleBootList());
                for (int i = moduleBoot.getModuleBootDetailList().size() - 1; i > 0; i--) {
                    ModuleBootDetailDTO moduleBootDetailDTO = moduleBoot.getModuleBootDetailList().get(i);
                    if (Objects.equals(moduleBootDetailDTO.getBootStatus(), BootStatusEnum.START_FAILED.name())) {
                        vehicleAlarmEventDTO.setErrorCode(moduleBootDetailDTO.getErrorCode() + "");
                        vehicleAlarmEventDTO.setErrorLevel(GuardianErrorLevelEnum.FATAL.getErrorLevel());
                        vehicleAlarmEventDTO.setErrorMessage(moduleBootDetailDTO.getErrorMsg());
                        vehicleAlarmEventDTO.setRuntimeUuid(String.valueOf(vehicleBoot.getRuntimeUuid()));
                        return;
                    }
                }
            });
            reportAlarmRepository.save(vehicleName, Lists.newArrayList(vehicleAlarmEventDTO));
        } else {
            VehicleAlarmEventDTO alarm = alarmMapDb.get(GuardianErrorEnum.BOOT_TIMEOUT.getAlarmType());
            if (alarm != null) {
                return;
            }
            VehicleAlarmEventDTO vehicleAlarmEventDTO = new VehicleAlarmEventDTO();
            vehicleAlarmEventDTO.setType(AlarmTypeEnum.BOOT_TIMEOUT.getValue());
            vehicleAlarmEventDTO.setReportTime(new Date(vehicleBoot.getReportTime() / NumberConstant.MILLION));
            vehicleAlarmEventDTO.setErrorCode(AlarmTypeEnum.BOOT_TIMEOUT.getValue());
            vehicleAlarmEventDTO.setErrorLevel(GuardianErrorLevelEnum.ERROR.getErrorLevel());
            vehicleAlarmEventDTO.setRuntimeUuid(String.valueOf(vehicleBoot.getRuntimeUuid()));
            reportAlarmRepository.save(vehicleName, Lists.newArrayList(vehicleAlarmEventDTO));
        }
        vehicleAlarmManager.sendKafka(vehicleName);
    }

    /**
     * 移除启动异常告警
     *
     * @param vehicleName
     * @param alarmMapDb
     * @param guardianErrorEnum
     */
    private void removeBootAlarm(String vehicleName, Map<String, VehicleAlarmEventDTO> alarmMapDb, GuardianErrorEnum guardianErrorEnum) {
        VehicleAlarmEventDTO alarm = alarmMapDb.get(guardianErrorEnum.getAlarmType());
        if (Objects.isNull(alarm)) {
            return;
        }
        reportAlarmRepository.remove(vehicleName, guardianErrorEnum.getAlarmType());
        vehicleAlarmManager.sendKafka(vehicleName);
    }

    /**
     * 保存redis
     *
     * @param vehicleBoot
     */
    private void saveRedis(VehicleBootDTO vehicleBoot, ReportBootDTO reportBootDb) {
        if (!isSaveRedis(vehicleBoot, reportBootDb)) {
            return;
        }
        ReportBootDTO reportBootDTO = new ReportBootDTO();
        BeanUtils.copyProperties(vehicleBoot, reportBootDTO);
        reportBootRepository.put(reportBootDTO);
    }

    /**
     * 是否保存redis
     *
     * @param vehicleBoot
     * @param reportBootDb
     * @return
     */
    private boolean isSaveRedis(VehicleBootDTO vehicleBoot, ReportBootDTO reportBootDb) {
        if (isPowerButtonBoot(vehicleBoot, reportBootDb)) {
            return true;
        }

        if (!Objects.equals(reportBootDb.getBootId(), vehicleBoot.getBootId())) {
            return true;
        }

        if (!Objects.equals(vehicleBoot.getAllBootStatus(), BootStatusEnum.STARTING.name())) {
            return true;
        }

        return false;
    }

    /**
     * 是否电源按钮启动
     *
     * @param vehicleBoot
     * @param reportBootDb
     * @return
     */
    private boolean isPowerButtonBoot(VehicleBootDTO vehicleBoot, ReportBootDTO reportBootDb) {
        if (reportBootDb == null) {
            return true;
        }

        if (!Objects.equals(reportBootDb.getRuntimeUuid(), vehicleBoot.getRuntimeUuid())) {
            return true;
        }
        return false;
    }

    /**
     * 是否重启
     *
     * @param vehicleBoot
     * @param reportBootDb
     * @return
     */
    private boolean isRestart(VehicleBootDTO vehicleBoot, ReportBootDTO reportBootDb) {
        if (isPowerButtonBoot(vehicleBoot, reportBootDb)) {
            return true;
        }

        if (!Objects.equals(reportBootDb.getBootId(), vehicleBoot.getBootId())) {
            return true;
        }

        return false;
    }
}
