/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/
package com.jdx.rover.server.business.manager;

import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.server.api.domain.dto.guardian.VehiclePowerDTO;
import com.jdx.rover.server.api.domain.guardian.VehiclePowerInfoEntity;
import com.jdx.rover.server.business.config.ProducerTopicProperties;
import com.jdx.rover.server.domain.vo.guardian.GuardianInfoAddVo;
import com.jdx.rover.server.repository.jmq.JmqProducerManager;
import com.jdx.rover.server.repository.redis.guardian.VehicleKafkaPowerRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;

/**
 * 车辆电量处理,每变化1%,对外发送kafka消息
 *
 * <AUTHOR> shiling
 * @version 1.0
 * @date  2022-01-01
 */
@Service
@Slf4j
public class VehiclePowerManager {
  @Autowired
  private VehicleKafkaPowerRepository vehicleKafkaPowerRepository;
  @Autowired
  private KafkaTemplate<String, String> kafkaTemplate;
  @Autowired
  private RedisTemplate<String, Object> redisTemplate;
  /**
   * jmq消息发送producer
   */
  @Autowired
  private JmqProducerManager jmqProducerManager;

  /**
   * 生产主题配置类
   */
  @Autowired
  private ProducerTopicProperties producerTopicProperties;

  /**
   * 处理电量信息
   *
   * @param guardianInfoAddVo guardian信息.
   */
  public void handle(GuardianInfoAddVo guardianInfoAddVo) {
    VehiclePowerInfoEntity powerDb = vehicleKafkaPowerRepository.getValue(guardianInfoAddVo.getVehicleName());
    Double power = guardianInfoAddVo.getVehicleStatusRequest().getPowerUsage();
    String chargeState = guardianInfoAddVo.getVehicleStatusRequest().getChargeState();
    if (power == null) {
      return;
    }
    if (powerDb != null && powerDb.getPower() != null && Math.abs(powerDb.getPower().doubleValue() - power.doubleValue()) < 1
        && StringUtils.equals(chargeState, powerDb.getChargeState())) {
      return;
    }
    VehiclePowerInfoEntity powerInfoEntity = new VehiclePowerInfoEntity();
    powerInfoEntity.setChargeState(chargeState);
    powerInfoEntity.setPower(power);
    vehicleKafkaPowerRepository.saveValue(guardianInfoAddVo.getVehicleName(), powerInfoEntity);
    VehiclePowerDTO vehiclePowerDTO = new VehiclePowerDTO();
    vehiclePowerDTO.setVehicleName(guardianInfoAddVo.getVehicleName());
    vehiclePowerDTO.setPower(power);
    vehiclePowerDTO.setRecordTime(guardianInfoAddVo.getReportTime());
    String vehiclePowerDTOStr = JsonUtils.writeValueAsString(vehiclePowerDTO);
    jmqProducerManager.sendOrdered(producerTopicProperties.getServerGuardianPower(), vehiclePowerDTOStr, guardianInfoAddVo.getVehicleName());
    log.info("车辆电量变化消息{}", vehiclePowerDTOStr);
  }
}
