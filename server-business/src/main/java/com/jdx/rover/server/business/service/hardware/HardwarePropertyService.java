/*
 * Copyright (c) 2023 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.server.business.service.hardware;

import com.fasterxml.jackson.core.type.TypeReference;
import com.jd.jmq.common.message.Message;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.hardware.info.HardwareInfo;
import com.jdx.rover.server.api.domain.dto.mqtt.MqttToJmqDTO;
import com.jdx.rover.server.business.manager.hardware.HardwareInfoManager;
import com.jdx.rover.server.business.manager.hardware.HardwareStatusManager;
import com.jdx.rover.server.common.utils.proto.ProtoUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 车辆硬件属性数据服务,负责接收和分发硬件信息数据到相应的管理器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025-08-25
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class HardwarePropertyService {
    private final HardwareInfoManager hardwareInfoManager;
    private final HardwareStatusManager hardwareStatusManager;

    /**
     * 接收并处理JMQ消息
     *
     * @param message JMQ消息
     */
    public void receive(Message message) {
        try {
            MqttToJmqDTO<byte[]> jmqTopicDTO = JsonUtils.readByteValue(message.getByteBody(), new TypeReference<>() {
            });
            if (Objects.isNull(jmqTopicDTO) || Objects.isNull(jmqTopicDTO.getData())) {
                log.warn("接收到空的JMQ消息");
                return;
            }
            HardwareInfo hardwareInfo = HardwareInfo.parseFrom(jmqTopicDTO.getData());
            hardwareInfoManager.processHardwareInfo(hardwareInfo);
            hardwareStatusManager.processHardwareStatus(hardwareInfo);
            log.info("接收到硬件信息数据: {}", ProtoUtils.protoToJson(hardwareInfo));
        } catch (Exception e) {
            log.error("解析硬件信息数据失败: {}", message, e);
        }
    }
}