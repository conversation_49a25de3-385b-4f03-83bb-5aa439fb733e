/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/
package com.jdx.rover.server.business.manager.report;

import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleAlarmEventDTO;
import com.jdx.rover.server.api.domain.dto.report.routing.VehicleRoutingStatusDTO;
import com.jdx.rover.server.api.domain.enums.guardian.AlarmTypeEnum;
import com.jdx.rover.server.business.config.ProducerTopicProperties;
import com.jdx.rover.server.repository.jmq.JmqProducerManager;
import com.jdx.rover.server.repository.redis.guardian.ReportAlarmRepository;
import jdx.rover.report.dto.proto.ReportDto;
import jdx.rover.report.dto.proto.ReportHeader;
import jdx.rover.report.routing.proto.ReportRouting;
import jdx.rover.report.routing.proto.ReportRouting.RoutingStatus.ResultCode.Type;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 收到路由信息,对外发送kafka消息
 *
 * <AUTHOR>
 * @version 1.0
 * @date  2022-01-01
 */
@Service
@Slf4j
public class VehicleRoutingManager {
    @Autowired
    private KafkaTemplate<String, String> kafkaTemplate;

    @Autowired
    private ReportAlarmRepository reportAlarmRepository;

    @Autowired
    private VehicleAlarmManager vehicleAlarmManager;

    /**
     * jmq消息发送producer
     */
    @Autowired
    private JmqProducerManager jmqProducerManager;

    /**
     * 生产主题配置类
     */
    @Autowired
    private ProducerTopicProperties producerTopicProperties;

    /**
     * 处理启动信息
     */
    public void handle(ReportDto.ReportDTO reportDTO, ReportHeader.MessageTypeRequest messageTypeRequest) {
        if (!ReportHeader.MessageType.ROUTING_STATUS.equals(messageTypeRequest.getMessageType()) &&
                !ReportHeader.MessageType.ROUTING_ALL.equals(messageTypeRequest.getMessageType())) {
            return;
        }
        if (Objects.isNull(reportDTO.getRoutingStatus())) {
            return;
        }
        // 处理路由数据
        ReportRouting.RoutingStatus routingStatusProto = reportDTO.getRoutingStatus();
        VehicleRoutingStatusDTO vehicleRoutingStatus = new VehicleRoutingStatusDTO();
        vehicleRoutingStatus.setVehicleName(reportDTO.getRequestHeader().getVehicleName());
        vehicleRoutingStatus.setRuntimeUuid(reportDTO.getReportStatisticsData().getRuntimeUuid());
        vehicleRoutingStatus.setReportTime(reportDTO.getRequestHeader().getRequestTime());
        vehicleRoutingStatus.setResultCodeType(routingStatusProto.getResultCode().getType().name());
        vehicleRoutingStatus.setResultCodeMsg(routingStatusProto.getResultCode().getMsg().name());
        vehicleRoutingStatus.setRoutingId(routingStatusProto.getRoutingId());
        vehicleRoutingStatus.setGlobalMileage(routingStatusProto.getGlobalMileage());
        vehicleRoutingStatus.setCurrentStopIndex(routingStatusProto.getCurrentStopIndex());
        vehicleRoutingStatus.setMileageToNextStop(routingStatusProto.getMileageToNextStop());
        vehicleRoutingStatus.setNaviRefreshId(routingStatusProto.getNaviRefreshId());
        vehicleRoutingStatus.setNaviSequenceNum(routingStatusProto.getNaviSequenceNum());
        vehicleRoutingStatus.setMissionStatus(routingStatusProto.getMissionStatus().name());

        String kafkaTopic = producerTopicProperties.getServerReportRoutingStatus();
        if (ReportHeader.MessageType.ROUTING_ALL.equals(messageTypeRequest.getMessageType())) {
            vehicleRoutingStatus.setStopResultList(buildStopResultList(routingStatusProto));
            kafkaTopic = producerTopicProperties.getServerReportRoutingAll();
        }

        String jsonStr = JsonUtils.writeValueAsString(vehicleRoutingStatus);
        jmqProducerManager.sendOrdered(kafkaTopic, jsonStr, reportDTO.getRequestHeader().getVehicleName());
        log.info("发送路由{}信息={}", kafkaTopic, jsonStr);

        // 处理Routing规划失败异常
        handleRoutingPlanFail(reportDTO.getRequestHeader().getVehicleName(), routingStatusProto);
    }

    /**
     * 处理routing规划失败异常消除
     *
     * @param vehicleName vehicleName
     * @param routingStatusProto routingStatusProto
     */
    private void handleRoutingPlanFail(String vehicleName, ReportRouting.RoutingStatus routingStatusProto) {
        Type type = routingStatusProto.getResultCode().getType();

        // Routing规划成功或任务关闭，消除异常
        if (type == Type.CANCELLED || type == Type.OK) {
            Map<String, VehicleAlarmEventDTO> alarmEventDTOMap = reportAlarmRepository.get(vehicleName);
            if (!alarmEventDTOMap.containsKey(AlarmTypeEnum.ROUTING_PLAN_FAIL.getValue())) {
                return;
            }
            vehicleAlarmManager.removeAlarm(vehicleName, AlarmTypeEnum.ROUTING_PLAN_FAIL.getValue());
        }
    }

    private static List<VehicleRoutingStatusDTO.StopResult> buildStopResultList(ReportRouting.RoutingStatus routingStatusProto) {
        List<VehicleRoutingStatusDTO.StopResult> stopResultList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(routingStatusProto.getStopResultList())) {
            return stopResultList;
        }
        routingStatusProto.getStopResultList().forEach(item -> {
            VehicleRoutingStatusDTO.StopResult dto = new VehicleRoutingStatusDTO.StopResult();
            dto.setStopId(item.getStopId());
            dto.setRoutingMileage(item.getRoutingMileage());
            if (!CollectionUtils.isEmpty(item.getRoutingPointList())) {
                return;
            }
            List<VehicleRoutingStatusDTO.RoutingPosition> routingPositionList = new ArrayList<>();
            item.getRoutingPointList().forEach(position -> {
                VehicleRoutingStatusDTO.RoutingPosition routingPosition = new VehicleRoutingStatusDTO.RoutingPosition();
                routingPosition.setLongitude(position.getLongitude());
                routingPosition.setLatitude(position.getLatitude());
                routingPositionList.add(routingPosition);
            });
            dto.setRoutingPositionList(routingPositionList);
            stopResultList.add(dto);
        });
        return stopResultList;
    }
}
