/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */
package com.jdx.rover.server.business.jmq;

import com.jd.jmq.client.consumer.MessageListener;
import com.jd.jmq.common.message.Message;
import com.jdx.rover.server.business.service.map.MapModuleVersionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 地图模块版本监听器.
 * 接收kafka发送的地图模块版本信息，并存入redis中.
 *
 * <AUTHOR>
 * @date 2024-12-17
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class MapModuleVersionConsumer implements MessageListener {
    /**
     * 地图模块版本服务
     */
    private final MapModuleVersionService mapModuleVersionService;

    /**
     * 接收到消息后的处理
     *
     * @param messageList 消息列表
     */
    @Override
    public void onMessage(List<Message> messageList) {
        if (CollectionUtils.isEmpty(messageList)) {
            return;
        }
        if (messageList.size() > 1) {
            log.info("Received topic={}, size={}", messageList.get(0).getTopic(), messageList.size());
        }
        for (Message message : messageList) {
            try {
                log.info("Received topic={}, message={}", message.getTopic(), message.getText());
                mapModuleVersionService.handleOneMessage(message.getText());
            } catch (Exception e) {
                log.error("处理消息失败!{}", message, e);
            }
        }
    }
}