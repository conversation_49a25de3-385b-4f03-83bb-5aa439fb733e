/*
 * Copyright (c) 2021 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.server.business;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Import;
import org.springframework.context.annotation.ImportResource;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * 入口函数类
 *
 * <AUTHOR>
 * @date  2022-01-01
 */
@EnableCaching
@EnableDiscoveryClient
@SpringBootApplication
@ComponentScan(basePackages = {"com.jdx.rover.server.repository", "com.jdx.rover.server.business", "com.jdx.rover.server.service"})
@EnableFeignClients(basePackages = {"com.jdx.rover.server.repository.feign"})
@Import(cn.hutool.extra.spring.SpringUtil.class)
@ImportResource(locations = {"classpath:jmq.xml"})
@EnableScheduling
public class BusinessApplication {
  public static void main(String[] args) {
    SpringApplication.run(BusinessApplication.class, args);
  }
}
