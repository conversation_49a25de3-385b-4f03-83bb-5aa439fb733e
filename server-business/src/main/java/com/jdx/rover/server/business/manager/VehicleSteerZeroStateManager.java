/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/
package com.jdx.rover.server.business.manager;

import cn.hutool.core.util.NumberUtil;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.server.api.domain.dto.status.ChangeStatusDTO;
import com.jdx.rover.server.business.config.ProducerTopicProperties;
import com.jdx.rover.server.common.utils.date.ServerDateUtils;
import com.jdx.rover.server.domain.dto.vehicle.VehicleStatusDTO;
import com.jdx.rover.server.repository.jmq.JmqProducerManager;
import com.jdx.rover.server.repository.redis.guardian.VehicleStatusRepository;
import jdx.rover.guardian.data.dto.GuardianDataDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 车辆零偏值处理
 *
 * <AUTHOR>
 * @version 1.0
 * @date  2022-01-01
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class VehicleSteerZeroStateManager {
    private final KafkaTemplate<String, String> kafkaTemplate;

    private final VehicleStatusRepository vehicleStatusRepository;
    /**
     * jmq消息发送producer
     */
    private final JmqProducerManager jmqProducerManager;

    /**
     * 生产主题配置类
     */
    private final ProducerTopicProperties producerTopicProperties;

    /**
     * 处理车辆零偏状态
     */
    public void handle(GuardianDataDto.GuardianInfoDTO guardianInfoDTO) {
        GuardianDataDto.ChassisStatus chassisStatus = guardianInfoDTO.getChassisStatus();
        Double steerZero = chassisStatus.getSteerZero();
        if (Objects.isNull(steerZero)) {
            return;
        }
        String newSteerZeroValue = String.valueOf(NumberUtil.round(steerZero, 2).doubleValue());
        String vehicleName = guardianInfoDTO.getVehicleId();
        VehicleStatusDTO vehicleStatusDTO = vehicleStatusRepository.get(vehicleName);
        if (!Objects.isNull(vehicleStatusDTO) && Objects.equals(vehicleStatusDTO.getSteerZero(), newSteerZeroValue)) {
            return;
        }
        vehicleStatusRepository.putMapValue(vehicleName, VehicleStatusDTO::getSteerZero, newSteerZeroValue);

        ChangeStatusDTO changeStatusDTO = new ChangeStatusDTO();
        changeStatusDTO.setVehicleName(vehicleName);
        changeStatusDTO.setOldValue(vehicleStatusDTO.getSteerZero());
        changeStatusDTO.setNewValue(newSteerZeroValue);
        changeStatusDTO.setChangeType(ChangeStatusDTO.ChangeTypeEnum.STEER_ZERO.name());
        changeStatusDTO.setRecordTime(ServerDateUtils.getDateFromNanosecond(guardianInfoDTO.getTimestamp()));
        String jsonStr = JsonUtils.writeValueAsString(changeStatusDTO);
        jmqProducerManager.sendOrderedAndLog(producerTopicProperties.getServerVehicleChangeStatus(), jsonStr, vehicleName);
    }
}
