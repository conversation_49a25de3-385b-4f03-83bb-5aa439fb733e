/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/
package com.jdx.rover.server.business.manager;

import cn.hutool.core.collection.CollectionUtil;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleOtaVersionInfoDTO;
import com.jdx.rover.server.api.domain.enums.OtaMoudleEnum;
import com.jdx.rover.server.api.domain.guardian.VehicleVersionInfoEntity;
import com.jdx.rover.server.business.config.ProducerTopicProperties;
import com.jdx.rover.server.domain.vo.guardian.GuardianInfoAddVo;
import com.jdx.rover.server.domain.vo.guardian.OtaUpdateTaskStatus;
import com.jdx.rover.server.domain.vo.guardian.ServiceInfo;
import com.jdx.rover.server.repository.jmq.JmqProducerManager;
import com.jdx.rover.server.repository.redis.guardian.VehicleVersionInfoRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <p>
 * This is a guardian service info manager which contains operations on redis.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 * @date  2022-01-01
 */
@Service
@Slf4j
public class VehicleVersionInfoManager {

  @Autowired
  private VehicleVersionInfoRepository vehicleVersionInfoRepository;

  @Autowired
  private KafkaTemplate kafkaTemplate;
  /**
   * jmq消息发送producer
   */
  @Autowired
  private JmqProducerManager jmqProducerManager;

  /**
   * 生产主题配置类
   */
  @Autowired
  private ProducerTopicProperties producerTopicProperties;

  /**
   * 处理电量信息
   *
   * @param guardianInfoAddVo guardian信息.
   */
  public void handle(GuardianInfoAddVo guardianInfoAddVo) {
    Map<String, VehicleVersionInfoEntity> dataInRedis =
            vehicleVersionInfoRepository.getByVehicleName(guardianInfoAddVo.getVehicleName());
    OtaUpdateTaskStatus otaInfo = guardianInfoAddVo.getOtaUpdateTaskStatus();
    Map<String, VehicleVersionInfoEntity> otaInfoEntityMap = new HashMap<>();
    if (CollectionUtil.isNotEmpty(otaInfo.getOtaModuleVersion())) {
      otaInfoEntityMap.putAll(otaInfo.getOtaModuleVersion().stream().map(moduleVersion -> {
        OtaMoudleEnum otaMoudleEnum = OtaMoudleEnum.of(moduleVersion.getName());
        return Optional.ofNullable(otaMoudleEnum).map(enumModule -> enumModule.getConstructor().get().convert(otaMoudleEnum.getName(), moduleVersion.getCurVersion(), moduleVersion.getOtaVersion())).orElse(null);
      }).filter(entity -> isNewVersionInfo(entity, dataInRedis)).collect(Collectors.toMap(VehicleVersionInfoEntity::getKey, v -> v)));
    }
    List<ServiceInfo> serviceInfoList = guardianInfoAddVo.getServiceInfo();
    if (CollectionUtil.isNotEmpty(serviceInfoList)) {
      otaInfoEntityMap.putAll(serviceInfoList.stream().map(moduleVersion -> {
        OtaMoudleEnum otaMoudleEnum = OtaMoudleEnum.of(moduleVersion.getServiceName());
        return Optional.ofNullable(otaMoudleEnum).map(enumModule -> enumModule.getConstructor().get().convert(otaMoudleEnum.getName(), moduleVersion.getMsg(), moduleVersion.getMsg())).orElse(null);
      }).filter(entity -> isNewVersionInfo(entity, dataInRedis)).collect(Collectors.toMap(VehicleVersionInfoEntity::getKey, v -> v)));
    }
    if (CollectionUtil.isEmpty(otaInfoEntityMap)) {
      return;
    }
    dataInRedis.putAll(otaInfoEntityMap);
    log.info("save value {}", JsonUtils.writeValueAsString(otaInfoEntityMap));
    vehicleVersionInfoRepository.saveValue(guardianInfoAddVo.getVehicleName(), dataInRedis);
    VehicleOtaVersionInfoDTO vehicleOtaVersionInfoDTO = new VehicleOtaVersionInfoDTO();
    vehicleOtaVersionInfoDTO.setVehicleName(guardianInfoAddVo.getVehicleName());
    vehicleOtaVersionInfoDTO.setVersion(new ArrayList<>(otaInfoEntityMap.values()));
    jmqProducerManager.sendOrderedAndLog(producerTopicProperties.getServerGuardianOtaVersion(), JsonUtils.writeValueAsString(vehicleOtaVersionInfoDTO), guardianInfoAddVo.getVehicleName());
  }

  /**
   * 是否新的版本更新(redis中不存在为新的信息)
   */
  private boolean isNewVersionInfo(VehicleVersionInfoEntity comeEntity, Map<String, VehicleVersionInfoEntity> versionInfoEntityMap) {
    if (comeEntity == null) {
      return false;
    }
    if (CollectionUtils.isEmpty(versionInfoEntityMap)) {
      return true;
    }
    VehicleVersionInfoEntity entity = versionInfoEntityMap.get(comeEntity.getKey());
    if (entity == null || !StringUtils.equals(entity.getCurVersion(), comeEntity.getCurVersion())) {
      return true;
    }
    return false;
  }
}
