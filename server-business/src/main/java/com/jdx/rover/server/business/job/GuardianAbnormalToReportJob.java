package com.jdx.rover.server.business.job;

import com.jdx.rover.server.business.manager.report.VehicleAbnormalReportManager;
import com.wangyin.schedule.client.job.ScheduleContext;
import com.wangyin.schedule.client.job.ScheduleFlowTask;
import com.wangyin.schedule.client.job.TaskResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class GuardianAbnormalToReportJob implements ScheduleFlowTask {

    private final VehicleAbnormalReportManager vehicleAbnormalReportManager;

    @Override
    public TaskResult doTask(ScheduleContext scheduleContext) {
        vehicleAbnormalReportManager.guardianAbnormalToReport();
        return TaskResult.success();
    }
}