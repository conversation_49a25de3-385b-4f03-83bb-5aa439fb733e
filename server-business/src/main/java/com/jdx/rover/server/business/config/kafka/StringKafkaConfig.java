package com.jdx.rover.server.business.config.kafka;

import org.apache.kafka.common.serialization.StringDeserializer;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.kafka.config.KafkaListenerContainerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.listener.ConcurrentMessageListenerContainer;

/**
 * <AUTHOR>
 */
@Primary
@Configuration
public class StringKafkaConfig extends BaseKafkaConfig {
  @Bean
  public KafkaTemplate<String, String> kafkaTemplate() {
    return super.kafkaTemplate(StringSerializer.class);
  }

  @Bean
  public KafkaListenerContainerFactory<ConcurrentMessageListenerContainer<Integer, String>> kafkaContainerFactory() {
    return super.kafkaContainerFactory(StringDeserializer.class);
  }
}
