/*
 * Copyright (c) 2022-2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.server.business.controller.vehicle;

import com.google.common.collect.Lists;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleLauchStateInfoDTO;
import com.jdx.rover.server.api.domain.guardian.VehicleStateInfoEntity;
import com.jdx.rover.server.repository.redis.guardian.VehicleStateInfoRepository;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RestController;

/**
 * 车辆启动实时信息
 * <AUTHOR>
 */
@RestController
public class VehicleILauchStateInfoController {

  @Autowired
  private VehicleStateInfoRepository vehicleStateInfoRepository;

  /**
   * 获取车辆实时胎压信息
   *
   * @param vehicleName
   */
  @GetMapping(value = "/server/business/vehicle/lauch_state/{vehicleName}")
  public HttpResult getVehicleLauchStateInfo(@PathVariable("vehicleName") String vehicleName) {
    VehicleStateInfoEntity vehicleStateInfo = vehicleStateInfoRepository.getByVehicleName(vehicleName);
    VehicleLauchStateInfoDTO stateInfoDto = new VehicleLauchStateInfoDTO();
    if (vehicleStateInfo == null) {
      return HttpResult.success(stateInfoDto);
    }
    stateInfoDto.setRecordTime(vehicleStateInfo.getRecordTime());
    stateInfoDto.setVehicleState(vehicleStateInfo.getVehicleState());
    stateInfoDto.setVehicleStateMessage(vehicleStateInfo.getVehicleStateMessage()); 
    return HttpResult.success(vehicleStateInfo);
  }
}
