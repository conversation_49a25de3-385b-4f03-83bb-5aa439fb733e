package com.jdx.rover.server.business.provider;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.jsf.provider.AbstractProvider;
import com.jdx.rover.server.api.domain.dto.guardian.ModuleVersionInfoDTO;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleLauchStateInfoDTO;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleLocationAndPowerDTO;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleLocationDTO;
import com.jdx.rover.server.api.domain.dto.guardian.VehiclePncInfoDTO;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleRealtimeWheelInfoDTO;
import com.jdx.rover.server.api.domain.dto.vehicle.GridDataDTO;
import com.jdx.rover.server.api.domain.vo.vehicle.GridVO;
import com.jdx.rover.server.api.jsf.service.vehicle.ServerVehicleService;
import com.jdx.rover.server.business.service.VehicleGridService;
import com.jdx.rover.server.business.service.VehicleService;
import com.jdx.rover.server.business.service.VehicleVersionService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * @description: ServerVehicleServiceImpl
 * @author: wangguotai
 * @create: 2024-09-27 09:52
 **/
@Service
@RequiredArgsConstructor
public class ServerVehicleServiceImpl extends AbstractProvider<ServerVehicleService> implements ServerVehicleService {

    private final VehicleService vehicleService;

    private final VehicleVersionService vehicleVersionService;

    private final VehicleGridService vehicleGridService;

    @Override
    public HttpResult<VehicleLocationDTO> getVehicleLocation(String vehicleName) {
        return HttpResult.success(vehicleService.getVehicleLocation(vehicleName));
    }

    @Override
    public HttpResult<VehicleLocationAndPowerDTO> getLocationAndPower(String vehicleName) {
        return HttpResult.success(vehicleService.getLocationAndPower(vehicleName));
    }

    @Override
    public HttpResult<List<VehicleRealtimeWheelInfoDTO>> getWheelInfo(String vehicleName) {
        return HttpResult.success(vehicleService.getWheelInfo(vehicleName));
    }

    @Override
    public HttpResult<VehicleLauchStateInfoDTO> getVehicleStartInitStateInfo(String vehicleName) {
        return HttpResult.success(vehicleService.getVehicleStartInitStateInfo(vehicleName));
    }

    @Override
    public HttpResult<Map<String, ModuleVersionInfoDTO>> getVehicleOtaVersionInfo(String vehicleName) {
        return HttpResult.success(vehicleVersionService.getVehicleVersion(vehicleName));
    }

    @Override
    public HttpResult<VehiclePncInfoDTO> getVehiclePnc(String vehicleName) {
        return HttpResult.success(vehicleService.getVehiclePnc(vehicleName));
    }

    @Override
    public HttpResult<GridDataDTO> getGridStatus(GridVO gridVO) {
        return HttpResult.success(vehicleGridService.getGridStatus(gridVO));
    }
}