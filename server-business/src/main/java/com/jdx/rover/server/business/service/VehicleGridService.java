package com.jdx.rover.server.business.service;

import com.jdx.rover.server.api.domain.dto.vehicle.GridDTO;
import com.jdx.rover.server.api.domain.dto.vehicle.GridDataDTO;
import com.jdx.rover.server.api.domain.vo.vehicle.GridVO;
import com.jdx.rover.server.repository.redis.guardian.VehicleGridRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 车辆格口服务
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class VehicleGridService {
    @Autowired
    private VehicleGridRepository vehicleGridRepository;

    public GridDataDTO getGridStatus(GridVO gridVO) {
        GridDataDTO gridDataDTO = vehicleGridRepository.get(gridVO.getVehicleName());
        if (gridDataDTO == null || CollectionUtils.isEmpty(gridDataDTO.getGridList())) {
            log.info("getGridStatus结果为空!{}", gridVO);
            return null;
        }
        GridDataDTO result = new GridDataDTO();
        result.setVehicleName(gridVO.getVehicleName());
        result.setRecordTime(gridDataDTO.getRecordTime());
        if (CollectionUtils.isEmpty(gridVO.getGridNoList())) {
            result.setGridList(gridDataDTO.getGridList());
            return result;
        }
        List<GridDTO> gridList = new ArrayList<>();
        for (String gridNo : gridVO.getGridNoList()) {
            for (GridDTO gridDTO : gridDataDTO.getGridList()) {
                if (Objects.equals(gridNo, gridDTO.getGridNo())) {
                    GridDTO gridStatus = new GridDTO();
                    gridStatus.setGridNo(gridNo);
                    gridStatus.setOpenStatus(gridDTO.getOpenStatus());
                    gridList.add(gridStatus);
                    break;
                }
            }
        }
        result.setGridList(gridList);
        log.info("格口状态={}", result);
        return result;
    }
}
