package com.jdx.rover.server.business.job;

import com.jdx.rover.server.business.manager.report.VehicleAlarmManager;
import com.wangyin.schedule.client.job.ScheduleContext;
import com.wangyin.schedule.client.job.ScheduleFlowTask;
import com.wangyin.schedule.client.job.TaskResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class StopAlarmJob implements ScheduleFlowTask {

  private final VehicleAlarmManager vehicleAlarmManager;

  @Override
  public TaskResult doTask(ScheduleContext scheduleContext) {
//    vehicleAlarmManager.handStopAlarm();
//    log.info("处理关闭停止上报告警完成!");
    return TaskResult.success();
  }
}