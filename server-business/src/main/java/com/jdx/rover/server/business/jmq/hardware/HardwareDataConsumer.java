/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */
package com.jdx.rover.server.business.jmq.hardware;

import com.jd.jmq.client.consumer.MessageListener;
import com.jd.jmq.common.message.Message;
import com.jdx.rover.server.business.service.hardware.HardwareDataService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 车辆硬件信息数据JMQ消费者
 *
 * <AUTHOR>
 * @date 2025-08-25
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class HardwareDataConsumer implements MessageListener {
    /**
     * 车辆硬件信息数据服务
     */
    private final HardwareDataService hardwareDataService;

    /**
     * 接收到消息后的处理
     *
     * @param messageList 消息列表
     */
    @Override
    public void onMessage(List<Message> messageList) {
        if (CollectionUtils.isEmpty(messageList)) {
            return;
        }
        for (Message message : messageList) {
            hardwareDataService.receive(message);
        }
    }
}