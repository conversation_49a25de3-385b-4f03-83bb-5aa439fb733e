/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/
package com.jdx.rover.server.business.manager;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.server.api.domain.constants.KafkaTopicConstant;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleAlarmEventDTO;
import com.jdx.rover.server.api.domain.dto.guardian.VehiclePncInfoDTO;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleRealtimeDrivableInfoDTO;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleRealtimeInfoDTO;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleRealtimeWheelInfoDTO;
import com.jdx.rover.server.api.domain.dto.report.abnormal.VehicleAbnormalDTO;
import com.jdx.rover.server.api.domain.dto.status.ChangeStatusDTO;
import com.jdx.rover.server.api.domain.enums.guardian.AlarmTypeEnum;
import com.jdx.rover.server.api.domain.enums.guardian.SystemStateEnum;
import com.jdx.rover.server.api.domain.enums.guardian.VehicleStateEnum;
import com.jdx.rover.server.business.config.ProducerTopicProperties;
import com.jdx.rover.server.business.manager.report.VehicleAlarmManager;
import com.jdx.rover.server.domain.dto.vehicle.VehicleStatusDTO;
import com.jdx.rover.server.domain.enums.GuardianErrorEnum;
import com.jdx.rover.server.domain.enums.GuardianErrorLevelEnum;
import com.jdx.rover.server.domain.vo.guardian.GuardianInfoAddVo;
import com.jdx.rover.server.domain.vo.guardian.NavigationPncStatus;
import com.jdx.rover.server.domain.vo.guardian.NavigationRoutingStatus;
import com.jdx.rover.server.domain.vo.guardian.VehicleStatusAddVo;
import com.jdx.rover.server.domain.vo.guardian.VehicleWheelEntityAddVo;
import com.jdx.rover.server.domain.vo.guardian.VehicleWheelInfoAddVo;
import com.jdx.rover.server.repository.jmq.JmqProducerManager;
import com.jdx.rover.server.repository.redis.guardian.ReportAbnormalRepository;
import com.jdx.rover.server.repository.redis.guardian.ReportAlarmRepository;
import com.jdx.rover.server.repository.redis.guardian.VehiclePncRepository;
import com.jdx.rover.server.repository.redis.guardian.VehicleRealtimeInfoRepository;
import com.jdx.rover.server.repository.redis.guardian.VehicleStatusRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <p>
 * This is a guardian realtime info manager which contains operations on redis.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date  2022-01-01
 */
@Service
@Slf4j
public class VehicleRealtimeInfoManager {

  @Autowired
  private KafkaTemplate<String, String> kafkaTemplate;

  @Autowired
  private VehicleRealtimeInfoRepository vehicleRealtimeInfoRepository;

  @Autowired
  private VehicleStateInfoManager vehicleStateInfoManager;

  @Autowired
  private VehicleStatusRepository vehicleStatusRepository;

  @Autowired
  private VehicleAlarmManager vehicleAlarmManager;

  @Autowired
  private ReportAlarmRepository reportAlarmRepository;

  @Autowired
  private ReportAbnormalRepository reportAbnormalRepository;

  @Autowired
  private VehicleWheelManager vehicleWheelManager;

  @Autowired
  private VehicleLocalizationManager vehicleLocalizationManager;
  /**
   * jmq消息发送producer
   */
  @Autowired
  private JmqProducerManager jmqProducerManager;

  /**
   * 生产主题配置类
   */
  @Autowired
  private ProducerTopicProperties producerTopicProperties;

  /**
   * <p>
   * solution repairing type.
   * </p>
   */
  private static final String SOLUTION_REPAIRING_TYPE = "REPAIRING";

  /**
   * 处理实时信息
   *
   * @param guardianInfoAddVo guardian信息.
   */
  public void handle(GuardianInfoAddVo guardianInfoAddVo) {
    String vehicleName = guardianInfoAddVo.getVehicleName();
    VehicleRealtimeInfoDTO vehicleRealtimeInfoDTO = buildVehicleRealtimeInfoEntity(guardianInfoAddVo);

    vehicleRealtimeInfoRepository.put(vehicleRealtimeInfoDTO);

    String jsonStr = JsonUtils.writeValueAsString(vehicleRealtimeInfoDTO);
    jmqProducerManager.sendOrdered(producerTopicProperties.getServerGuardianRealtime(), jsonStr, guardianInfoAddVo.getVehicleName());
    kafkaTemplate.send(KafkaTopicConstant.SERVER_GUARDIAN_REALTIME, vehicleName, jsonStr);

    VehicleStatusDTO vehicleStatusDTO = vehicleStatusRepository.get(vehicleName);
    if (Objects.isNull(vehicleStatusDTO) || !Objects.equals(vehicleStatusDTO.getVehicleState(), vehicleRealtimeInfoDTO.getVehicleState())) {
      vehicleStatusRepository.putMapValue(vehicleName, VehicleStatusDTO::getVehicleState, vehicleRealtimeInfoDTO.getVehicleState());
      sendVehicleChangeStatus(guardianInfoAddVo, vehicleStatusDTO, vehicleRealtimeInfoDTO);
    }
    Map<String, VehicleAlarmEventDTO> alarmMapDb = reportAlarmRepository.get(vehicleName);

    // 按钮停车
    handleStopButtonAlarm(guardianInfoAddVo, vehicleRealtimeInfoDTO, vehicleStatusDTO, alarmMapDb);

    // 安全接管停车
    handleVehicleStopGuardian(guardianInfoAddVo, alarmMapDb);

    // 胎压骤减
    vehicleWheelManager.handleWheelPressureSharpDecrease(vehicleRealtimeInfoDTO, alarmMapDb);

    // 定位异常
    vehicleLocalizationManager.handleLocalizationError(vehicleRealtimeInfoDTO, alarmMapDb);
  }

  /**
   * 发送车辆模式变化kafka
   */
  private void sendVehicleChangeStatus(GuardianInfoAddVo guardianInfoAddVo, VehicleStatusDTO vehicleStatusDTO, VehicleRealtimeInfoDTO vehicleRealtimeInfoDTO) {
    ChangeStatusDTO changeStatusDTO = new ChangeStatusDTO();
    changeStatusDTO.setVehicleName(guardianInfoAddVo.getVehicleName());
    changeStatusDTO.setRecordTime(guardianInfoAddVo.getReportTime());
    changeStatusDTO.setOldValue(vehicleStatusDTO.getVehicleState());
    changeStatusDTO.setNewValue(vehicleRealtimeInfoDTO.getVehicleState());
    changeStatusDTO.setChangeType(ChangeStatusDTO.ChangeTypeEnum.VEHICLE_STATE.name());
    String changeStatusStr = JsonUtils.writeValueAsString(changeStatusDTO);
    jmqProducerManager.sendOrdered(producerTopicProperties.getServerVehicleChangeStatus(), changeStatusStr, guardianInfoAddVo.getVehicleName());
  }

  /**
   * 处理停车按钮报警
   *
   * @param guardianInfoAddVo      监控信息
   * @param vehicleRealtimeInfoDTO 车辆实时信息
   * @param vehicleStatusDTO       车辆状态信息
   */
  private void handleStopButtonAlarm(GuardianInfoAddVo guardianInfoAddVo, VehicleRealtimeInfoDTO vehicleRealtimeInfoDTO, VehicleStatusDTO vehicleStatusDTO, Map<String, VehicleAlarmEventDTO> alarmMapDb) {
    // 获取车辆名称和状态
    String vehicleName = guardianInfoAddVo.getVehicleName();
    String vehicleState = vehicleRealtimeInfoDTO.getVehicleState();

    // 忽略车辆状态为空的情况
    if (Objects.isNull(vehicleState) || Objects.isNull(vehicleStatusDTO)) {
      return;
    }

    // 忽略非按钮停车的情况
    if (!Objects.equals(vehicleState, VehicleStateEnum.EMERGENCY_STOP.getVehicleState())
            && !Objects.equals(vehicleStatusDTO.getVehicleState(), VehicleStateEnum.EMERGENCY_STOP.getVehicleState())) {
      return;
    }

    // 如果车辆状态不是紧急停车，则移除车辆停车按钮报警
    if (!Objects.equals(vehicleState, VehicleStateEnum.EMERGENCY_STOP.getVehicleState())) {
      vehicleAlarmManager.removeAlarm(vehicleName, AlarmTypeEnum.VEHICLE_STOP_BUTTON.getValue());
    } else if (VehicleStateInfoManager.hasPlanRoute(guardianInfoAddVo)){
      VehicleAlarmEventDTO alarmEventDTO = alarmMapDb.get(AlarmTypeEnum.VEHICLE_STOP_BUTTON.getValue());
      if (Objects.isNull(alarmEventDTO)) {
        // 如果导航信息不为空，则添加车辆停车按钮报警
        VehicleAlarmEventDTO alarm = new VehicleAlarmEventDTO();
        alarm.setType(AlarmTypeEnum.VEHICLE_STOP_BUTTON.getValue());
        alarm.setReportTime(guardianInfoAddVo.getReportTime());
        alarm.setErrorCode(AlarmTypeEnum.VEHICLE_STOP_BUTTON.getValue());
        vehicleAlarmManager.addAlarm(vehicleName, alarm);
      }
    }
  }

  /**
   * <p>
   * Get supervisor real-time vehicle system state by GuardianInfoAddVo.
   * </p>
   *
   * @param guardianInfoAddVo The guardian info.
   * @return status of supervisor real time system.
   */
  public VehicleRealtimeInfoDTO buildVehicleRealtimeInfoEntity(GuardianInfoAddVo guardianInfoAddVo) {
    String vehicleName = guardianInfoAddVo.getVehicleName();
    VehicleRealtimeInfoDTO realtimeInfoEntity = new VehicleRealtimeInfoDTO();
    realtimeInfoEntity.setRecordTime(guardianInfoAddVo.getReportTime());
    realtimeInfoEntity.setVehicleName(vehicleName);
    realtimeInfoEntity.setSystemState(getVehicleSystemState(guardianInfoAddVo));
    realtimeInfoEntity.setVehicleState(vehicleStateInfoManager.getVehicleDriveMode(guardianInfoAddVo));
    VehicleStatusAddVo vehicleStatusRequestVo = guardianInfoAddVo.getVehicleStatusRequest();
    if (vehicleStatusRequestVo != null) {
      realtimeInfoEntity.setSpeed(vehicleStatusRequestVo.getSpeed());
      realtimeInfoEntity.setPower(vehicleStatusRequestVo.getPowerUsage());
      realtimeInfoEntity.setHeading(vehicleStatusRequestVo.getHeading());
      realtimeInfoEntity.setLat(vehicleStatusRequestVo.getLat());
      realtimeInfoEntity.setLon(vehicleStatusRequestVo.getLon());
    }
    if (realtimeInfoEntity.getSpeed() == null) {
      realtimeInfoEntity.setSpeed(0.0);
    }
    if (Objects.isNull(vehicleStatusRequestVo.getSteerZero())) {
      realtimeInfoEntity.setSteerZero(0.0);
    } else {
      realtimeInfoEntity.setSteerZero(NumberUtil.round(vehicleStatusRequestVo.getSteerZero(), 2).doubleValue());
    }
    realtimeInfoEntity.setWheelInfo(getVehicleWheelInfo(guardianInfoAddVo));
    realtimeInfoEntity.setDrivableDirection(getVehicleDrivableDirection(guardianInfoAddVo));
    // TODO: move this convert to json
    NavigationPncStatus navigationPncStatus = guardianInfoAddVo.getNavigationPncStatusRequest();
    NavigationRoutingStatus navigationRoutingStatus = navigationPncStatus.getRoutingStatus();
    realtimeInfoEntity.setCurrentStopFinishedMileage(navigationRoutingStatus.getCurrentStopFinishedMileage());
    realtimeInfoEntity.setMileageToNextStop(navigationRoutingStatus.getMileageToNextStop());
    realtimeInfoEntity.setCurrentStopIndex(navigationRoutingStatus.getCurrentStopIndex());
//    vehicleRealtimeInfoRepository.saveValue(vehicleName, realtimeInfoEntity);
    return realtimeInfoEntity;
  }

  /**
   * <p>
   * Get supervisor real-time vehicle system state by GuardianInfoAddVo.
   * </p>
   *
   * @param guardianInfoAddVo The guardian info.
   * @return status of supervisor real time system.
   */
  private String getVehicleSystemState(GuardianInfoAddVo guardianInfoAddVo) {
    VehicleStatusAddVo vehicleStatus = guardianInfoAddVo.getVehicleStatusRequest();
    if (vehicleStatus != null && SOLUTION_REPAIRING_TYPE.equals(vehicleStatus.getSolutionStatus())) {
      return SystemStateEnum.SELF_REPAIR.name();
    }
    boolean isMalFunction = guardianInfoAddVo.getVehicleExceptionLogSaveRequestList().stream().anyMatch(
        vehicleExceptionLogAddVo -> GuardianErrorLevelEnum.FATAL.getErrorLevel().equals(vehicleExceptionLogAddVo.getErrorLevel()));
    return isMalFunction ? SystemStateEnum.MALFUNCTION.name() : SystemStateEnum.NORMAL.name();
  }

  /**
   * <p>
   * Get real-time vehicle drivable direction by GuardianInfoAddVo.
   * </p>
   *
   * @param guardianInfoAddVo The guardian info from vehicle.
   * @return message of vehicle drivable enable.
   */
  private VehicleRealtimeDrivableInfoDTO getVehicleDrivableDirection(GuardianInfoAddVo guardianInfoAddVo) {
    VehicleRealtimeDrivableInfoDTO drivableDirection = new VehicleRealtimeDrivableInfoDTO();
    if (guardianInfoAddVo.getDrivableDirectionRequest() != null) {
      drivableDirection.setEnableBack(guardianInfoAddVo.getDrivableDirectionRequest().getEnableBack());
      drivableDirection.setEnableFront(guardianInfoAddVo.getDrivableDirectionRequest().getEnableFront());
      drivableDirection.setEnableLeft(guardianInfoAddVo.getDrivableDirectionRequest().getEnableLeft());
      drivableDirection.setEnableRight(guardianInfoAddVo.getDrivableDirectionRequest().getEnableRight());
    }
    return drivableDirection;
  }

  /**
   * <p>
   * Get real-time vehicle drivable direction by GuardianInfoAddVo.
   * </p>
   *
   * @param guardianInfoAddVo The guardian info from vehicle.
   * @return message of vehicle drivable enable.
   */
  private List<VehicleRealtimeWheelInfoDTO> getVehicleWheelInfo(GuardianInfoAddVo guardianInfoAddVo) {
    List<VehicleRealtimeWheelInfoDTO> wheelInfo = new ArrayList<VehicleRealtimeWheelInfoDTO>();
    VehicleWheelInfoAddVo wheelInfoAddVo = guardianInfoAddVo.getVehicleWheelInfo();
    if (wheelInfoAddVo != null && wheelInfoAddVo.getVehicleWheelEntityAddVo() != null) {
      List<VehicleWheelEntityAddVo> wheelEntitys = wheelInfoAddVo.getVehicleWheelEntityAddVo();
      for (VehicleWheelEntityAddVo wheelEntity : wheelEntitys) {
        VehicleRealtimeWheelInfoDTO wheelInfoEntity = new VehicleRealtimeWheelInfoDTO();
        wheelInfoEntity.setPressure(wheelEntity.getPressure());
        wheelInfoEntity.setPressureStatus(wheelEntity.getPressureStatus());
        wheelInfoEntity.setWheelPosition(wheelEntity.getWheelPosition());
        wheelInfo.add(wheelInfoEntity);
      }
    }
    return wheelInfo;
  }

  /**
   * 处理安全接管停车
   */
  private void handleVehicleStopGuardian(GuardianInfoAddVo guardianInfoAddVo, Map<String, VehicleAlarmEventDTO> alarmMapDb) {
    String vehicleName = guardianInfoAddVo.getVehicleName();

    VehicleAlarmEventDTO alarmDb = alarmMapDb.get(AlarmTypeEnum.VEHICLE_STOP_GUARDIAN.getValue());
    if (VehicleStateInfoManager.disableFrontAndBack(guardianInfoAddVo)) {
      VehiclePncInfoDTO vehiclePncInfoDTO = SpringUtil.getBean(VehiclePncRepository.class).get(vehicleName);
      if (Objects.isNull(alarmDb) && VehicleStateInfoManager.hasPlaning(vehiclePncInfoDTO)) {
        // 新增告警
        VehicleAlarmEventDTO alarm = new VehicleAlarmEventDTO();
        alarm.setType(AlarmTypeEnum.VEHICLE_STOP_GUARDIAN.getValue());
        alarm.setReportTime(guardianInfoAddVo.getReportTime());
        alarm.setErrorCode(GuardianErrorEnum.GUARD_EMERGENCY_STOP_ERROR_CODE_TYPE.getErrorCode());
        vehicleAlarmManager.addAlarm(vehicleName, alarm);
      }
      return;
    }

    if (Objects.isNull(alarmDb)) {
      // 没有使能异常,没有告警,不需要删除,直接忽略
      return;
    }

    Map<String, VehicleAbnormalDTO> abnormalMapDb = reportAbnormalRepository.get(vehicleName);
    if (!CollectionUtils.isEmpty(abnormalMapDb)) {
      for (VehicleAbnormalDTO abnormalDb : abnormalMapDb.values()) {
        if (Objects.equals(abnormalDb.getErrorCode(), GuardianErrorEnum.GUARD_EMERGENCY_STOP_ERROR_CODE_TYPE.getErrorCode())) {
          // 存在-13600异常,直接忽略
          return;
        }
      }
    }

    // 删除告警
    vehicleAlarmManager.removeAlarm(vehicleName, AlarmTypeEnum.VEHICLE_STOP_GUARDIAN.getValue());
  }
}
