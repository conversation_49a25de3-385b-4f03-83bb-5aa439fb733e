package com.jdx.rover.server.business.listener;

import com.jdx.rover.server.business.service.GuardianService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * guardian消息接收Listener
 *
 * <AUTHOR>
 * @date  2022-01-01
 */
@Service
@Slf4j
public class GuardianKafkaListener {
  @Autowired
  private GuardianService guardianService;

//  @KafkaListener(topics = {"server_upstream_guardian_origin"}, containerFactory = "byteKafkaContainerFactory")
  public void onMessage(List<byte[]> messageList) {
    guardianService.receiveList(messageList);
  }
}
