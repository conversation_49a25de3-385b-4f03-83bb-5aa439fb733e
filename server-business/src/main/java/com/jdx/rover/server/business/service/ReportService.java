/*
 * Copyright (c) 2023 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.server.business.service;

import cn.hutool.core.util.HexUtil;
import com.jdx.rover.server.business.manager.report.VehicleAbnormalReportManager;
import com.jdx.rover.server.business.manager.report.VehicleBootManager;
import com.jdx.rover.server.business.manager.report.VehicleDriveModeManager;
import com.jdx.rover.server.business.manager.report.VehicleGpsManager;
import com.jdx.rover.server.business.manager.report.VehicleRoutingManager;
import com.jdx.rover.server.business.manager.report.VehicleMaxVelocityManager;
import com.jdx.rover.server.business.manager.report.*;
import jdx.rover.report.dto.proto.ReportDto;
import jdx.rover.report.dto.proto.ReportHeader;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 车辆report数据service
 *
 * <AUTHOR>
 * @version 1.0
 * @date  2022-01-01
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class ReportService {
    private final VehicleBootManager vehicleBootManager;
    private final VehicleDriveModeManager vehicleDriveModeManager;
    private final VehicleAbnormalReportManager vehicleAbnormalReportManager;
    private final VehicleRoutingManager vehicleRoutingManager;
    private final VehicleGpsManager vehicleGpsManager;
    private final VehicleMaxVelocityManager vehicleMaxVelocityManager;
    private final VehicleHardwareManager vehicleHardwareManager;

    /**
     * 处理消息
     */
    public void receive(byte[] message) {
        try {
            ReportDto.ReportDTO reportDTO = ReportDto.ReportDTO.parseFrom(message);
            receive(reportDTO);
        } catch (Exception e) {
            log.error("解析report数据失败!{}", HexUtil.encodeHexStr(message), e);
        }
    }

    /**
     * 处理消息
     */
    private void receive(ReportDto.ReportDTO reportDTO) {
        ReportHeader.RequestHeader reportHeader = reportDTO.getRequestHeader();
        List<ReportHeader.MessageTypeRequest> requestList = reportHeader.getMessageTypeRequestList();
        for (ReportHeader.MessageTypeRequest messageTypeRequest : requestList) {
            // 处理boot启动数据
            switch (messageTypeRequest.getMessageType()) {
                case BOOT:
                    vehicleBootManager.handle(reportDTO, messageTypeRequest);
                    break;
                case DRIVING_MODE:
                    vehicleDriveModeManager.handle(reportDTO, messageTypeRequest);
                    break;
                case ABNORMAL:
                    vehicleAbnormalReportManager.handle(reportDTO, messageTypeRequest);
                    break;
                case ROUTING_ALL:
                case ROUTING_STATUS:
                    vehicleRoutingManager.handle(reportDTO, messageTypeRequest);
                    break;
                case GPS:
                    vehicleGpsManager.handle(reportDTO, messageTypeRequest);
                    break;
                case VELOCITY:
                    vehicleMaxVelocityManager.handle(reportDTO, messageTypeRequest);
                    break;
                case HARDWARE:
                    vehicleHardwareManager.handle(reportDTO, messageTypeRequest);
                    break;
                default:
            }
        }
    }
}