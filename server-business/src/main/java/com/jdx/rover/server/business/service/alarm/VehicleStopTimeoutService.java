package com.jdx.rover.server.business.service.alarm;

import cn.hutool.extra.spring.SpringUtil;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleAlarmEventDTO;
import com.jdx.rover.server.api.domain.dto.guardian.VehiclePncInfoDTO;
import com.jdx.rover.server.api.domain.enums.guardian.AlarmTypeEnum;
import com.jdx.rover.server.business.manager.VehicleStateInfoManager;
import com.jdx.rover.server.business.manager.localview.PlanningManager;
import com.jdx.rover.server.domain.enums.HandleAlarmEnum;
import com.jdx.rover.server.repository.redis.guardian.VehiclePncRepository;
import jdx.rover.guardian.dto.LocalView;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.queue.CircularFifoQueue;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 遇阻停车
 *
 * <AUTHOR>
 */
@Slf4j
public class VehicleStopTimeoutService {
    private static final int MAX_INDEX = 16;
    private static final int MAX_SIZE = MAX_INDEX + 1;
    private static final Map<String, CircularFifoQueue<VehicleLocation>> vehicleLocationQueueMap = new HashMap<>();

    /**
     * 缓存车辆位置
     */
    public static VehicleLocation cacheLocation(LocalView.LocalViewDTO dto) {
        String vehicleName = dto.getAuthInfo().getVehicleId();
        CircularFifoQueue<VehicleLocation> vehicleLocationQueue = vehicleLocationQueueMap.get(vehicleName);
        if (Objects.isNull(vehicleLocationQueue)) {
            vehicleLocationQueue = new CircularFifoQueue<>(MAX_SIZE);
            vehicleLocationQueueMap.put(vehicleName, vehicleLocationQueue);
        }
        return addVehicleLocation(dto, vehicleLocationQueue);
    }

    /**
     * 清空缓存的车辆位置
     */
    public static void clearLocation(LocalView.LocalViewDTO dto) {
        String vehicleName = dto.getAuthInfo().getVehicleId();
        CircularFifoQueue<VehicleLocation> vehicleLocationQueue = vehicleLocationQueueMap.get(vehicleName);
        if (Objects.isNull(vehicleLocationQueue) || vehicleLocationQueue.isEmpty()) {
           return;
        }
        vehicleLocationQueue.clear();
    }

    /**
     * 是否遇阻停车
     * 上报条件
     * 仅当PNC有规划路线
     * <p>
     * 持续15s行驶距离小于0.25m
     * 持续15S车速0.2m/s以下
     * 持续8s车速为0
     * <p>
     * 消失条件
     * 3秒内车辆行驶超1米，消失
     * 当车辆pnc规划路线消失时，消失
     *
     * @param dto
     * @return -1 删除报警 0暂不处理 1新增报警
     */
    public static HandleAlarmEnum isReportAlarm(LocalView.LocalViewDTO dto, Map<String, VehicleAlarmEventDTO> alarmMap) {
        String vehicleName = dto.getAuthInfo().getVehicleId();
        if (StringUtils.isBlank(vehicleName)) {
            return HandleAlarmEnum.IGNORE;
        }

        VehicleAlarmEventDTO alarmDb = alarmMap.get(AlarmTypeEnum.VEHICLE_STOP_TIMEOUT.getValue());
        VehiclePncInfoDTO vehiclePncInfoDTO = SpringUtil.getBean(VehiclePncRepository.class).get(vehicleName);
        if (!VehicleStateInfoManager.hasPlaning(vehiclePncInfoDTO)) {
            // 当车辆pnc规划路线消失时,清空缓存位置队列，消除报警
            clearLocation(dto);
            if (!Objects.isNull(alarmDb)) {
                log.info("车辆pnc规划路线消失时,消除遇阻停车报警!车辆={},告警={}", vehicleName, alarmMap);
                return HandleAlarmEnum.REMOVE;
            }
            return HandleAlarmEnum.IGNORE;
        }
        if (PlanningManager.isStopTrafficLight(dto)) {
            // 红绿灯停车意图上报后，遇阻停车重新计时
            clearLocation(dto);
            return HandleAlarmEnum.IGNORE;
        }
        cacheLocation(dto);
        CircularFifoQueue<VehicleLocation> vehicleLocationQueue = vehicleLocationQueueMap.get(vehicleName);
        if (!vehicleLocationQueue.isAtFullCapacity()) {
            // 队列不满,暂不处理
            log.info("初始队列不满,暂不处理遇阻停车!车辆={},大小={}", vehicleName, vehicleLocationQueue.size());
            return HandleAlarmEnum.IGNORE;
        }

        if (!Objects.isNull(alarmDb)) {
            // 3秒内车辆行驶超1米，消失
            if (computeDistance(vehicleLocationQueue, 3) > 1) {
                log.info("3秒内车辆行驶超1米,消失遇阻停车报警!车辆={},告警={}", vehicleName, alarmMap);
                return HandleAlarmEnum.REMOVE;
            }
        } else if (isAddAlarm(vehicleLocationQueue, dto)) {
            return HandleAlarmEnum.ADD;
        }
        return HandleAlarmEnum.IGNORE;
    }

    /**
     * 计算指定时间段内距离
     *
     * @param timeSecond
     * @return
     */
    private static double computeDistance(CircularFifoQueue<VehicleLocation> vehicleLocationQueue, int timeSecond) {
        double distance = 0;
        for (int i = 1; i < timeSecond; i++) {
            VehicleLocation start = vehicleLocationQueue.get(MAX_INDEX - i - 1);
            VehicleLocation end = vehicleLocationQueue.get(MAX_INDEX - i);
            // 近似计算,提高效率,经纬度相减,绝对值求和
            distance += Math.abs(end.getLon() - start.getLon());
            distance += Math.abs(end.getLat() - start.getLat());
        }
        return distance;
    }

    /**
     * 计算指定时间段内速度是否为0
     *
     * @param timeSecond
     * @return
     */
    private static boolean isSpeedEqualZero(CircularFifoQueue<VehicleLocation> vehicleLocationQueue, int timeSecond) {
        for (int i = 0; i < timeSecond; i++) {
            VehicleLocation start = vehicleLocationQueue.get(MAX_INDEX - i);
            if (start.getSpeed() != 0) {
                return false;
            }
        }
        return true;
    }

    /**
     * 计算指定时间段内速度是否小于某个值
     *
     * @param timeSecond
     * @return
     */
    private static boolean isSpeedLess(CircularFifoQueue<VehicleLocation> vehicleLocationQueue, int timeSecond, double speed) {
        for (int i = 0; i < timeSecond; i++) {
            VehicleLocation start = vehicleLocationQueue.get(MAX_INDEX - i);
            if (Math.abs(start.getSpeed()) > speed) {
                return false;
            }
        }
        return true;
    }

    /**
     * 是否新增告警
     *
     * @param dto
     * @return
     */
    private static boolean isAddAlarm(CircularFifoQueue<VehicleLocation> vehicleLocationQueue, LocalView.LocalViewDTO dto) {
        LocalView.PlanningDecision decision = dto.getPlanning().getDecision();
        // 在路口内属于路口遇阻,不处理
        if (decision.getIsInIntersection()) {
            return false;
        }
        // 红绿灯停车意图状态,不处理
        if (PlanningManager.isStopTrafficLight(dto)) {
            return false;
        }
        // 持续15s行驶距离小于0.25m，新增
        if (computeDistance(vehicleLocationQueue, 15) < 0.25) {
            log.info("持续15s行驶距离小于0.25m,新增遇阻停车报警!车辆={}", dto.getAuthInfo().getVehicleId());
            return true;
        }
        // 持续15S车速0.2m/s以下，新增
        if (isSpeedLess(vehicleLocationQueue, 15, 0.2)) {
            log.info("持续15s车速0.2m/s以下,新增遇阻停车报警!车辆={}", dto.getAuthInfo().getVehicleId());
            return true;
        }
        // 持续8s车速为0，新增
        if (isSpeedEqualZero(vehicleLocationQueue, 8)) {
            log.info("持续8s车速为0,新增遇阻停车报警!车辆={}", dto.getAuthInfo().getVehicleId());
            return true;
        }
        return false;
    }

    private static VehicleLocation addVehicleLocation(LocalView.LocalViewDTO dto, CircularFifoQueue<VehicleLocation> vehicleLocationQueue) {
        double x = dto.getOrigin().getX() + dto.getCar().getOrientation().getX();
        double y = dto.getOrigin().getY() + dto.getCar().getOrientation().getY();
        VehicleLocation vehicleLocation = new VehicleLocation(y, x, dto.getCar().getVelocity(), dto.getTimestamp());
        vehicleLocationQueue.add(vehicleLocation);
        return vehicleLocation;
    }

    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class VehicleLocation {

        /**
         * 纬度
         */
        private Double lat;

        /**
         * 经度
         */
        private Double lon;

        /**
         * 速度
         */
        private Double speed;

        /**
         * 记录时间,单位纳秒ns
         */
        private long recordTime;
    }
}
