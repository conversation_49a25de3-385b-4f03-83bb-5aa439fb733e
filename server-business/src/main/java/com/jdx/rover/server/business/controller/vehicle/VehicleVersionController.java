/*
 * Copyright (c) 2022-2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.server.business.controller.vehicle;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.server.api.domain.constants.KafkaTopicConstant;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleLocationDTO;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleOtaVersionInfoDTO;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleRealtimeInfoDTO;
import com.jdx.rover.server.business.service.VehicleVersionService;
import com.jdx.rover.server.domain.enums.RedisCacheEnum;
import com.jdx.rover.server.repository.redis.RedissonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * <AUTHOR>
 */
@RestController
@Slf4j
public class VehicleVersionController {

  @Autowired
  private VehicleVersionService vehicleVersionService;
  /**
   * 获取车辆版本信息
   *
   * @param vehicleName
   */
  @GetMapping(value = "/server/business/vehicle/ota_version/{vehicleName}")
  public HttpResult getVehicleVersion(@PathVariable("vehicleName") String vehicleName) {
    return HttpResult.success(vehicleVersionService.getVehicleVersion(vehicleName));
  }

}
