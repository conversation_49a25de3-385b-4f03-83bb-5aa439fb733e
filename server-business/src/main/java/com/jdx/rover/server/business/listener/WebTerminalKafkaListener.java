package com.jdx.rover.server.business.listener;

import com.jdx.rover.server.business.service.WebTerminalService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * web terminal消息接收Listener
 *
 * <AUTHOR>
 * @date  2022-01-01
 */
@Service
@Slf4j
public class WebTerminalKafkaListener {
  @Autowired
  private WebTerminalService webTerminalService;

//  @KafkaListener(topics = {KafkaTopicConstant.SERVER_WEB_TERMINAL_COMMAND_DOWN})
  public void onMessage(List<String> messageList) {
    webTerminalService.receiveList(messageList);
  }
}
