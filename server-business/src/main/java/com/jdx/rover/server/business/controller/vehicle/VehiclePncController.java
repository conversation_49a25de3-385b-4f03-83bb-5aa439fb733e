/*
 * Copyright (c) 2022-2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.server.business.controller.vehicle;

import com.jdx.rover.server.api.domain.constants.KafkaTopicConstant;
import com.jdx.rover.server.api.domain.dto.guardian.VehiclePncInfoDTO;
import com.jdx.rover.server.repository.redis.RedissonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RestController;

/**
 * 获取车辆实PNC信息
 *
 * <AUTHOR>
 */
@RestController
@Slf4j
public class VehiclePncController {
  /**
   * 获取车辆实PNC信息
   *
   * @param vehicleName 车辆名称
   * @return
   */
  @GetMapping(value = "/server/business/vehicle/pnc/{vehicleName}")
  public VehiclePncInfoDTO getVehiclePnc(@PathVariable("vehicleName") String vehicleName) {
    String redisKey = KafkaTopicConstant.SERVER_GUARDIAN_PNC + "_" + vehicleName;
    VehiclePncInfoDTO dto = RedissonUtils.getObject(redisKey);
    log.info("feign pnc vehicleName={}, result={}", vehicleName, dto);
    return dto;
  }
}
