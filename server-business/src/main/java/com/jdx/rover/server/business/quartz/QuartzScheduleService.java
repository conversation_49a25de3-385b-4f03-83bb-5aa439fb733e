package com.jdx.rover.server.business.quartz;

import com.jdx.rover.server.business.manager.metadata.MetadataVehicleApiKeyManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

/**
 * quartz定时任务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class QuartzScheduleService {
    private final MetadataVehicleApiKeyManager metadataVehicleApiKeyManager;

    /**
     * 每10分钟处理一次
     */
    @Scheduled(fixedRate = 10 * 60 * 1000)
    public void handleVehiclePosition() {
        metadataVehicleApiKeyManager.initVehicleApiKeyMap();
    }
}