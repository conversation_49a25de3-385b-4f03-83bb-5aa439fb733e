///***************************************************************************
// *
// * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
// *
// **************************************************************************/
//package com.jdx.rover.server.business.manager;
//
//import cn.hutool.core.date.DateUnit;
//import cn.hutool.core.date.DateUtil;
//import com.jdx.rover.common.utils.JsonUtils;
//import com.jdx.rover.server.api.domain.constants.KafkaTopicConstant;
//import com.jdx.rover.server.api.domain.dto.guardian.VehicleAlarmDTO;
//import com.jdx.rover.server.api.domain.dto.guardian.VehicleAlarmEventDTO;
//import com.jdx.rover.server.domain.enums.GuardianErrorEnum;
//import com.jdx.rover.server.domain.enums.GuardianErrorLevelEnum;
//import com.jdx.rover.server.domain.vo.guardian.DrivableDirectionAddVo;
//import com.jdx.rover.server.domain.vo.guardian.GuardianInfoAddVo;
//import com.jdx.rover.server.domain.vo.guardian.VehicleExceptionLogAddVo;
//import com.jdx.rover.server.domain.vo.guardian.PassNoSignalIntersectionVO;
//import com.jdx.rover.server.repository.redis.guardian.VehicleAlarmInfoRepository;
//import jdx.rover.guardian.data.dto.GuardianDataDto;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.kafka.core.KafkaTemplate;
//import org.springframework.stereotype.Service;
//import org.springframework.util.CollectionUtils;
//
//import java.util.ArrayList;
//import java.util.Comparator;
//import java.util.Date;
//import java.util.HashMap;
//import java.util.Iterator;
//import java.util.List;
//import java.util.Map;
//import java.util.Objects;
//import java.util.stream.Collectors;
//
///**
// * guardian车辆报警
// *
// * <AUTHOR> shiling
// * @version 1.0
// */
//@Service
//@Slf4j
//public class VehicleAlarmInfoManager {
//  @Autowired
//  private VehicleAlarmInfoRepository vehicleAlarmInfoRepository;
//  @Autowired
//  private KafkaTemplate<String, String> kafkaTemplate;
//
//  public static final int MAX_ALARM_INTERVAL = 30;
//
//  /**
//   * 处理告警信息
//   *
//   * @param guardianInfoAddVo guardian信息.
//   */
//  public VehicleAlarmDTO handle(GuardianInfoAddVo guardianInfoAddVo) {
//    String vehicleName = guardianInfoAddVo.getVehicleName();
//    Map<String, VehicleAlarmEventDTO> alarmMapDb = vehicleAlarmInfoRepository.get(vehicleName);
//    // 车辆启动过程中,全部报警事件都不上报,并删除缓存中所有告警
////    if (VehicleStateInfoManager.isLaunch(guardianInfoAddVo)) {
////      if (!CollectionUtils.isEmpty(alarmMapDb)) {
////        List<String> keys = Lists.newArrayList(alarmMapDb.keySet());
////        vehicleAlarmInfoRepository.remove(vehicleName, keys);
////        sendKafka(vehicleName, new HashMap<>());
////      }
////      return null;
////    }
//    Map<String, VehicleAlarmEventDTO> alarmMap = getAlarmEventMap(guardianInfoAddVo, alarmMapDb);
//    if (!CollectionUtils.isEmpty(alarmMap)) {
//      vehicleAlarmInfoRepository.save(vehicleName, alarmMap);
//    }
//    if (!isSend(alarmMap, alarmMapDb)) {
//      return null;
//    }
//    if (!CollectionUtils.isEmpty(alarmMapDb)) {
//      List<String> keys = new ArrayList<>();
//      alarmMapDb.forEach((k, v) -> {
//        if (alarmMap.containsKey(k)) {
//          return;
//        }
//        if (Objects.equals(v.getType(), GuardianErrorEnum.GUARD_EMERGENCY_STOP_ERROR_CODE_TYPE.getAlarmType())) {
//          return;
//        }
//        keys.add(k);
//      });
//      vehicleAlarmInfoRepository.remove(vehicleName, keys);
//    }
//    sendKafka(vehicleName, alarmMap);
//    return null;
//  }
//
//  /**
//   * 获取对外输出格式的告警Map
//   *
//   * @param guardianInfoAddVo
//   * @return
//   */
//  private Map<String, VehicleAlarmEventDTO> getAlarmEventMap(GuardianInfoAddVo guardianInfoAddVo, Map<String, VehicleAlarmEventDTO> alarmMapDb) {
//    Map<String, VehicleAlarmEventDTO> alarmEventMap = getAlarmEventReportMap(guardianInfoAddVo, alarmMapDb);
//    VehicleAlarmEventDTO vehicleAlarmEventRedis = getGuardEmergencyStop(alarmMapDb);
//    // 合并策略,存在老的安全接管停车,用老的告警
//    if (vehicleAlarmEventRedis != null) {
//      String category = GuardianErrorEnum.GUARD_EMERGENCY_STOP_ERROR_CODE_TYPE.getCategory();
//      VehicleAlarmEventDTO alarmEvent = alarmEventMap.get(category);
//      if (alarmEvent != null) {
//        GuardianErrorEnum guardianErrorEnum = GuardianErrorEnum.getFromAlarmType(alarmEvent.getType());
//        if (guardianErrorEnum.getPriority() >= GuardianErrorEnum.GUARD_EMERGENCY_STOP_ERROR_CODE_TYPE.getPriority()) {
//          alarmEventMap.put(category, vehicleAlarmEventRedis);
//        }
//      } else {
//        alarmEventMap.put(category, vehicleAlarmEventRedis);
//      }
//    }
//    return alarmEventMap;
//  }
//
//  /**
//   * 获取安全接管告警
//   *
//   * @param alarmMapDb
//   * @return
//   */
//  private VehicleAlarmEventDTO getGuardEmergencyStop(Map<String, VehicleAlarmEventDTO> alarmMapDb) {
//    if (CollectionUtils.isEmpty(alarmMapDb)) {
//      return null;
//    }
//    VehicleAlarmEventDTO result = alarmMapDb.values().stream().filter(
//            tmp -> Objects.equals(tmp.getType(), GuardianErrorEnum.GUARD_EMERGENCY_STOP_ERROR_CODE_TYPE.getAlarmType()))
//            .findFirst().orElse(null);
//    return result;
//  }
//
//  /**
//   * guardian车辆报警
//   */
//  private Map<String, VehicleAlarmEventDTO> getAlarmEventReportMap(GuardianInfoAddVo guardianInfoAddVo, Map<String, VehicleAlarmEventDTO> alarmMapDb) {
//    Map<String, VehicleAlarmEventDTO> resultMap = new HashMap<>();
//    for (VehicleExceptionLogAddVo vo : guardianInfoAddVo.getVehicleExceptionLogSaveRequestList()) {
//      VehicleAlarmEventDTO alarm = new VehicleAlarmEventDTO();
//      alarm.setReportTime(vo.getTimestamp());
//      GuardianErrorEnum guardianErrorEnum = GuardianErrorEnum.of(vo.getErrorCode());
//      // 未匹配上枚举,且是error级别错误,且不是接管状态,都设置为告警停车
//      if (guardianErrorEnum == null && GuardianErrorLevelEnum.FATAL.getErrorLevel().equals(vo.getErrorLevel())
//              && !VehicleStateInfoManager.isTakeOver(guardianInfoAddVo)) {
//        guardianErrorEnum = GuardianErrorEnum.ABNORMAL_STOP_ERROR_LEVE_TYPE;
//        VehicleAlarmEventDTO lastAbnormalAlarmEvent = resultMap.get(guardianErrorEnum.getCategory());
//        if (lastAbnormalAlarmEvent != null && lastAbnormalAlarmEvent.getReportTime().before(vo.getTimestamp())) {
//          alarm.setReportTime(lastAbnormalAlarmEvent.getReportTime());
//        }
//      }
//      // 未匹配上枚举,直接忽略
//      if (guardianErrorEnum == null) {
//        continue;
//      }
//      // 胎压告警,但是级别不是error,直接忽略
//      if (guardianErrorEnum.equals(GuardianErrorEnum.ERROR_DRIVER_CHASSIS_WHEEL_ALARM)
//          && !GuardianErrorLevelEnum.ERROR.getErrorLevel().equals(vo.getErrorLevel())) {
//        continue;
//      }
//      if (!isNewOrHighPriorityAlarm(resultMap, guardianErrorEnum, alarm.getReportTime())) {
//        continue;
//      }
//      if (isNotReportStuckStop(guardianInfoAddVo, guardianErrorEnum)) {
//        continue;
//      }
//      alarm.setType(guardianErrorEnum.getAlarmType());
//      alarm.setErrorCode(vo.getErrorCode());
//      alarm.setErrorLevel(vo.getErrorLevel());
//      alarm.setErrorMessage(vo.getErrorMessage());
//      Date endTIme = getEndTime(alarmMapDb, guardianErrorEnum, vo.getTimestamp());
//      alarm.setEndTime(endTIme);
//      resultMap.put(guardianErrorEnum.getCategory(), alarm);
//    }
//
//    // 如果前后使能无效,设置为安全接管停车
//    if (isNotDrivable(guardianInfoAddVo.getDrivableDirectionRequest())
//            && VehicleStateInfoManager.hasPlanRoute(guardianInfoAddVo)
//            && VehicleStateInfoManager.isRemoteControl(guardianInfoAddVo)) {
//      if (isNewOrHighPriorityAlarm(resultMap, GuardianErrorEnum.GUARD_EMERGENCY_STOP_ERROR_CODE_TYPE, guardianInfoAddVo.getReportTime())) {
//        putAlarmEventMap(resultMap, alarmMapDb, GuardianErrorEnum.GUARD_EMERGENCY_STOP_ERROR_CODE_TYPE
//                , guardianInfoAddVo.getReportTime(), guardianInfoAddVo.getReportTime());
//      }
//    }
//
//    // 底盘为DRIVEMODE_TAKEOVER_EMERGENCY_STOP 车端拍停,优先级最高,放在最后面
//    if (guardianInfoAddVo.getVehicleStatusRequest() != null
//        && GuardianErrorEnum.BUTTON_EMERGENCY_STOP_TYPE.getErrorCode()
//        .equals(guardianInfoAddVo.getVehicleStatusRequest().getChassisStatus()) && VehicleStateInfoManager.hasPlanRoute(guardianInfoAddVo)) {
//      putAlarmEventMap(resultMap, alarmMapDb, GuardianErrorEnum.BUTTON_EMERGENCY_STOP_TYPE
//          , guardianInfoAddVo.getVehicleStatusRequest().getLastUpdateTimeForChassis(), guardianInfoAddVo.getReportTime());
//    }
//
//    // 无保护左转报警
//    passNoSignalIntersectionAlarm(guardianInfoAddVo, resultMap, alarmMapDb);
//
//    // 比较redis和当前时间,取最早时间
//    resultMap.values().forEach(v -> {
//      Date startTime = getStartTime(alarmMapDb, v);
//      v.setReportTime(startTime);
//    });
//    return resultMap;
//  }
//
//  /**
//   * 是否不可以驾驶
//   *
//   * @param drivable
//   * @return
//   */
//  private boolean isNotDrivable(DrivableDirectionAddVo drivable) {
//    if (drivable == null) {
//      return false;
//    }
//    if (Objects.equals(drivable.getEnableFront(), Boolean.TRUE)) {
//      return false;
//    }
//    if (Objects.equals(drivable.getEnableBack(), Boolean.TRUE)) {
//      return false;
//    }
//    return true;
//  }
//
//  /**
//   * 增加告警事件map
//   *
//   * @param alarmEventMap
//   * @param guardianErrorEnum
//   * @param reportTime
//   */
//  private void putAlarmEventMap(Map<String, VehicleAlarmEventDTO> alarmEventMap, Map<String, VehicleAlarmEventDTO> alarmMapDb
//      , GuardianErrorEnum guardianErrorEnum, Date reportTime, Date latestReportTime) {
//    VehicleAlarmEventDTO vehicleAlarmEvent = new VehicleAlarmEventDTO();
//    vehicleAlarmEvent.setType(guardianErrorEnum.getAlarmType());
//    vehicleAlarmEvent.setReportTime(reportTime);
//    vehicleAlarmEvent.setErrorCode(guardianErrorEnum.getErrorCode());
//    Date endTime = getEndTime(alarmMapDb, guardianErrorEnum, latestReportTime);
//    vehicleAlarmEvent.setEndTime(endTime);
//    alarmEventMap.put(guardianErrorEnum.getCategory(), vehicleAlarmEvent);
//  }
//
//  /**
//   * 是否为新告警或者高优先级告警
//   *
//   * @param alarmEventMap
//   * @param guardianErrorEnum
//   * @param reportTime
//   * @return
//   */
//  private boolean isNewOrHighPriorityAlarm(Map<String, VehicleAlarmEventDTO> alarmEventMap, GuardianErrorEnum guardianErrorEnum, Date reportTime) {
//    VehicleAlarmEventDTO lastAlarmEvent = alarmEventMap.get(guardianErrorEnum.getCategory());
//    if (lastAlarmEvent == null) {
//      return true;
//    }
//    GuardianErrorEnum lastGuardianErrorEnum = GuardianErrorEnum.getFromAlarmType(lastAlarmEvent.getType());
//    // 新告警优先级低靠后,直接忽略
//    if (lastGuardianErrorEnum.getPriority() < guardianErrorEnum.getPriority()) {
//      return false;
//    }
//    // 新告警优先级相同,时间靠后,直接忽略
//    if (lastGuardianErrorEnum.getPriority().equals(guardianErrorEnum.getPriority())
//        && lastAlarmEvent.getReportTime().before(reportTime)) {
//      return false;
//    }
//    return true;
//  }
//
//  /**
//   * 是否忽略遇阻停车
//   *
//   * @param guardianInfoAddVo
//   * @param guardianErrorEnum
//   * @return
//   */
//  private boolean isNotReportStuckStop(GuardianInfoAddVo guardianInfoAddVo, GuardianErrorEnum guardianErrorEnum) {
//    if (!GuardianErrorEnum.STUCK_ERROR_CODE_TYPE.equals(guardianErrorEnum)) {
//      return false;
//    }
//    if (VehicleStateInfoManager.isTakeOver(guardianInfoAddVo)) {
//      return true;
//    }
//    if (!VehicleStateInfoManager.hasPlanRoute(guardianInfoAddVo)) {
//      return true;
//    }
//    return false;
//  }
//
//  /**
//   * 处理所有停止上报告警
//   */
//  public void handStopAlarmException() {
//    Map<String, Map<String, VehicleAlarmEventDTO>> map = vehicleAlarmInfoRepository.getAll();
//    for (Map.Entry<String, Map<String, VehicleAlarmEventDTO>> entry : map.entrySet()) {
//      handStopAlarmException(entry.getKey(), entry.getValue());
//    }
//  }
//
//  /**
//   * 处理停止上报的告警信息
//   *
//   * @param vehicleName
//   * @param alarmMapDb
//   */
//  public void handStopAlarmException(String vehicleName, Map<String, VehicleAlarmEventDTO> alarmMapDb) {
//    if (CollectionUtils.isEmpty(alarmMapDb)) {
//      return;
//    }
//    boolean isSend = false;
//    Date now = new Date();
//    Iterator<Map.Entry<String, VehicleAlarmEventDTO>> it = alarmMapDb.entrySet().iterator();
//    while (it.hasNext()) {
//      Map.Entry<String, VehicleAlarmEventDTO> item = it.next();
//      VehicleAlarmEventDTO vehicleAlarmEventDTO = item.getValue();
//      // 目前只支持安全接管急停的定时清除
//      if (!Objects.equals(GuardianErrorEnum.GUARD_EMERGENCY_STOP_ERROR_CODE_TYPE.getAlarmType(), vehicleAlarmEventDTO.getType())) {
//        continue;
//      }
//      GuardianErrorEnum guardianErrorEnum = GuardianErrorEnum.getFromAlarmType(vehicleAlarmEventDTO.getType());
//      long time = DateUtil.between(now, vehicleAlarmEventDTO.getEndTime(), DateUnit.SECOND);
//      if (time < MAX_ALARM_INTERVAL) {
//        continue;
//      }
//      long result = vehicleAlarmInfoRepository.remove(vehicleName, guardianErrorEnum.getCategory());
//      if (result == 0) {
//        log.info("redis已经被删除,不发送异常kafka,车辆={},异常={}", vehicleName, vehicleAlarmEventDTO);
//        continue;
//      }
//      isSend = true;
//      it.remove();
//    }
//    if (!isSend) {
//      return;
//    }
//    sendKafka(vehicleName, alarmMapDb);
//  }
//
//  /**
//   * 发送kafka消息
//   *
//   * @param vehicleName
//   * @param alarmMapDb
//   */
//  private void sendKafka(String vehicleName, Map<String, VehicleAlarmEventDTO> alarmMapDb) {
//    VehicleAlarmDTO alarmEntity = new VehicleAlarmDTO();
//    alarmEntity.setVehicleName(vehicleName);
//    alarmEntity.setRecordTime(new Date());
//    List<VehicleAlarmEventDTO> alarmEventList = alarmMapDb.values().stream()
//            .sorted(Comparator.comparing(tmp -> {
//              GuardianErrorEnum guardianErrorEnum = GuardianErrorEnum.getFromAlarmType(tmp.getType());
//              return guardianErrorEnum.getPriority();
//            }))
//            .collect(Collectors.toList());
//    alarmEntity.setAlarmEventList(alarmEventList);
//    String jsonStr = JsonUtils.writeValueAsString(alarmEntity);
//    kafkaTemplate.send(KafkaTopicConstant.SERVER_GUARDIAN_ALARM, vehicleName, jsonStr);
//    log.info("发送告警消息={}", jsonStr);
//  }
//
//  /**
//   * 获取开始时间
//   *
//   * @param alarmMapDb
//   * @return
//   */
//  private Date getStartTime(Map<String, VehicleAlarmEventDTO> alarmMapDb, VehicleAlarmEventDTO alarmEvent) {
//    Date reportTime = alarmEvent.getReportTime();
//    if (CollectionUtils.isEmpty(alarmMapDb)) {
//      return reportTime;
//    }
//    GuardianErrorEnum guardianErrorEnum = GuardianErrorEnum.getFromAlarmType(alarmEvent.getType());
//    String key = guardianErrorEnum.getCategory();
//    VehicleAlarmEventDTO alarmDb = alarmMapDb.get(key);
//    if (alarmDb == null) {
//      return reportTime;
//    }
//    Date startTimeDb = alarmDb.getReportTime();
//    if (startTimeDb == null) {
//      return reportTime;
//    }
//    if (reportTime.before(startTimeDb)) {
//      return reportTime;
//    }
//    if (!Objects.equals(alarmEvent.getType(), alarmDb.getType())) {
//      return reportTime;
//    }
//    return startTimeDb;
//  }
//
//  /**
//   * 获取结束时间
//   *
//   * @param alarmMapDb
//   * @return
//   */
//  private Date getEndTime(Map<String, VehicleAlarmEventDTO> alarmMapDb, GuardianErrorEnum guardianErrorEnum, Date reportTime) {
//    Date endTime = reportTime;
//    Date now = new Date();
//    long time = DateUtil.between(now, endTime, DateUnit.SECOND);
//    // 结束时间和当前时间相差MAX_ABNORMAL_INTERVAL分钟以上,结束时间取当前时间,防止积压插入太多数据
//    if (time >= MAX_ALARM_INTERVAL) {
//      endTime = now;
//    }
//    if (CollectionUtils.isEmpty(alarmMapDb)) {
//      return endTime;
//    }
//    String key = guardianErrorEnum.getCategory();
//    VehicleAlarmEventDTO alarmDb = alarmMapDb.get(key);
//    if (alarmDb == null) {
//      return endTime;
//    }
//    Date endTimeDb = alarmDb.getEndTime();
//    if (endTimeDb == null) {
//      return endTime;
//    }
//    if (endTimeDb.before(endTime)) {
//      return endTime;
//    }
//    if (!Objects.equals(alarmDb.getType(), guardianErrorEnum.getAlarmType())) {
//      return reportTime;
//    }
//    return endTimeDb;
//  }
//
//  private boolean isSend(Map<String, VehicleAlarmEventDTO> alarmMap, Map<String, VehicleAlarmEventDTO> alarmMapDb) {
//    // 同时为空不发送,否则一个为空发送
//    if (CollectionUtils.isEmpty(alarmMap) && CollectionUtils.isEmpty(alarmMapDb)) {
//      return false;
//    }
//    if (CollectionUtils.isEmpty(alarmMap)) {
//      return true;
//    }
//    if (CollectionUtils.isEmpty(alarmMapDb)) {
//      return true;
//    }
//    if(alarmMap.size() != alarmMapDb.size()){
//      return true;
//    }
//    for (Map.Entry<String, VehicleAlarmEventDTO> entry : alarmMap.entrySet()) {
//      String key = entry.getKey();
//      if (!alarmMapDb.containsKey(key)) {
//        return true;
//      }
//      if (!Objects.equals(entry.getValue().getType(), alarmMapDb.get(key).getType())) {
//        return true;
//      }
//    }
//    return false;
//  }
//
//  /**
//   * 无保护左转报警
//   *
//   * @param guardianInfoAddVo
//   * @param alarmMap
//   */
//  private void passNoSignalIntersectionAlarm(GuardianInfoAddVo guardianInfoAddVo, Map<String, VehicleAlarmEventDTO> alarmMap, Map<String, VehicleAlarmEventDTO> alarmMapDb) {
//    PassNoSignalIntersectionVO noSignal = guardianInfoAddVo.getNavigationPncStatusRequest().getPlanningStatus().getPassNoSignalIntersection();
//    if (noSignal == null || noSignal.getStatus() == null) {
//      return;
//    }
//    String status = noSignal.getStatus();
//    if (Objects.equals(status, GuardianDataDto.PassNoSignalIntersectionStatus.Status.READY.name())
//            || Objects.equals(status, GuardianDataDto.PassNoSignalIntersectionStatus.Status.RUNNING.name())) {
//      putAlarmEventMap(alarmMap, alarmMapDb, GuardianErrorEnum.PASS_NO_SIGNAL_INTERSECTION
//              , guardianInfoAddVo.getReportTime(), guardianInfoAddVo.getReportTime());
//    }
//  }
//}
