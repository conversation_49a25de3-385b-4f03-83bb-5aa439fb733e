/*
 * Copyright (c) 2025 www.jd.com All rights reserved.
 */

package com.jdx.rover.server.business.manager.report;

import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.server.api.domain.dto.status.ChangeStatusDTO;
import com.jdx.rover.server.api.domain.dto.status.ChangeStatusDTO.ChangeTypeEnum;
import com.jdx.rover.server.business.config.ProducerTopicProperties;
import com.jdx.rover.server.common.utils.date.ServerDateUtils;
import com.jdx.rover.server.domain.dto.vehicle.VehicleStatusDTO;
import com.jdx.rover.server.repository.jmq.JmqProducerManager;
import com.jdx.rover.server.repository.redis.guardian.VehicleStatusRepository;
import java.util.Objects;
import jdx.rover.report.dto.proto.ReportDto;
import jdx.rover.report.dto.proto.ReportHeader;
import jdx.rover.report.velocity.proto.ReportVelocity;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/8/7 13:54
 * @description 车辆限速管理
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class VehicleMaxVelocityManager {

    /**
     * VehicleStatusRepository
     */
    private final VehicleStatusRepository vehicleStatusRepository;

    /**
     * jmq消息发送producer
     */
    private final JmqProducerManager jmqProducerManager;

    /**
     * 生产主题配置类
     */
    private final ProducerTopicProperties producerTopicProperties;

    /**
     * 处理限速信息
     *
     * @param reportDTO reportDTO
     * @param messageTypeRequest messageTypeRequest
     */
    public void handle(ReportDto.ReportDTO reportDTO, ReportHeader.MessageTypeRequest messageTypeRequest) {
        if (!ReportHeader.MessageType.VELOCITY.equals(messageTypeRequest.getMessageType())) {
            return;
        }

        ReportVelocity.ReportVelocityData dto = reportDTO.getReportVelocityData();
        String vehicleName = reportDTO.getRequestHeader().getVehicleName();
        Double currentMaxVelocity = dto.getMaxVelocity();
        VehicleStatusDTO vehicleStatusDTO = vehicleStatusRepository.get(vehicleName);
        if (!Objects.isNull(vehicleStatusDTO) && Objects.equals(vehicleStatusDTO.getReportMaxVelocity(), currentMaxVelocity)) {
            return;
        }

        // 发送变更消息
        ChangeStatusDTO changeStatusDTO = new ChangeStatusDTO();
        changeStatusDTO.setVehicleName(vehicleName);
        changeStatusDTO.setRecordTime(ServerDateUtils.getDateFromNanosecond(reportDTO.getRequestHeader().getRequestTime()));
        changeStatusDTO.setOldValue((Objects.isNull(vehicleStatusDTO) || Objects.isNull(vehicleStatusDTO.getReportMaxVelocity())) ? "" : String.valueOf(vehicleStatusDTO.getReportMaxVelocity()));
        changeStatusDTO.setNewValue(String.valueOf(currentMaxVelocity));
        changeStatusDTO.setChangeType(ChangeTypeEnum.MAX_VELOCITY.name());
        String jsonStr = JsonUtils.writeValueAsString(changeStatusDTO);
        jmqProducerManager.sendOrdered(producerTopicProperties.getServerVehicleChangeStatus(), jsonStr, vehicleName);
        log.info("handle speedLimit send jmq: [{}]", jsonStr);

        // 更新缓存
        vehicleStatusRepository.putMapValue(vehicleName, VehicleStatusDTO::getReportMaxVelocity, String.valueOf(currentMaxVelocity));
    }
}
