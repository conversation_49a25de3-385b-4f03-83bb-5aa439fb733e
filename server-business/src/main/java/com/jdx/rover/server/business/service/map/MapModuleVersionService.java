package com.jdx.rover.server.business.service.map;

import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.server.api.domain.guardian.VehicleVersionInfoEntity;
import com.jdx.rover.server.repository.redis.guardian.VehicleVersionInfoRepository;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * 地图模块版本服务
 *
 * <AUTHOR>
 * @date 2024/3/16
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class MapModuleVersionService {

    /**
     * 车辆版本仓库
     */
    private final VehicleVersionInfoRepository vehicleVersionInfoRepository;

    /**
     * 处理单条kafka消息
     */
    public void handleOneMessage(String message) {
        MapModuleVersion mapModuleVersion = JsonUtils.readValue(message, MapModuleVersion.class);
        if (mapModuleVersion == null) {
            return;
        }
        VehicleVersionInfoEntity vehicleVersionInfoEntity = new VehicleVersionInfoEntity();
        vehicleVersionInfoEntity.setModuleName(mapModuleVersion.getModuleName() + "-" + mapModuleVersion.getNodeName());
        vehicleVersionInfoEntity.setCurVersion(mapModuleVersion.getCurVersion());
        Map<String, VehicleVersionInfoEntity> map = new HashMap<>();
        map.put(vehicleVersionInfoEntity.getModuleName(), vehicleVersionInfoEntity);
        vehicleVersionInfoRepository.saveValue(mapModuleVersion.vehicleName, map);
    }

    @Data
    @NoArgsConstructor
    public static class MapModuleVersion {
        /**
         * 车号
         */
        private String vehicleName;

        /**
         * 模块名称
         */
        private String moduleName;

        /**
         * 节点名称
         */
        private String nodeName;

        /**
         * 当前版本
         */
        private String curVersion;

    }
}
