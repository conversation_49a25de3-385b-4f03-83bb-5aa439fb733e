package com.jdx.rover.server.business.service;

import com.jd.jmq.common.message.Message;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.server.api.domain.vo.webterminal.WebTerminalRequestVO;
import com.jdx.rover.server.domain.constants.RedisTopicConstant;
import com.jdx.rover.server.domain.entity.VehicleManualRebootEntity;
import com.jdx.rover.server.domain.enums.RebootTypeEnum;
import com.jdx.rover.server.domain.enums.RedisCacheEnum;
import com.jdx.rover.server.repository.redis.RedissonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;

/**
 * web terminal 消息处理
 *
 * <AUTHOR>
 * @date  2022-01-01
 */
@Service
@Slf4j
public class WebTerminalService {
    private static final String ROVER_RESTART = "sudo systemctl restart rover5";

    public void receiveList(List<String> messageList) {
        log.info("web terminal send vehicle kafka messageList:{}", JsonUtils.writeValueAsString(messageList));
        if (CollectionUtils.isEmpty(messageList)) {
            return;
        }
        for (String message : messageList) {
            try {
                WebTerminalRequestVO requestVO = JsonUtils.readValue(message, WebTerminalRequestVO.class);
                sendVehicle(requestVO);
                saveCommand(requestVO);
            } catch (Exception e) {
                log.error("web terminal 处理失败", e);
            }
        }
    }

    public void onMessage(List<Message> messageList) {
        if (CollectionUtils.isEmpty(messageList)) {
            return;
        }
        if (messageList.size() > 1) {
            log.info("Received topic={}, size={}", messageList.get(0).getTopic(), messageList.size());
        }
        for (Message message : messageList) {
            try {
                log.info("Received topic={}, message={}", message.getTopic(), message.getText());
                WebTerminalRequestVO requestVO = JsonUtils.readValue(message.getText(), WebTerminalRequestVO.class);
                sendVehicle(requestVO);
                saveCommand(requestVO);
            } catch (Exception e) {
                log.error("处理消息失败!{}", message, e);
            }
        }
    }

    private void saveCommand(WebTerminalRequestVO requestVO) {
        if (requestVO.getData() instanceof String) {
            if (StringUtils.contains((String) requestVO.getData(), ROVER_RESTART)) {
                VehicleManualRebootEntity rebootEntity = new VehicleManualRebootEntity();
                rebootEntity.setVehicleName(requestVO.getHeader().getVehicleName());
                rebootEntity.setRecordTime(new Date());
                rebootEntity.setType(RebootTypeEnum.WEBTERMINAL.getType());
                RedissonUtils.setObject(RedisCacheEnum.VEHICLE_RESTART_ROVER.getKey(requestVO.getHeader().getVehicleName()), rebootEntity, 300);
            }
        }
    }

    /**
     * 发送车端
     *
     * @param requestVO
     */
    public void sendVehicle(WebTerminalRequestVO requestVO) {
        String jsonMsg = JsonUtils.writeValueAsString(requestVO);
        String topicName = String.format(RedisTopicConstant.SERVER_TERMINAL_PREFIX, requestVO.getHeader().getVehicleName(), requestVO.getHeader().getTerminalName());
        Long result = RedissonUtils.getRTopic(topicName).publish(jsonMsg);
        log.info("web terminal kafka send monitor result:{}, responseDTO:{}", result, jsonMsg);
    }
}
