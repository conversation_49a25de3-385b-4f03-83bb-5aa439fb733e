package com.jdx.rover.server.business.service.alarm;

import java.util.List;
import java.util.Objects;
import jdx.rover.guardian.dto.LocalView;
import jdx.rover.guardian.dto.LocalView.BehaviorDecision;
import jdx.rover.guardian.dto.LocalView.BehaviorState;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @date 2024/9/9 20:42
 * @description 停靠困难
 */
@Slf4j
public class ParkHardService {

    /**
     * 是否上报停靠困难告警
     *
     * @param dto LocalView.LocalViewDTO
     * @return boolean
     */
    public static boolean isReportAlarm(LocalView.LocalViewDTO dto) {
        List<BehaviorDecision> decisionList = dto.getPlanning().getDecision().getBehaviorDecisionList().getBehaviorDecisionList();
        if (CollectionUtils.isEmpty(decisionList)) {
            return false;
        }

        for (LocalView.BehaviorDecision behaviorDecision : decisionList) {
            if (behaviorDecision == null) {
                continue;
            }
            if (Objects.equals(behaviorDecision.getState().name(), BehaviorState.PARK_HARD.name())) {
                return true;
            }
        }
        return false;
    }
}
