package com.jdx.rover.server.business.job;

import com.wangyin.schedule.client.job.ScheduleContext;
import com.wangyin.schedule.client.job.ScheduleFlowTask;
import com.wangyin.schedule.client.job.TaskResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class StopReportAlarmJob implements ScheduleFlowTask {

//  @Autowired
//  private VehicleAlarmInfoManager vehicleAlarmInfoManager;

  @Override
  public TaskResult doTask(ScheduleContext scheduleContext) {
//    vehicleAlarmInfoManager.handStopAlarmException();
//    log.info("处理关闭停止上报告警完成!");
    return TaskResult.success();
  }
}