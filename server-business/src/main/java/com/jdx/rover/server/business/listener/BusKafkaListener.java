package com.jdx.rover.server.business.listener;

import com.jdx.rover.server.business.service.BusService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 车辆上报总线数据Kafka监听
 *
 * <AUTHOR>
 * @date  2022-01-01
 */
@Service
@Slf4j
public class BusKafkaListener {

  @Autowired
  private BusService busService;

//  @KafkaListener(topics = {"server_bus_origin"}, containerFactory = "byteKafkaContainerFactory")
  public void onMessage(List<byte[]> messageList) {
    if (CollectionUtils.isEmpty(messageList)) {
      return;
    }
    for (byte[] message : messageList) {
      busService.receive(message);
    }
  }
}