/***************************************************************************
 *
 * Copyright (c) 2023 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/
package com.jdx.rover.server.business.manager.localview;

import jdx.rover.guardian.dto.LocalView;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;


/**
 * planning
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
public class PlanningManager {
    /**
     * 是否看灯(红绿灯停车意图状态)
     */
    public static boolean isStopTrafficLight(LocalView.LocalViewDTO dto) {
        List<LocalView.BehaviorDecision> decisionList = dto.getPlanning().getDecision().getBehaviorDecisionList().getBehaviorDecisionList();
        if (CollectionUtils.isEmpty(decisionList)) {
            return false;
        }

        for (LocalView.BehaviorDecision behaviorDecision : decisionList) {
            if (behaviorDecision == null) {
                continue;
            }
            if (Objects.equals(behaviorDecision.getState().name(), LocalView.BehaviorState.STOP_TRAFFIC_LIGHT.name())) {
                return true;
            }
        }
        return false;
    }
}
