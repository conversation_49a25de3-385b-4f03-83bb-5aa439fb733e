/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/
package com.jdx.rover.server.business.manager;

import com.jdx.rover.server.api.domain.dto.guardian.VehiclePncInfoDTO;
import com.jdx.rover.server.api.domain.guardian.VehicleStateInfoEntity;
import com.jdx.rover.server.domain.enums.VehicleDriveModeEnum;
import com.jdx.rover.server.domain.vo.guardian.*;
import com.jdx.rover.server.repository.redis.guardian.VehicleStateInfoRepository;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <p>
 * This is a guardian state info manager which contains operations on redis.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
public class VehicleStateInfoManager {

    /**
     * <p>
     * Presents the vehicle state info redis repository.
     * </p>
     */
    @Autowired
    private VehicleStateInfoRepository vehicleStateInfoRepository;

    /**
     * <p>
     * launch scucess type.
     * </p>
     */
    private static final String LAUNCH_SUCCESS_TYPE = "START_SUCCESS";


    /**
     * <p>
     * Get supervisor real-time vehicle system state by GuardianInfoAddVo.
     * </p>
     *
     * @param guardianInfoAddVo The guardian info.
     * @return status of supervisor real time system.
     */
    public void buildVehicleStateInfoEntity(String vehicleName, GuardianInfoAddVo guardianInfoAddVo) {
        VehicleStateInfoEntity stateInfoEntity = new VehicleStateInfoEntity();
        stateInfoEntity.setRecordTime(guardianInfoAddVo.getReportTime());
        VehicleStatusAddVo vehicleStatus = guardianInfoAddVo.getVehicleStatusRequest();
        if (vehicleStatus == null || vehicleStatus.getChassisStatus() == null) {
            stateInfoEntity.setVehicleState(VehicleDriveModeEnum.UNKNOWN.getVehicleState());
        }
        String vehicleState = getVehicleDriveMode(guardianInfoAddVo);
        stateInfoEntity.setVehicleState(vehicleState);
        if (VehicleDriveModeEnum.INITIALIZATION.getVehicleState().equals(vehicleState)) {
            stateInfoEntity.setVehicleStateMessage(getVehicleStateMessage(guardianInfoAddVo.getLaunchStatusSaveRequestList()));
        }
        vehicleStateInfoRepository.saveValue(vehicleName, stateInfoEntity);
    }

    /**
     * <p>
     * Get supervisor real-time vehicle state by GuardianInfoAddVo.
     * </p>
     *
     * @param guardianInfoAddVo The guardian info in redis.
     * @return status of vehicle real time .
     */
    public String getVehicleDriveMode(GuardianInfoAddVo guardianInfoAddVo) {
        VehicleStatusAddVo vehicleStatus = guardianInfoAddVo.getVehicleStatusRequest();
        if (vehicleStatus == null || vehicleStatus.getChassisStatus() == null) {
            return VehicleDriveModeEnum.UNKNOWN.getVehicleState();
        }
        boolean isLaunchInit = isLaunch(guardianInfoAddVo);
        return isLaunchInit ? VehicleDriveModeEnum.INITIALIZATION.getVehicleState() :
                VehicleDriveModeEnum.getByDriveMode(vehicleStatus.getChassisStatus()).getVehicleState();
    }

    /**
     * <p>
     * Get real-time vehicle state message by GuardianInfoAddVo.
     * </p>
     *
     * @param launchStatusAddVos The guardian info in redis.
     * @return message of vehicle state .
     */
    private String getVehicleStateMessage(List<LaunchStatusAddVo> launchStatusAddVos) {
        StringBuilder stateMessage = new StringBuilder();
        launchStatusAddVos.stream().forEach(launchStatusAddVo -> {
            stateMessage.append(launchStatusAddVo.getIp()).append(" : ").append(
                    "starting: ").append(launchStatusAddVo.getCurrentStartingModuleName()).append(";").append(" left: ");
            launchStatusAddVo.getRequiredModuleList().forEach(leftModule -> stateMessage.append(leftModule).append(" "));
            stateMessage.append(";");
        });
        return stateMessage.toString();
    }

    /**
     * 是否正在启动
     */
    public static boolean isLaunch(GuardianInfoAddVo guardianInfoAddVo) {
        List<LaunchStatusAddVo> launchStatusList = guardianInfoAddVo.getLaunchStatusSaveRequestList();
        if (CollectionUtils.isEmpty(launchStatusList)) {
            return true;
        }

        boolean result = launchStatusList.stream().anyMatch(
                launchStatusAddVo -> !LAUNCH_SUCCESS_TYPE.equals(launchStatusAddVo.getState()));
        return result;
    }

    /**
     * 是否有规划任务
     */
    public static boolean hasPlanRoute(GuardianInfoAddVo guardianInfoAddVo) {
        NavigationRoutingStatus routingStatus = guardianInfoAddVo.getNavigationPncStatusRequest().getRoutingStatus();
        return routingStatus.getRoutingId() != null && routingStatus.getRoutingId() > 0;
    }

    public static boolean hasPlanRoute(VehiclePncInfoDTO vehiclePncInfoDTO) {
        if (vehiclePncInfoDTO == null || !StringUtils.equalsAny(vehiclePncInfoDTO.getType(), "ACTIVE", "ACCOMPLISHED")) {
            return false;
        }
        return true;
    }

    public static boolean hasPlaning(VehiclePncInfoDTO vehiclePncInfoDTO) {
        if (vehiclePncInfoDTO == null || !StringUtils.equals("ACTIVE", vehiclePncInfoDTO.getType())) {
            return false;
        }
        return true;
    }

    /**
     * 是否远程遥控(远程控制、强制遥控)
     */
    public static boolean isRemoteControl(GuardianInfoAddVo guardianInfoAddVo) {
        String chassisStatus = guardianInfoAddVo.getVehicleStatusRequest().getChassisStatus();
        return isRemoteControl(chassisStatus);
    }

    /**
     * 是否远程遥控(远程控制、强制遥控)
     */
    public static boolean isRemoteControl(String driveMode) {
        return StringUtils.equalsAny(driveMode, VehicleDriveModeEnum.REMOTE_CONTROL.getDriveMode()
                , VehicleDriveModeEnum.REMOTE_SUPER_CTRL.getDriveMode()
                , VehicleDriveModeEnum.REMOTE_JOYSTICK_CTRL.getDriveMode()
                , VehicleDriveModeEnum.REMOTE_JOYSTICK_SUPER_CTRL.getDriveMode());
    }

    /**
     * 是否接管(远程控制、强制遥控、XBOX控制、手柄控制)
     */
    public static boolean isTakeOver(GuardianInfoAddVo guardianInfoAddVo) {
        String chassisStatus = guardianInfoAddVo.getVehicleStatusRequest().getChassisStatus();
        return isTakeOver(chassisStatus);
    }

    /**
     * 是否接管(远程控制、强制遥控、XBOX控制、手柄控制)
     */
    public static boolean isTakeOver(String driveMode) {
        return StringUtils.equalsAny(driveMode, VehicleDriveModeEnum.REMOTE_CONTROL.getDriveMode()
                , VehicleDriveModeEnum.REMOTE_SUPER_CTRL.getDriveMode()
                , VehicleDriveModeEnum.XBOX_CTRL.getDriveMode()
                , VehicleDriveModeEnum.LOCAL_CONTROL.getDriveMode()
                , VehicleDriveModeEnum.REMOTE_MOBILE_CTRL.getDriveMode()
                , VehicleDriveModeEnum.REMOTE_JOYSTICK_CTRL.getDriveMode()
                , VehicleDriveModeEnum.REMOTE_JOYSTICK_SUPER_CTRL.getDriveMode());
    }

    /**
     * 是否接管(远程控制、强制遥控、XBOX控制、手柄控制)
     */
    public static boolean isVehicleStateTakeOver(String vehicleState) {
        return StringUtils.equalsAny(vehicleState, VehicleDriveModeEnum.REMOTE_CONTROL.getVehicleState()
                , VehicleDriveModeEnum.REMOTE_SUPER_CTRL.getVehicleState()
                , VehicleDriveModeEnum.XBOX_CTRL.getVehicleState()
                , VehicleDriveModeEnum.LOCAL_CONTROL.getVehicleState()
                , VehicleDriveModeEnum.REMOTE_MOBILE_CTRL.getVehicleState()
                , VehicleDriveModeEnum.REMOTE_JOYSTICK_CTRL.getVehicleState()
                , VehicleDriveModeEnum.REMOTE_JOYSTICK_SUPER_CTRL.getVehicleState());
    }

    /**
     * 前后使能为false
     */
    public static boolean disableFrontAndBack(GuardianInfoAddVo guardianInfoAddVo) {
        return !guardianInfoAddVo.getDrivableDirectionRequest().getEnableFront()
                && !guardianInfoAddVo.getDrivableDirectionRequest().getEnableBack();
    }
}
