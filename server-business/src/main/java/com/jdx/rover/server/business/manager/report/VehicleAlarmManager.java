/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/
package com.jdx.rover.server.business.manager.report;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.google.common.collect.Lists;
import com.jdx.rover.common.constant.NumberConstant;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.server.api.domain.constants.KafkaTopicConstant;
import com.jdx.rover.server.api.domain.dto.guardian.PassNoSignalIntersectionDTO;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleAlarmDTO;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleAlarmEventDTO;
import com.jdx.rover.server.api.domain.dto.guardian.VehiclePncInfoDTO;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleRealtimeInfoDTO;
import com.jdx.rover.server.api.domain.dto.report.abnormal.VehicleAbnormalDTO;
import com.jdx.rover.server.api.domain.enums.guardian.AlarmTypeEnum;
import com.jdx.rover.server.api.domain.enums.report.BootStatusEnum;
import com.jdx.rover.server.business.config.ProducerTopicProperties;
import com.jdx.rover.server.business.manager.VehicleStateInfoManager;
import com.jdx.rover.server.domain.dto.vehicle.ReportBootDTO;
import com.jdx.rover.server.domain.dto.vehicle.VehicleStatusDTO;
import com.jdx.rover.server.domain.enums.GuardianErrorEnum;
import com.jdx.rover.server.domain.enums.GuardianErrorLevelEnum;
import com.jdx.rover.server.repository.jmq.JmqProducerManager;
import com.jdx.rover.server.repository.redis.RedissonUtils;
import com.jdx.rover.server.repository.redis.guardian.ReportAbnormalRepository;
import com.jdx.rover.server.repository.redis.guardian.ReportAlarmRepository;
import com.jdx.rover.server.repository.redis.guardian.VehiclePncRepository;
import com.jdx.rover.server.repository.redis.guardian.VehicleRealtimeInfoRepository;
import com.jdx.rover.server.repository.redis.guardian.VehicleStatusRepository;
import com.jdx.rover.server.repository.redis.report.ReportBootRepository;
import jdx.rover.guardian.data.dto.GuardianDataDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 收到异常信息,对外发送kafka消息
 *
 * <AUTHOR>
 * @version 1.0
 * @date  2022-01-01
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class VehicleAlarmManager {
    private final KafkaTemplate<String, String> kafkaTemplate;
    private final ReportAlarmRepository reportAlarmRepository;
    private final ReportAbnormalRepository reportAbnormalRepository;
    private final VehiclePncRepository vehiclePncRepository;
    private final ReportBootRepository reportBootRepository;
    private final VehicleRealtimeInfoRepository vehicleRealtimeInfoRepository;
    private final VehicleStatusRepository vehicleStatusRepository;
    /**
     * jmq消息发送producer
     */
    private final JmqProducerManager jmqProducerManager;

    /**
     * 生产主题配置类
     */
    private final ProducerTopicProperties producerTopicProperties;

    public static final int MAX_ALARM_INTERVAL = 30;

    public void sendAllAlarm(String vehicleName) {
        Map<String, VehicleAbnormalDTO> abnormalMap = reportAbnormalRepository.get(vehicleName);
        saveAlarmRedis(Lists.newArrayList(abnormalMap.values()));
    }

    /**
     * 新增一个告警
     */
    public void addAlarm(String vehicleName, String alarmType, Date reportTime, Map<String, VehicleAlarmEventDTO> alarmMap) {
        if (!CollectionUtils.isEmpty(alarmMap) && alarmMap.containsKey(alarmType)) {
            return;
        }
        VehicleAlarmEventDTO alarm = new VehicleAlarmEventDTO();
        alarm.setType(alarmType);
        alarm.setReportTime(reportTime);
        alarm.setErrorCode(alarmType);
        addAlarm(vehicleName, alarm);
    }

    /**
     * 新增一个告警
     *
     * @param vehicleName
     * @param alarm
     */
    public void addAlarm(String vehicleName, VehicleAlarmEventDTO alarm) {
        if (Objects.isNull(alarm.getReportTime())) {
            alarm.setReportTime(new Date());
        }
        if (Objects.isNull(alarm.getRuntimeUuid())) {
            ReportBootDTO reportBoot = reportBootRepository.get(vehicleName);
            if (!Objects.isNull(reportBoot)) {
                alarm.setRuntimeUuid(String.valueOf(reportBoot.getRuntimeUuid()));
            }
        }
        Map<String, VehicleAlarmEventDTO> alarmMapDb = reportAlarmRepository.get(vehicleName);
        if (!CollectionUtils.isEmpty(alarmMapDb)) {
            VehicleAlarmEventDTO alarmDb = alarmMapDb.get(alarm.getType());
            if (!Objects.isNull(alarmDb)) {
                if (alarmDb.getReportTime().before(alarm.getReportTime())) {
                    alarm.setReportTime(alarmDb.getReportTime());
                }
                if (!Objects.isNull(alarm.getEndTime()) && !Objects.isNull(alarmDb.getEndTime())
                        && alarmDb.getEndTime().after(alarm.getEndTime())) {
                    alarm.setEndTime(alarmDb.getEndTime());
                }
            }
        }

        reportAlarmRepository.save(vehicleName, alarm);
        sendKafka(vehicleName);
    }

    /**
     * 删除告警
     *
     * @param vehicleName
     * @param alarmType
     * @param alarmMap
     */
    public void removeAlarm(String vehicleName, String alarmType, Map<String, VehicleAlarmEventDTO> alarmMap) {
        if (CollectionUtils.isEmpty(alarmMap)) {
            return;
        }
        if (!alarmMap.containsKey(alarmType)) {
            return;
        }
        removeAlarm(vehicleName, alarmType);
    }

    /**
     * 删除一个告警
     *
     * @param vehicleName
     * @param alarmType
     */
    public void removeAlarm(String vehicleName, String alarmType) {
        reportAlarmRepository.remove(vehicleName, alarmType);
        sendKafka(vehicleName);
    }

    public void saveAlarmRedis(List<VehicleAbnormalDTO> abnormalList) {
        if (CollectionUtils.isEmpty(abnormalList)) {
            return;
        }
        String vehicleName = abnormalList.get(0).getVehicleName();
        Map<String, VehicleAlarmEventDTO> alarmMapDb = reportAlarmRepository.get(vehicleName);
        Map<String, VehicleAlarmEventDTO> alarmMap = getAlarmMap(abnormalList, alarmMapDb);
        if (CollectionUtils.isEmpty(abnormalList)) {
            return;
        }

        List<VehicleAlarmEventDTO> saveList = new ArrayList<>();
        List<String> removeList = new ArrayList<>();
        for (VehicleAlarmEventDTO alarm : alarmMap.values()) {
            if (!Objects.isNull(alarm.getEndTime()) && alarm.getEndTime().getTime() > 0) {
                removeList.add(alarm.getType());
                if (retainVehicleStopGuardian(vehicleName, removeList)) {
                    continue;
                }
                reportAlarmRepository.remove(vehicleName, alarm.getType());
                alarmMapDb = reportAlarmRepository.get(vehicleName);
                continue;
            }
            VehicleAlarmEventDTO alarmDb = alarmMapDb.get(alarm.getType());
            if (Objects.isNull(alarmDb)) {
                saveList.add(alarm);
                reportAlarmRepository.save(vehicleName, alarm);
                alarmMapDb = reportAlarmRepository.get(vehicleName);
            } else if (!Objects.equals(alarm.getErrorCode(), alarmDb.getErrorCode())) {
                // 如果只是异常码变化,暂时不发送kafka
                reportAlarmRepository.save(vehicleName, alarm);
                alarmMapDb = reportAlarmRepository.get(vehicleName);
                log.info("{}当前告警异常码变化{},redis告警{}", vehicleName, alarm, alarmDb);
            } else {
                log.info("{}当前告警已存在{},redis告警{}", vehicleName, alarm, alarmDb);
            }
        }

        if (!CollectionUtils.isEmpty(saveList) || !CollectionUtils.isEmpty(removeList)) {
            sendKafka(vehicleName);
            log.info("saveAlarmRedis vehicleName={}, saveList={}, removeList={}", vehicleName, saveList, removeList);
        }
    }

    public Map<String, VehicleAlarmEventDTO> getAlarmMap(List<VehicleAbnormalDTO> abnormalList, Map<String, VehicleAlarmEventDTO> alarmMapDb) {
        Map<String, VehicleAlarmEventDTO> resultMap = new HashMap<>();
        String vehicleName = abnormalList.get(0).getVehicleName();
        VehicleRealtimeInfoDTO realtime = vehicleRealtimeInfoRepository.get(vehicleName);
        for (VehicleAbnormalDTO vo : abnormalList) {
            VehicleAlarmEventDTO alarm = new VehicleAlarmEventDTO();
            Date startTime = new Date(vo.getStartTime() / NumberConstant.MILLION);
            alarm.setReportTime(startTime);
            GuardianErrorEnum guardianErrorEnum = GuardianErrorEnum.of(vo.getErrorCode());
            // 未匹配上枚举,且是error级别错误,都设置为告警停车
            if (guardianErrorEnum == null && GuardianErrorLevelEnum.FATAL.getErrorLevel().equals(vo.getErrorLevel())) {
                guardianErrorEnum = GuardianErrorEnum.ABNORMAL_STOP_ERROR_LEVE_TYPE;
                VehicleAlarmEventDTO lastAbnormalAlarmEvent = resultMap.get(guardianErrorEnum.getAlarmType());
                if (lastAbnormalAlarmEvent != null && lastAbnormalAlarmEvent.getReportTime().before(startTime)) {
                    alarm.setReportTime(lastAbnormalAlarmEvent.getReportTime());
                }
            }
            // 未匹配上枚举,直接忽略
            if (guardianErrorEnum == null) {
                continue;
            }
            // 胎压告警,但是级别不是error,直接忽略
            if (guardianErrorEnum.equals(GuardianErrorEnum.ERROR_DRIVER_CHASSIS_WHEEL_ALARM) && !GuardianErrorLevelEnum.ERROR.getErrorLevel().equals(vo.getErrorLevel())) {
                continue;
            }
            // CPU温度过高仅保留FATAL级别
            if (guardianErrorEnum.equals(GuardianErrorEnum.ERROR_GUARDIAN_SYSTEM_CPU_TEMPATURE_TOO_HIGH) && !GuardianErrorLevelEnum.FATAL.getErrorLevel().equals(vo.getErrorLevel())) {
                continue;
            }
            alarm.setType(guardianErrorEnum.getAlarmType());
            alarm.setErrorCode(vo.getErrorCode());
            alarm.setErrorLevel(vo.getErrorLevel());
            alarm.setErrorMessage(vo.getErrorMsg());
            alarm.setRuntimeUuid(String.valueOf(vo.getRuntimeUuid()));
            if (vo.getEndTime() > 0) {
                alarm.setEndTime(new Date(vo.getEndTime() / NumberConstant.MILLION));
            }
            resultMap.put(guardianErrorEnum.getAlarmType(), alarm);
        }
        VehiclePncInfoDTO vehiclePncInfoDTO = vehiclePncRepository.get(vehicleName);

        // 无保护左转报警
        passNoSignalIntersectionAlarm(vehiclePncInfoDTO, resultMap, new Date());

        // 只有车辆自动驾驶（pnc有规划路线）和远程遥控（车辆状态为“远程控制”(DRIVEMODE_TAKEOVER_REMOTE_CTRL)和“强制遥控”(DRIVEMODE_TAKEOVER_REMOTE_SUPER_CTRL)）时，
        // 上报“安全接管停车”，其他状态不上报该报警事件。
        String vehicleState = (realtime == null ? null : realtime.getVehicleState());
        if (!VehicleStateInfoManager.hasPlanRoute(vehiclePncInfoDTO) && !VehicleStateInfoManager.isRemoteControl(vehicleState)) {
            resultMap.remove(GuardianErrorEnum.GUARD_EMERGENCY_STOP_ERROR_CODE_TYPE.getAlarmType());
        }

        // 比较redis和当前时间,取最早时间
        resultMap.values().forEach(v -> {
            Date startTime = getStartTime(alarmMapDb, v);
            v.setReportTime(startTime);
        });
        return resultMap;
    }

    /**
     * 获取开始时间
     *
     * @param alarmMapDb
     * @return
     */
    private Date getStartTime(Map<String, VehicleAlarmEventDTO> alarmMapDb, VehicleAlarmEventDTO alarmEvent) {
        Date reportTime = alarmEvent.getReportTime();
        if (CollectionUtils.isEmpty(alarmMapDb)) {
            return reportTime;
        }
        VehicleAlarmEventDTO alarmDb = alarmMapDb.get(alarmEvent.getType());
        if (alarmDb == null) {
            return reportTime;
        }
        Date startTimeDb = alarmDb.getReportTime();
        if (startTimeDb == null) {
            return reportTime;
        }
        if (reportTime.before(startTimeDb)) {
            return reportTime;
        }
        return startTimeDb;
    }

    /**
     * 增加告警事件map
     *
     * @param alarmEventMap
     * @param guardianErrorEnum
     * @param reportTime
     */
    private void putAlarmEventMap(String vehicleName, Map<String, VehicleAlarmEventDTO> alarmEventMap
            , GuardianErrorEnum guardianErrorEnum, Date reportTime, Date latestReportTime) {
        VehicleAlarmEventDTO vehicleAlarmEvent = new VehicleAlarmEventDTO();
        vehicleAlarmEvent.setType(guardianErrorEnum.getAlarmType());
        vehicleAlarmEvent.setReportTime(reportTime);
        vehicleAlarmEvent.setErrorCode(guardianErrorEnum.getErrorCode());
        vehicleAlarmEvent.setEndTime(latestReportTime);
        ReportBootDTO reportBoot = reportBootRepository.get(vehicleName);
        if (Objects.nonNull(reportBoot)) {
            vehicleAlarmEvent.setRuntimeUuid(String.valueOf(reportBoot.getRuntimeUuid()));
        }
        alarmEventMap.put(guardianErrorEnum.getAlarmType(), vehicleAlarmEvent);
    }

    /**
     * 是否为新告警或者高优先级告警
     *
     * @param alarmEventMap
     * @param guardianErrorEnum
     * @param reportTime
     * @return
     */
    private boolean isNewOrHighPriorityAlarm(Map<String, VehicleAlarmEventDTO> alarmEventMap, GuardianErrorEnum guardianErrorEnum, Date reportTime) {
        VehicleAlarmEventDTO lastAlarmEvent = alarmEventMap.get(guardianErrorEnum.getCategory());
        if (lastAlarmEvent == null) {
            return true;
        }
        GuardianErrorEnum lastGuardianErrorEnum = GuardianErrorEnum.getFromAlarmType(lastAlarmEvent.getType());
        // 新告警优先级低靠后,直接忽略
        if (lastGuardianErrorEnum.getPriority() < guardianErrorEnum.getPriority()) {
            return false;
        }
        // 新告警优先级相同,时间靠后,直接忽略
        if (lastGuardianErrorEnum.getPriority().equals(guardianErrorEnum.getPriority())
                && lastAlarmEvent.getReportTime().before(reportTime)) {
            return false;
        }
        return true;
    }

    /**
     * 是否忽略遇阻停车
     *
     * @param vehicleName
     * @param guardianErrorEnum
     * @return
     */
    private boolean isNotReportStuckStop(String vehicleName, GuardianErrorEnum guardianErrorEnum) {
        String redisKey = KafkaTopicConstant.SERVER_GUARDIAN_REALTIME + "_" + vehicleName;
        VehicleRealtimeInfoDTO vehicleRealtimeInfoDTO = RedissonUtils.getObject(redisKey);
        VehiclePncInfoDTO vehiclePncInfoDTO = vehiclePncRepository.get(vehicleName);
        if (!GuardianErrorEnum.STUCK_ERROR_CODE_TYPE.equals(guardianErrorEnum)) {
            return false;
        }
        if (VehicleStateInfoManager.isVehicleStateTakeOver(vehicleRealtimeInfoDTO.getVehicleState())) {
            return true;
        }
        if (!VehicleStateInfoManager.hasPlanRoute(vehiclePncInfoDTO)) {
            return true;
        }
        return false;
    }

    /**
     * 发送kafka消息
     *
     * @param vehicleName
     */
    public void sendKafka(String vehicleName) {
        Map<String, VehicleAlarmEventDTO> alarmMapDb = reportAlarmRepository.get(vehicleName);
        // 必须拷贝后使用,否则会改变本地缓存数据
        Map<String, VehicleAlarmEventDTO> alarmMapCopy = new HashMap<>(alarmMapDb.size());
        if (!CollectionUtils.isEmpty(alarmMapDb)) {
            for (Map.Entry<String, VehicleAlarmEventDTO> entry : alarmMapDb.entrySet()) {
                VehicleAlarmEventDTO dto = new VehicleAlarmEventDTO();
                VehicleAlarmEventDTO db = entry.getValue();
                dto.setType(db.getType());
                dto.setReportTime(db.getReportTime());
                dto.setErrorCode(db.getErrorCode());
                dto.setErrorLevel(db.getErrorLevel());
                dto.setErrorMessage(db.getErrorMessage());
                dto.setEndTime(db.getEndTime());
                dto.setRuntimeUuid(db.getRuntimeUuid());
                alarmMapCopy.put(entry.getKey(), dto);
            }
        }
        sendKafka(vehicleName, alarmMapCopy);
    }

    /**
     * 发送kafka消息
     *
     * @param vehicleName
     * @param alarmMapDb
     */
    public void sendKafka(String vehicleName, Map<String, VehicleAlarmEventDTO> alarmMapDb) {
        filterBootAlarm(vehicleName, alarmMapDb);
        filterNoPlanRouteAlarm(vehicleName, alarmMapDb);
//        filterNotReportStuckStop(vehicleName, alarmMapDb);
        // 人工接管不解除停车告警
        filterTakeOver(vehicleName, alarmMapDb);

        List<VehicleAlarmEventDTO> alarmEventList = alarmMapDb.values().stream()
                .sorted(Comparator.comparing(tmp -> {
                    GuardianErrorEnum guardianErrorEnum = GuardianErrorEnum.getFromAlarmType(tmp.getType());
                    return guardianErrorEnum.getPriority();
                }))
                .collect(Collectors.toList());

        filterSamePriorityAlarm(alarmEventList);

        VehicleAlarmDTO alarmEntity = new VehicleAlarmDTO();
        alarmEntity.setAlarmEventList(alarmEventList);
        alarmEntity.setVehicleName(vehicleName);
        alarmEntity.setRecordTime(new Date());
        String jsonStr = JsonUtils.writeValueAsString(alarmEntity);
        jmqProducerManager.sendOrdered(producerTopicProperties.getServerGuardianAlarm(), jsonStr, vehicleName);
        log.info("发送告警消息={}", jsonStr);
    }

    /**
     * 相同优先级报警只发送一个
     *
     * @param alarmList
     */
    private void filterSamePriorityAlarm(List<VehicleAlarmEventDTO> alarmList) {
        if (CollectionUtils.isEmpty(alarmList)) {
            return;
        }
        Iterator<VehicleAlarmEventDTO> iterator = alarmList.iterator();
        GuardianErrorEnum last = GuardianErrorEnum.getFromAlarmType(iterator.next().getType());
        while (iterator.hasNext()) {
            VehicleAlarmEventDTO item = iterator.next();
            GuardianErrorEnum guardianErrorEnum = GuardianErrorEnum.getFromAlarmType(item.getType());
            if (Objects.equals(last.getCategory(), guardianErrorEnum.getCategory())) {
                iterator.remove();
            } else {
                last = guardianErrorEnum;
            }
        }
    }

    /**
     * 过滤启动异常
     * 通过车端开机按钮启动时，启动过程中（车辆模式为“初始化”），除“防撞”“开机异常”场景报警事件外，其他报警事件都不上报
     *
     * @param vehicleName
     * @param alarmMapDb
     */
    private void filterBootAlarm(String vehicleName, Map<String, VehicleAlarmEventDTO> alarmMapDb) {
        if (CollectionUtils.isEmpty(alarmMapDb)) {
            return;
        }
        ReportBootDTO reportBoot = reportBootRepository.get(vehicleName);
        if (Objects.isNull(reportBoot)) {
            return;
        }
        if (isNewHardwareBoot(reportBoot, alarmMapDb)) {
            // 不是清除所有,只清除runtimeUuid对应不上的
            alarmMapDb.entrySet().removeIf(entry -> !Objects.equals(reportBoot.getRuntimeUuid(), Long.valueOf(entry.getValue().getRuntimeUuid())));
            log.info("新硬件启动过滤发送告警vehicleName={},reportBoot={},alarmMapDb={}", vehicleName, reportBoot, alarmMapDb);
            return;
        }
        if (Objects.equals(reportBoot.getAllBootStatus(), BootStatusEnum.START_SUCCESS.name())) {
            return;
        }
        if (!Objects.equals(reportBoot.getBootId(), 1L)) {
            return;
        }
        removeBootAlarm(alarmMapDb);
    }

    /**
     * 删除启动时期告警
     *
     * @param alarmMapDb
     */
    private static void removeBootAlarm(Map<String, VehicleAlarmEventDTO> alarmMapDb) {
        Iterator<Map.Entry<String, VehicleAlarmEventDTO>> it = alarmMapDb.entrySet().iterator();  //entrySet
        while (it.hasNext()) {
            Map.Entry<String, VehicleAlarmEventDTO> entry = it.next();
            VehicleAlarmEventDTO alarm = entry.getValue();
            if (StringUtils.equalsAny(alarm.getType(), AlarmTypeEnum.VEHICLE_CRASH.getValue(), AlarmTypeEnum.BOOT_ABNORMAL.getValue()
                    , AlarmTypeEnum.BOOT_FAIL.getValue(), AlarmTypeEnum.BOOT_TIMEOUT.getValue())) {
                continue;
            }
            it.remove();
            log.info("启动时过滤发送告警={}", alarm);
        }
    }

    /**
     * 通过告警判断是否硬件启动
     *
     * @param alarmMapDb
     * @return
     */
    private boolean isNewHardwareBoot(Long runtimeUuid, Map<String, VehicleAlarmEventDTO> alarmMapDb) {
        if (CollectionUtils.isEmpty(alarmMapDb)) {
            return false;
        }
        for (VehicleAlarmEventDTO value : alarmMapDb.values()) {
            if (!Objects.equals(runtimeUuid, value.getRuntimeUuid())) {
                return true;
            }
        }
        return false;
    }

    /**
     * 通过告警判断是否硬件启动
     *
     * @param reportBoot
     * @param alarmMapDb
     * @return
     */
    private boolean isNewHardwareBoot(ReportBootDTO reportBoot, Map<String, VehicleAlarmEventDTO> alarmMapDb) {
        if (Objects.isNull(reportBoot)) {
            return false;
        }
        Long runtimeUuid = reportBoot.getRuntimeUuid();
        if (Objects.isNull(runtimeUuid)) {
            return false;
        }
        for (VehicleAlarmEventDTO value : alarmMapDb.values()) {
            if (Objects.isNull(value.getRuntimeUuid())) {
                continue;
            }
            if (!Objects.equals(runtimeUuid, Long.valueOf(value.getRuntimeUuid()))) {
                return true;
            }
        }
        return false;
    }

    /**
     * 过滤没有路径规划的异常
     * 如果没有规划路径，则不上报停车按钮,遇阻停车的告警
     *
     * @param vehicleName 车辆名称
     * @param alarmMapDb  告警事件映射表
     */
    private void filterNoPlanRouteAlarm(String vehicleName, Map<String, VehicleAlarmEventDTO> alarmMapDb) {
        if (CollectionUtils.isEmpty(alarmMapDb)) {
            return;
        }

        if (!alarmMapDb.containsKey(AlarmTypeEnum.VEHICLE_STOP_BUTTON.getValue())) {
            return;
        }

        // 获取车辆的路径规划信息
        VehiclePncInfoDTO vehiclePncInfoDTO = vehiclePncRepository.get(vehicleName);
        // 如果车辆没有规划路径，则移除停车按钮的告警事件
        if (VehicleStateInfoManager.hasPlanRoute(vehiclePncInfoDTO)) {
            return;
        }
        alarmMapDb.remove(AlarmTypeEnum.VEHICLE_STOP_BUTTON.getValue());
        log.info("过滤删除无规划路径的按钮停车!车辆={},告警={}", vehicleName, alarmMapDb);
    }

    /**
     * 是否忽略遇阻停车
     *
     * @param vehicleName 车辆名称
     * @param alarmMapDb  告警事件映射表
     */
    private void filterNotReportStuckStop(String vehicleName, Map<String, VehicleAlarmEventDTO> alarmMapDb) {
        if (CollectionUtils.isEmpty(alarmMapDb)) {
            return;
        }

        // 获取遇阻停车的告警事件
        VehicleAlarmEventDTO alarm = alarmMapDb.get(AlarmTypeEnum.VEHICLE_STOP_TIMEOUT.getValue());
        if (Objects.isNull(alarm)) {
            return;
        }

        VehicleRealtimeInfoDTO realtime = vehicleRealtimeInfoRepository.get(vehicleName);
        //  如果车辆没有规划路径，则移除遇阻停车的告警事件
        VehiclePncInfoDTO vehiclePncInfoDTO = vehiclePncRepository.get(vehicleName);
        if (!VehicleStateInfoManager.isVehicleStateTakeOver(realtime.getVehicleState())
                && VehicleStateInfoManager.hasPlanRoute(vehiclePncInfoDTO)) {
            return;
        }
        alarmMapDb.remove(AlarmTypeEnum.VEHICLE_STOP_TIMEOUT.getValue());
    }

    /**
     * 过滤接管
     *
     * @param vehicleName 车辆名称
     * @param alarmMapDb  告警事件映射表
     */
    private void filterTakeOver(String vehicleName, Map<String, VehicleAlarmEventDTO> alarmMapDb) {
        if (CollectionUtils.isEmpty(alarmMapDb)) {
            return;
        }
        if(!alarmMapDb.containsKey(AlarmTypeEnum.GATE_STUCK.getValue())){
            return;
        }
        VehicleStatusDTO vehicleStatusDTO = vehicleStatusRepository.get(vehicleName);
        if (Objects.isNull(vehicleStatusDTO)) {
            return;
        }
        if (!VehicleStateInfoManager.isVehicleStateTakeOver(vehicleStatusDTO.getVehicleState())) {
            return;
        }
        alarmMapDb.remove(AlarmTypeEnum.GATE_STUCK.getValue());
        log.info("接管时过滤发送告警={}", alarmMapDb);
    }

    /**
     * 无保护左转报警
     *
     * @param alarmMap
     */
    private void passNoSignalIntersectionAlarm(VehiclePncInfoDTO vehiclePncInfoDTO, Map<String, VehicleAlarmEventDTO> alarmMap, Date reportTime) {
        if (Objects.isNull(vehiclePncInfoDTO)) {
            return;
        }
        PassNoSignalIntersectionDTO noSignal = vehiclePncInfoDTO.getPassNoSignalIntersection();
        if (Objects.isNull(noSignal) || Objects.isNull(noSignal.getStatus())) {
            return;
        }
        String status = noSignal.getStatus();
        if (Objects.equals(status, GuardianDataDto.PassNoSignalIntersectionStatus.Status.READY.name())
                || Objects.equals(status, GuardianDataDto.PassNoSignalIntersectionStatus.Status.RUNNING.name())) {
            putAlarmEventMap(vehiclePncInfoDTO.getVehicleName(), alarmMap, GuardianErrorEnum.PASS_NO_SIGNAL_INTERSECTION
                    , reportTime, reportTime);
        }
    }

    /**
     * 处理所有停止上报告警
     */
    public void handStopAlarm() {
        Map<String, Map<String, VehicleAlarmEventDTO>> map = reportAlarmRepository.getAll();
        for (Map.Entry<String, Map<String, VehicleAlarmEventDTO>> entry : map.entrySet()) {
            handStopAlarm(entry.getKey(), entry.getValue());
        }
    }

    /**
     * 处理停止上报的告警信息
     *
     * @param vehicleName
     * @param alarmMapDb
     */
    public void handStopAlarm(String vehicleName, Map<String, VehicleAlarmEventDTO> alarmMapDb) {
        if (CollectionUtils.isEmpty(alarmMapDb)) {
            return;
        }
        boolean isSend = false;
        Date now = new Date();
        Iterator<Map.Entry<String, VehicleAlarmEventDTO>> it = alarmMapDb.entrySet().iterator();
        while (it.hasNext()) {
            Map.Entry<String, VehicleAlarmEventDTO> item = it.next();
            VehicleAlarmEventDTO vehicleAlarmEventDTO = item.getValue();
            if (Objects.isNull(vehicleAlarmEventDTO.getEndTime())) {
                continue;
            }
            // 目前只支持安全接管急停的定时清除
            if (!Objects.equals(GuardianErrorEnum.GUARD_EMERGENCY_STOP_ERROR_CODE_TYPE.getAlarmType(), vehicleAlarmEventDTO.getType())) {
                continue;
            }
            long time = DateUtil.between(now, vehicleAlarmEventDTO.getEndTime(), DateUnit.SECOND);
            if (time < MAX_ALARM_INTERVAL) {
                continue;
            }
            long result = reportAlarmRepository.remove(vehicleName, vehicleAlarmEventDTO.getType());
            if (result == 0) {
                log.info("redis已经被删除,不发送异常kafka,车辆={},异常={}", vehicleName, vehicleAlarmEventDTO);
                continue;
            }
            isSend = true;
            it.remove();
        }
        if (!isSend) {
            return;
        }
        sendKafka(vehicleName, alarmMapDb);
    }

    /**
     * 保留安全接管停车
     *
     * @param vehicleName 车辆名称
     */
    private boolean retainVehicleStopGuardian(String vehicleName, List<String> removeList) {
        if (!removeList.contains(AlarmTypeEnum.VEHICLE_STOP_GUARDIAN.getValue())) {
            return false;
        }
        VehicleRealtimeInfoDTO realtime = vehicleRealtimeInfoRepository.get(vehicleName);
        if (Objects.isNull(realtime) || Objects.isNull(realtime.getDrivableDirection())) {
            return false;
        }
        if (!realtime.getDrivableDirection().getEnableFront() && !realtime.getDrivableDirection().getEnableBack()) {
            // 如果前后使能为false,暂不移除安全接管急停
            log.info("前后使能为false,保留安全接管急停!车辆={},realtime={}", vehicleName, realtime);
            removeList.remove(AlarmTypeEnum.VEHICLE_STOP_GUARDIAN.getValue());
            return true;
        }
        return false;
    }
}
