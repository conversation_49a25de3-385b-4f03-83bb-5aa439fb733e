/*
 * Copyright (c) 2022-2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.server.business.controller.vehicle;

import com.jdx.rover.server.api.domain.dto.vehicle.GridDataDTO;
import com.jdx.rover.server.api.domain.vo.vehicle.GridVO;
import com.jdx.rover.server.business.service.VehicleGridService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * 车辆格口controller
 *
 * <AUTHOR>
 */
@RestController
@Slf4j
public class VehicleGridController {
    @Autowired
    private VehicleGridService vehicleGridService;

    /**
     * 获取车辆实时位置
     *
     * @param gridVO
     */
    @PostMapping(value = "/server/business/vehicle/getGridStatus")
    public GridDataDTO getGridStatus(@RequestBody GridVO gridVO) {
        GridDataDTO result = vehicleGridService.getGridStatus(gridVO);
        return result;
    }
}
