/*
 * Copyright (c) 2023 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.server.business.listener;

import com.jdx.rover.server.business.service.map.MapModuleVersionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 地图模块版本监听器.
 * 接收kafka发送的地图模块版本信息，并存入redis中.
 *
 * <AUTHOR>
 * @date 2024-03-16
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class MapModuleVersionListener {
    /**
     * 地图模块版本服务
     */
    private final MapModuleVersionService mapModuleVersionService;

//    @KafkaListener(topics = {"map_module_version"})
    public void onMessage(List<String> messageList) {
        if (CollectionUtils.isEmpty(messageList)) {
            return;
        }
        log.info("Received topic={}, size={}, message={}", "map_module_version", messageList.size(), messageList);
        if (CollectionUtils.isEmpty(messageList)) {
            return;
        }
        for (String message : messageList) {
            try {
                mapModuleVersionService.handleOneMessage(message);
            } catch (Exception e) {
                log.error("处理消息失败!{}", message, e);
            }
        }
    }
}