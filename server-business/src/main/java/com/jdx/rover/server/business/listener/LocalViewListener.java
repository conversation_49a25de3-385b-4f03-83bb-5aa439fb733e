/*
 * Copyright (c) 2023 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.server.business.listener;

import com.jdx.rover.server.business.service.LocalViewService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 3D数据Kafka监听
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class LocalViewListener {

    private final LocalViewService localViewService;

    @KafkaListener(topicPattern = "server_local_view_.*", containerFactory = "byteKafkaContainerFactory")
    public void onMessage(List<byte[]> messageList) {
        if (CollectionUtils.isEmpty(messageList)) {
            return;
        }
        for (byte[] message : messageList) {
            localViewService.receive(message);
        }
    }
}