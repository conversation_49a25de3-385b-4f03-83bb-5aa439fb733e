package com.jdx.rover.server.business.service;

import com.google.common.collect.Lists;
import com.jdx.rover.server.api.domain.enums.OtaMoudleEnum;
import com.jdx.rover.server.api.domain.enums.guardian.ChargeStateEnum;
import com.jdx.rover.server.api.domain.enums.guardian.IntersectionStatusEnum;
import com.jdx.rover.server.business.manager.*;
import com.jdx.rover.server.domain.enums.TrafficLightSourceEnum;
import com.jdx.rover.server.domain.vo.guardian.DrivableDirectionAddVo;
import com.jdx.rover.server.domain.vo.guardian.GuardianInfoAddVo;
import com.jdx.rover.server.domain.vo.guardian.LaunchStatusAddVo;
import com.jdx.rover.server.domain.vo.guardian.NavigationPlanningStatus;
import com.jdx.rover.server.domain.vo.guardian.NavigationPncStatus;
import com.jdx.rover.server.domain.vo.guardian.NavigationRoutingPosition;
import com.jdx.rover.server.domain.vo.guardian.NavigationRoutingStatus;
import com.jdx.rover.server.domain.vo.guardian.NavigationStopResult;
import com.jdx.rover.server.domain.vo.guardian.OtaModuleVersion;
import com.jdx.rover.server.domain.vo.guardian.OtaUpdateTaskStatus;
import com.jdx.rover.server.domain.vo.guardian.PassNoSignalIntersectionVO;
import com.jdx.rover.server.domain.vo.guardian.ServiceInfo;
import com.jdx.rover.server.domain.vo.guardian.TrafficLightInfo;
import com.jdx.rover.server.domain.vo.guardian.VehicleExceptionLogAddVo;
import com.jdx.rover.server.domain.vo.guardian.VehicleStatusAddVo;
import com.jdx.rover.server.domain.vo.guardian.VehicleWheelEntityAddVo;
import com.jdx.rover.server.domain.vo.guardian.VehicleWheelInfoAddVo;
import jdx.rover.guardian.data.dto.GuardianDataDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Guardian消息处理
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class GuardianService {
    private static final long NANOSECOND_TO_MILLISECOND = 1000000L;
//    @Autowired
//    private VehicleAlarmInfoManager vehicleAlarmInfoManager;
    @Autowired
    private VehicleRealtimeInfoManager vehicleRealtimeInfoManager;
    @Autowired
    private VehiclePowerManager vehiclePowerManager;
    @Autowired
    private GuardianPncManager guardianPncManager;
    @Autowired
    private VehicleAbnormalManager vehicleAbnormalManager;
    @Autowired
    private VehicleOtaInfoManager vehicleOtaInfoManager;
    @Autowired
    private VehicleVersionInfoManager vehicleVersionInfoManager;
    @Autowired
    private RunMapStateManager runMapStateManager;
    @Autowired
    private VehicleSteerZeroStateManager vehicleSteerZeroStateManager;
    @Autowired
    private KafkaTemplate<String, String> kafkaTemplate;

    public void receiveList(List<byte[]> messageList) {
        if (CollectionUtils.isEmpty(messageList)) {
            return;
        }
        for (byte[] message : messageList) {
            try {
                GuardianDataDto.GuardianInfoDTO dto = GuardianDataDto.GuardianInfoDTO.parseFrom(message);
                receive(dto);
            } catch (Exception e) {
                log.error("guardian处理失败", e);
            }
        }
    }

    public void receive(GuardianDataDto.GuardianInfoDTO dto) {
        if (dto == null || StringUtils.isEmpty(dto.getVehicleId())) {
            log.error("Guardian or vehicleId absent!");
            return;
        }
        GuardianInfoAddVo guardianInfoAddVo = convertGuardianInfoProtoToVo(dto);
        vehicleRealtimeInfoManager.handle(guardianInfoAddVo);

        guardianPncManager.handle(guardianInfoAddVo);

//        vehicleAlarmInfoManager.handle(guardianInfoAddVo);

        vehicleAbnormalManager.handle(guardianInfoAddVo);

        vehiclePowerManager.handle(guardianInfoAddVo);

        vehicleVersionInfoManager.handle(guardianInfoAddVo);

//        VehicleLocalizationManager.printLog(dto);
        runMapStateManager.handle(dto);

        vehicleSteerZeroStateManager.handle(dto);
    }

    public GuardianInfoAddVo convertGuardianInfoProtoToVo(GuardianDataDto.GuardianInfoDTO dto) {
        GuardianInfoAddVo guardianInfoAddVo = new GuardianInfoAddVo();
        guardianInfoAddVo.setVehicleName(dto.getVehicleId());
        // reportTime
        if (dto.getTimestamp() != 0) {
            guardianInfoAddVo.setReportTime(new Date(Long.valueOf(dto.getTimestamp() / NANOSECOND_TO_MILLISECOND)));
        }

        // drivableDirection
        DrivableDirectionAddVo directionAddVo = getDrivableDirectionAddVo(dto);
        guardianInfoAddVo.setDrivableDirectionRequest(directionAddVo);

        // vehicleExceptionLogSaveRequestList
        List<VehicleExceptionLogAddVo> vehicleExceptionLogSaveRequestVoList = getVehicleExceptionLogAddVoList(dto);
        guardianInfoAddVo.setVehicleExceptionLogSaveRequestList(vehicleExceptionLogSaveRequestVoList);

        // vehicleStatusRequest
        VehicleStatusAddVo vehicleStatusRequestVo = getVehicleStatusAddVo(dto);
        guardianInfoAddVo.setVehicleStatusRequest(vehicleStatusRequestVo);

        // VehicleWheelInfoAddVo
        VehicleWheelInfoAddVo vehicleWheelInfo = getVehicleWheelInfoAddVo(dto);
        guardianInfoAddVo.setVehicleWheelInfo(vehicleWheelInfo);

        // launchStatusList
        List<LaunchStatusAddVo> launchStatusList = getLaunchStatusAddVoList(dto);
        guardianInfoAddVo.setLaunchStatusSaveRequestList(launchStatusList);

        // navigationPncStatusRequest
        NavigationPncStatus navigationPncStatus = new NavigationPncStatus();
        // planning status
        NavigationPlanningStatus navigationPlanningStatus = getNavigationPlanningStatus(dto);
        // routing status
        NavigationRoutingStatus navigationRoutingStatus = getNavigationRoutingStatus(dto);
        navigationPncStatus.setPlanningStatus(navigationPlanningStatus);
        navigationPncStatus.setRoutingStatus(navigationRoutingStatus);
        guardianInfoAddVo.setNavigationPncStatusRequest(navigationPncStatus);

        // ota update
        OtaUpdateTaskStatus otaUpdateTaskStatus = getOtaUpdateTaskStatus(dto);
        guardianInfoAddVo.setOtaUpdateTaskStatus(otaUpdateTaskStatus);
        // service info
        List<ServiceInfo> serviceInfo = getServiceInfo(dto);
        guardianInfoAddVo.setServiceInfo(serviceInfo);
        return guardianInfoAddVo;
    }

    /**
     * 获取导航路由信息
     *
     * @param guardianInfoDTO
     * @return
     */
    private NavigationRoutingStatus getNavigationRoutingStatus(GuardianDataDto.GuardianInfoDTO guardianInfoDTO) {
        NavigationRoutingStatus navigationRoutingStatus = new NavigationRoutingStatus();
        GuardianDataDto.RoutingStatus routingStatus = guardianInfoDTO.getRoutingStatus();
        if (routingStatus == null) {
            return navigationRoutingStatus;
        }

        List<NavigationStopResult> navigationStopResultList = new ArrayList<>();
        List<GuardianDataDto.StopResult> stopResultList = routingStatus.getStopResultList();
        if (!CollectionUtils.isEmpty(stopResultList)) {
            for (GuardianDataDto.StopResult stopResult : stopResultList) {
                List<GuardianDataDto.RoutingPosition> routingPointList = stopResult.getRoutingPointList();
                List<NavigationRoutingPosition> routingPositionList = new ArrayList<>();
                if (!CollectionUtils.isEmpty(routingPointList)) {
                    for (GuardianDataDto.RoutingPosition routingPosition : routingPointList) {
                        NavigationRoutingPosition position = new NavigationRoutingPosition();
                        position.setLatitude(routingPosition.getLatitude());
                        position.setLongitude(routingPosition.getLongitude());
                        position.setMapX(routingPosition.getMapX());
                        position.setMapY(routingPosition.getMapY());
                        position.setXcsX(routingPosition.getXcsX());
                        position.setXcsY(routingPosition.getXcsY());
                        routingPositionList.add(position);
                    }
                }
                NavigationStopResult navigationStopResult = new NavigationStopResult();
                navigationStopResult.setStopId(stopResult.getStopId());
                navigationStopResult.setRoutingMileage(stopResult.getRoutingMileage());
                navigationStopResult.setRoutingPositionList(routingPositionList);
                navigationStopResultList.add(navigationStopResult);
            }
        }
        navigationRoutingStatus.setStopResultList(navigationStopResultList);
        navigationRoutingStatus.setRoutingId(routingStatus.getRoutingId());
        navigationRoutingStatus.setCurrentStopIndex(routingStatus.getCurrentStopIndex());
        navigationRoutingStatus.setGlobalMileage(routingStatus.getGlobalMileage());
//        navigationRoutingStatus.setPowerConsumptionPrediction(routingStatus.getPowerConsumptionPrediction());
//        navigationRoutingStatus.setRemainingPower(routingStatus.getRemainingPower());
        navigationRoutingStatus.setMileageToNextStop(routingStatus.getMileageToNextStop());
        navigationRoutingStatus.setCurrentStopFinishedMileage(routingStatus.getCurrentStopFinishedMileage());
        navigationRoutingStatus.setNavSequenceNum(routingStatus.getNaviSequenceNum());
        navigationRoutingStatus.setNaviRefreshId(routingStatus.getNaviRefreshId());

        if (navigationRoutingStatus.getCurrentStopIndex() == null) {
            navigationRoutingStatus.setCurrentStopIndex(0);
        }
        if (navigationRoutingStatus.getCurrentStopFinishedMileage() == null) {
            navigationRoutingStatus.setCurrentStopFinishedMileage(0.0);
        }

        GuardianDataDto.RoutingStatus.ResultCode resultCode = routingStatus.getResultCode();
        if (resultCode != null) {
            navigationRoutingStatus.setType(resultCode.getType().name());
            navigationRoutingStatus.setMsg(resultCode.getMsg().name());
        }
        return navigationRoutingStatus;
    }

    /**
     * 获取导航规划信息
     *
     * @param guardianInfoDTO
     * @return
     */
    private NavigationPlanningStatus getNavigationPlanningStatus(GuardianDataDto.GuardianInfoDTO guardianInfoDTO) {
        NavigationPlanningStatus navigationPlanningStatus = new NavigationPlanningStatus();
        GuardianDataDto.PlanningStatus planningStatus = guardianInfoDTO.getPlanningStatus();
        if (planningStatus == null) {
            return navigationPlanningStatus;
        }

        navigationPlanningStatus.setIntersectionStatus(planningStatus.getIntersectionStatus().name());
        List<GuardianDataDto.PlanningTrafficLightInfo> planningTrafficLightInfo = planningStatus.getInterestedLightList();
        if (!CollectionUtils.isEmpty(planningTrafficLightInfo)) {
            List<TrafficLightInfo> trafficLightInfoList = new ArrayList<>();
            for (GuardianDataDto.PlanningTrafficLightInfo info : planningTrafficLightInfo) {
                TrafficLightInfo trafficLightInfo = new TrafficLightInfo();
                trafficLightInfo.setId(info.getId());
                TrafficLightSourceEnum trafficLightSource = TrafficLightSourceEnum.UNKNOWN;
                if (info.getSource() != null) {
                    if (TrafficLightSourceEnum.SUPERVISOR.getSource().equals(info.getSource().name())) {
                        trafficLightSource = TrafficLightSourceEnum.SUPERVISOR;
                    } else if (TrafficLightSourceEnum.PERCEPTION.getSource().equals(info.getSource().name())) {
                        trafficLightSource = TrafficLightSourceEnum.PERCEPTION;
                    }
                }
                trafficLightInfo.setTrafficLightSourceEnum(trafficLightSource);
                trafficLightInfoList.add(trafficLightInfo);
            }
            navigationPlanningStatus.setTrafficLightInfo(trafficLightInfoList);
            // protobuf 默认值会显示null,有红绿灯,赋值初值
            if (navigationPlanningStatus.getIntersectionStatus() == null) {
                navigationPlanningStatus.setIntersectionStatus(IntersectionStatusEnum.IN_INTERSECTION.getValue());
            }
        }
        if (planningStatus.getResultCode() != null) {
            navigationPlanningStatus.setType(planningStatus.getResultCode().getType().name());
            navigationPlanningStatus.setMsg(planningStatus.getResultCode().getMsg().name());
        }

        GuardianDataDto.PassNoSignalIntersectionStatus noSignal = planningStatus.getPassNoSignalIntersectionStatus();
        if (noSignal != null) {
            PassNoSignalIntersectionVO passNoSignalVO = new PassNoSignalIntersectionVO();
            if (noSignal.getStatus() != null) {
                passNoSignalVO.setStatus(noSignal.getStatus().name());
            }
            passNoSignalVO.setGroupLaneIdList(Lists.newArrayList(noSignal.getGroupLaneIdList()));
            navigationPlanningStatus.setPassNoSignalIntersection(passNoSignalVO);
        }

        return navigationPlanningStatus;
    }

    /**
     * 获取启动信息
     *
     * @param guardianInfoDTO
     * @return
     */
    private List<LaunchStatusAddVo> getLaunchStatusAddVoList(GuardianDataDto.GuardianInfoDTO guardianInfoDTO) {
        List<LaunchStatusAddVo> launchStatusList = new ArrayList<>();
        List<GuardianDataDto.LaunchStatus> statusList = guardianInfoDTO.getLaunchStatusList();
        if (CollectionUtils.isEmpty(statusList)) {
            return launchStatusList;
        }
        for (GuardianDataDto.LaunchStatus status : statusList) {
            if (status == null) {
                continue;
            }
            LaunchStatusAddVo statusAddVo = new LaunchStatusAddVo();
            statusAddVo.setState(status.getState().name());
            statusAddVo.setCurrentStartingModuleName(status.getCurrentStartingModuleName());
            statusAddVo.setIp(status.getIp());
            statusAddVo.setRequiredModuleList(status.getNoexistRequiredModuleList());
            launchStatusList.add(statusAddVo);
        }
        return launchStatusList;
    }

    /**
     * 获取胎压信息
     *
     * @param guardianInfoDTO
     * @return
     */
    private VehicleWheelInfoAddVo getVehicleWheelInfoAddVo(GuardianDataDto.GuardianInfoDTO guardianInfoDTO) {
        List<GuardianDataDto.WheelEntity> wheelEntityList = guardianInfoDTO.getWheelEntityList();
        VehicleWheelInfoAddVo wheelInfoAddVo = new VehicleWheelInfoAddVo();
        List<VehicleWheelEntityAddVo> wheelEntityAddVos = new ArrayList<>();
        if (CollectionUtils.isEmpty(wheelEntityList)) {
            return wheelInfoAddVo;
        }
        for (GuardianDataDto.WheelEntity wheelEntity : wheelEntityList) {
            VehicleWheelEntityAddVo wheelEntityAddVo = new VehicleWheelEntityAddVo();
            wheelEntityAddVo.setPressure(wheelEntity.getPressure());
            wheelEntityAddVo.setPressureStatus(wheelEntity.getPressureStatus().name());
            wheelEntityAddVo.setWheelPosition(wheelEntity.getPosition().name());
            wheelEntityAddVos.add(wheelEntityAddVo);
        }
        wheelInfoAddVo.setVehicleWheelEntityAddVo(wheelEntityAddVos);
        return wheelInfoAddVo;
    }

    /**
     * 获取车辆状态
     *
     * @param guardianInfoDTO
     * @return
     */
    private VehicleStatusAddVo getVehicleStatusAddVo(GuardianDataDto.GuardianInfoDTO guardianInfoDTO) {
        VehicleStatusAddVo vehicleStatusRequestVo = new VehicleStatusAddVo();

        GuardianDataDto.SolutionStatus solutionStatus = guardianInfoDTO.getSolutionStatus();
        if (solutionStatus != null) {
            vehicleStatusRequestVo.setSolutionStatus(solutionStatus.getState().name());
        }

        GuardianDataDto.ChassisStatus chassisStatus = guardianInfoDTO.getChassisStatus();
        if (chassisStatus != null) {
            vehicleStatusRequestVo.setChassisStatus(chassisStatus.getDrivingMode().name());
            vehicleStatusRequestVo.setHeading(chassisStatus.getHeading());
            vehicleStatusRequestVo.setLastUpdateTimeForChassis(new Date());
            vehicleStatusRequestVo.setLastUpdateTimeForSolution(new Date());
            vehicleStatusRequestVo.setPowerUsage(chassisStatus.getBatteryCapacity());
            vehicleStatusRequestVo.setSpeed(chassisStatus.getVelocity());
            vehicleStatusRequestVo.setLat(Double.valueOf(chassisStatus.getLat()));
            vehicleStatusRequestVo.setLon(Double.valueOf(chassisStatus.getLon()));
            vehicleStatusRequestVo.setChargeState(getChargeState(chassisStatus.getChargeState().name()));
            if (vehicleStatusRequestVo.getSpeed() == null) {
                vehicleStatusRequestVo.setSpeed(0.0);
            }
            vehicleStatusRequestVo.setSteerZero(chassisStatus.getSteerZero());
            if (Objects.isNull(chassisStatus.getSteerZero())) {
                vehicleStatusRequestVo.setSteerZero(0.0);
            }
        }
        return vehicleStatusRequestVo;
    }

    /**
     * 获取ota版本信息
     *
     * @param dto
     * @return
     */
    private OtaUpdateTaskStatus getOtaUpdateTaskStatus(GuardianDataDto.GuardianInfoDTO dto) {
        OtaUpdateTaskStatus otaUpdateTaskStatus = new OtaUpdateTaskStatus();
        if (dto.getOtaUpdaterStatus() != null && !CollectionUtils.isEmpty(dto.getOtaUpdaterStatus().getVersionsList())) {
            List<OtaModuleVersion> otaModuleVersionList = dto.getOtaUpdaterStatus().getVersionsList().stream().map(version -> {
                OtaModuleVersion otaModuleVersion = new OtaModuleVersion();
                otaModuleVersion.setCurVersion(version.getCurVersion());
                otaModuleVersion.setName(version.getName());
                otaModuleVersion.setOtaVersion(version.getOtaVersion());
                return otaModuleVersion;
            }).collect(Collectors.toList());
            otaUpdateTaskStatus.setOtaModuleVersion(otaModuleVersionList);
        }
        return otaUpdateTaskStatus;
    }

    /**
     * 获取充电状态
     *
     * @param chargeState
     * @return
     */
    private String getChargeState(String chargeState) {
        if (StringUtils.isBlank(chargeState) || Objects.equals(GuardianDataDto.ChassisStatus.ChargeState.NULL.name(), chargeState)) {
            return ChargeStateEnum.UNKNOWN.getChargeState();
        }
        return ChargeStateEnum.valueOf(chargeState).getChargeState();
    }

    /**
     * 获取Android 视频升级信息
     *
     * @param dto
     * @return
     */
    private List<ServiceInfo> getServiceInfo(GuardianDataDto.GuardianInfoDTO dto) {
        if (CollectionUtils.isEmpty(dto.getServiceInfoList())) {
            return new ArrayList<>();
        }
        List<ServiceInfo> serviceInfoList = dto.getServiceInfoList().stream()
                .filter(info -> StringUtils.isNotBlank(info.getServiceName()) && StringUtils.isNotBlank(info.getMsg()))
                .filter(info -> StringUtils.equalsAny(info.getServiceName(),
                        OtaMoudleEnum.ANDROID.getValue(), OtaMoudleEnum.VIDEO.getValue())).map(info -> {
                    ServiceInfo serviceInfo = new ServiceInfo();
                    serviceInfo.setServiceName(info.getServiceName());
                    serviceInfo.setServiceState(info.getServiceState().name());
                    serviceInfo.setMsg(info.getMsg());
                    serviceInfo.setTimestamp(info.getTimestamp());
                    serviceInfo.setIp(info.getIp());
                    serviceInfo.setProgressPermill(info.getProgressPermill());
                    serviceInfo.setErrorCode(info.getErrorCode());
                    return serviceInfo;
                }).collect(Collectors.toList());
        return serviceInfoList;
    }

    /**
     * 获取错误日志信息
     *
     * @param dto
     * @return
     */
    private List<VehicleExceptionLogAddVo> getVehicleExceptionLogAddVoList(GuardianDataDto.GuardianInfoDTO dto) {
        List<VehicleExceptionLogAddVo> vehicleExceptionLogSaveRequestVoList = new ArrayList<>();
        if (CollectionUtils.isEmpty(dto.getAbnormalList())) {
            return vehicleExceptionLogSaveRequestVoList;
        }
        for (GuardianDataDto.Abnormal abnormal : dto.getAbnormalList()) {
            VehicleExceptionLogAddVo vehicleExceptionLogSaveRequestVo = new VehicleExceptionLogAddVo();
            vehicleExceptionLogSaveRequestVo.setErrorCode(String.valueOf(abnormal.getErrorCode()));
            vehicleExceptionLogSaveRequestVo.setErrorLevel(abnormal.getErrorLevel());
            vehicleExceptionLogSaveRequestVo.setModule(abnormal.getModuleName());
            String errorMessage = abnormal.getErrorMsg();
            if (StringUtils.isNotBlank(errorMessage)) {
                vehicleExceptionLogSaveRequestVo.setErrorMessage(
                        errorMessage.substring(0, abnormal.getErrorMsg().length() > 1000 ? 1000 : errorMessage.length()));
            }
            if (abnormal.getTimestamp() != 0) {
                vehicleExceptionLogSaveRequestVo.setTimestamp(
                        new Date((Long.valueOf(abnormal.getTimestamp()) / NANOSECOND_TO_MILLISECOND)));
            }
            vehicleExceptionLogSaveRequestVoList.add(vehicleExceptionLogSaveRequestVo);
        }
        return vehicleExceptionLogSaveRequestVoList;
    }

    /**
     * 获取使能信息
     *
     * @param guardianInfoDTO
     * @return
     */
    private DrivableDirectionAddVo getDrivableDirectionAddVo(GuardianDataDto.GuardianInfoDTO guardianInfoDTO) {
        GuardianDataDto.DrivableDirection drivableDirection = guardianInfoDTO.getDrivableDirection();
        DrivableDirectionAddVo directionAddVo = new DrivableDirectionAddVo();
        if (drivableDirection == null) {
            return directionAddVo;
        }
      directionAddVo.setEnableFront(drivableDirection.getEnableFront());
      directionAddVo.setEnableBack(drivableDirection.getEnableBack());
      directionAddVo.setEnableLeft(drivableDirection.getEnableLeft());
      directionAddVo.setEnableRight(drivableDirection.getEnableRight());
      return directionAddVo;
    }

    /**
     * 打印日志,删除多余routingPoint点坐标
     *
     * @param messageList
     */
    private void printfLog(List<String> messageList) {
        for (String message : messageList) {
            try {
                String logStr = message.replaceAll(",\\s*\"routingPoint[\\s\\S]*?]", "");
                logStr = logStr.replaceAll(",\\s*\"serviceInfo\\\":[\\{\\s\\S\\},]*]", "");
                log.info("Received guardian topic=server_upstream_guardian_origin, message={}", logStr);
            } catch (Exception e) {
                log.info("Received guardian fail topic=server_upstream_guardian_origin, message={}", message);
            }
        }
    }
}
