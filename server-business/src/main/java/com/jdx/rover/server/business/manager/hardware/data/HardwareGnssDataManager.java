/*
 * Copyright (c) 2025 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.server.business.manager.hardware.data;

import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.symmetric.AES;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.hardware.data.GnssData;
import com.jdx.rover.server.api.domain.dto.hardware.data.GnssDataDTO;
import com.jdx.rover.server.business.config.ProducerTopicProperties;
import com.jdx.rover.server.business.manager.metadata.MetadataVehicleApiKeyManager;
import com.jdx.rover.server.repository.jmq.JmqProducerManager;
import com.jdx.rover.server.repository.redis.hardware.data.HardwareGnssDataRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;

/**
 * 车辆硬件Gnss信息数据管理器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025-08-25
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class HardwareGnssDataManager {
    private final HardwareGnssDataRepository hardwareGnssDataRepository;

    private final MetadataVehicleApiKeyManager metadataVehicleApiKeyManager;
    /**
     * jmq消息发送producer
     */
    private final JmqProducerManager jmqProducerManager;

    /**
     * 生产主题配置类
     */
    private final ProducerTopicProperties producerTopicProperties;

    /**
     * 处理GNSS实时数据
     *
     * @param gnssDataList GNSS实时数据列表
     * @param vehicleName  车辆名称
     * @param recordTime   记录时间
     */
    public void processGnssData(List<GnssData> gnssDataList, String vehicleName, Date recordTime) {
        if (CollectionUtils.isEmpty(gnssDataList)) {
            return;
        }

        GnssData gnssData = gnssDataList.get(0);
        GnssDataDTO dto = convertGnssData(gnssData, recordTime, vehicleName);
        if (dto != null) {
            hardwareGnssDataRepository.set(dto);
            jmqProducerManager.sendOrderedAndLog(producerTopicProperties.getServerHardwareGnssData(), JsonUtils.writeValueAsString(dto), vehicleName);
        }
    }


    /**
     * 转换GNSS实时数据
     *
     * @param gnssData    GNSS实时数据
     * @param recordTime  记录时间
     * @param vehicleName 车辆名称
     * @return GNSS实时数据DTO
     */
    private GnssDataDTO convertGnssData(GnssData gnssData, Date recordTime, String vehicleName) {
        if (gnssData == null) {
            return null;
        }

        GnssDataDTO dto = new GnssDataDTO();
        dto.setVehicleName(vehicleName);
        dto.setOnlineStatus(gnssData.getOnlineStatus().getNumber());
        dto.setRtkState(gnssData.getRtkState());
        dto.setSatelliteNumber(gnssData.getSatelliteNumber());
        dto.setHdop(gnssData.getHdop());
        dto.setSnr(gnssData.getSnr());
        dto.setHeading(gnssData.getHeading());
        dto.setRecordTime(recordTime);

        // 处理位置数据
        if (!gnssData.getPosition().isEmpty()) {
            processPositionData(dto, gnssData, vehicleName);
        }
        return dto;
    }

    /**
     * 处理位置数据
     *
     * @param dto         GNSS数据DTO
     * @param gnssData    GNSS数据
     * @param vehicleName 车辆名称
     */
    private void processPositionData(GnssDataDTO dto, GnssData gnssData, String vehicleName) {
        try {
            // 获取AES加密器
            AES aes = metadataVehicleApiKeyManager.getVehicleApiKey(vehicleName);
            if (aes == null) {
                log.warn("未找到车辆API密钥!{}", vehicleName);
                return;
            }

            // 解密位置数据
            byte[] positionBytes = gnssData.getPosition().toByteArray();
            String position = aes.decryptStr(positionBytes);
            if (StrUtil.isBlank(position)) {
                return;
            }

            dto.setPosition(position);

            // 解析经纬度
            String[] positionArray = position.split(StrPool.COMMA);
            if (positionArray.length == 2) {
                dto.setLongitude(Double.parseDouble(positionArray[0]));
                dto.setLatitude(Double.parseDouble(positionArray[1]));
            }
        } catch (Exception e) {
            log.warn("转换GNSS位置数据失败: {}", vehicleName, e);
        }
    }
}