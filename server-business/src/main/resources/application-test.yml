server:
  port: 8013
spring:
  cloud:
    nacos:
      discovery:
        server-addr: *************:8848
        username: nacos
        password: nacos@jdlX2022
  kafka:
    bootstrap-servers: *************:9092
  data:
    redis:
      host: *************
      password: jdlX2022@redis-test
jdd:
  easyjob:
    enable: false
    host: http://*************:8080
    secret: 747da872243c451c758d3700605de5a5
project:
  kafka:
    second:
      bootstrap-servers: *************:9092