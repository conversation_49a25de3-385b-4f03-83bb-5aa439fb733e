<?xml version="1.0" encoding="UTF-8"?>

<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:jmq="http://code.jd.com/schema/jmq"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
    http://www.springframework.org/schema/beans/spring-beans.xsd
    http://code.jd.com/schema/jmq http://code.jd.com/schema/jmq/jmq-1.1.xsd">

    <!--配置transport，每个transport实例对应一个APP，同一APP如果生产和消费多个主题，只需要配置一个transport实例即可-->
    <jmq:transport address="${jmq.address}" password="${jmq.password}" app="${jmq.app}" user="${jmq.app}"/>

    <!--配置producer-->
    <jmq:producer id="producer" retryTimes="2" transport="jmq.transport"/>

    <!--配置Consumer，messageListener bean需要实现com.jd.jmq.client.consumer.MessageListener接口，在onMessage方法中接收消息。-->
    <jmq:consumer id="consumer" transport="jmq.transport">
        <jmq:listener topic="${jmq.topic.consumer.mqtt_android_info}" listener="androidInfoConsumer"/>
        <jmq:listener topic="${jmq.topic.consumer.server_bus_origin}" listener="busJmqConsumer"/>
        <jmq:listener topic="${jmq.topic.consumer.server_upstream_guardian_origin}" listener="guardianJmqConsumer"/>
<!--        <jmq:listener topic="${jmq.topic.consumer.server_local_view}" listener="localViewConsumer"/>-->
        <jmq:listener topic="${jmq.topic.consumer.map_module_version}" listener="mapModuleVersionConsumer"/>
        <jmq:listener topic="${jmq.topic.consumer.server_report_origin}" listener="reportJmqConsumer"/>
        <jmq:listener topic="${jmq.topic.consumer.server_vehicle_change_status}" listener="vehicleChangeStatusConsumer"/>
        <jmq:listener topic="${jmq.topic.consumer.server_web_terminal_command_down}" listener="webTerminalJmqConsumer"/>
        <jmq:listener topic="${jmq.topic.consumer.mqtt_hardware_property}" listener="hardwarePropertyConsumer"/>
        <jmq:listener topic="${jmq.topic.consumer.mqtt_hardware_data}" listener="hardwareDataConsumer"/>
        <jmq:listener topic="${jmq.topic.consumer.mqtt_hardware_services_reply}" listener="hardwareServicesReplyConsumer"/>
    </jmq:consumer>
</beans>