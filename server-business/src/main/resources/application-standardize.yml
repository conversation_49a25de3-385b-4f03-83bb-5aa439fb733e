server:
  port: 8013
spring:
  cloud:
    nacos:
      discovery:
        server-addr: http://jdxnacos.jd.local/
        username: nacos
        password: nacos@jdlX2022
  kafka: # 默认的kafka集群,发送String
    bootstrap-servers: broker-kafka-yx22zifpk5-az1-0.jvessel-open-hb.jdcloud.com:9092,broker-kafka-yx22zifpk5-az1-1.jvessel-open-hb.jdcloud.com:9092,broker-kafka-yx22zifpk5-az1-2.jvessel-open-hb.jdcloud.com:9092
  data:
    redis:
      database: 2
      host: redis-kcbwfocfvlfh-proxy-nlb.jvessel-open-hb.jdcloud.com
      password: jdlX2023
jdd:
  easyjob:
    host: http://schedule.jdfin.local
    appId: xata-server-business
    secret: 042d166a8e936889cc91c3761de1de9b
project:
  kafka:
    second:
      bootstrap-servers: broker-kafka-yx22zifpk5-az1-0.jvessel-open-hb.jdcloud.com:9092,broker-kafka-yx22zifpk5-az1-1.jvessel-open-hb.jdcloud.com:9092,broker-kafka-yx22zifpk5-az1-2.jvessel-open-hb.jdcloud.com:9092