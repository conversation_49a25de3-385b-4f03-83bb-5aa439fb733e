server:
  port: 8013
spring:
  cloud:
    nacos:
      discovery:
        server-addr: http://jdxnacos-beta.jd.local/
        username: nacos
        password: nacos-beta@jdlX2022
  kafka:
    bootstrap-servers: broker-kafka-pqxxtsd6av-az1-0.jvessel-open-hb.jdcloud.com:9092,broker-kafka-pqxxtsd6av-az1-1.jvessel-open-hb.jdcloud.com:9092,broker-kafka-pqxxtsd6av-az1-2.jvessel-open-hb.jdcloud.com:9092
  data:
    redis:
      host: redis-yh7thvwgyt4s-proxy-nlb.jvessel-open-hb.jdcloud.com
      password: jdlX2022
      database: 2
jdd:
  easyjob:
    host: http://schedule.jdfin.local
    appId: server-business-test
    secret: 6845a6eb8788fec370a54602f6bdd9f3
project:
  kafka:
    second:
      bootstrap-servers: broker-kafka-pqxxtsd6av-az1-0.jvessel-open-hb.jdcloud.com:9092,broker-kafka-pqxxtsd6av-az1-1.jvessel-open-hb.jdcloud.com:9092,broker-kafka-pqxxtsd6av-az1-2.jvessel-open-hb.jdcloud.com:9092

jmq:
  address: nameserver.jmq.jd.local:80
  password: af3939d7be074020bfe6c0388c7f4877
  app: serverbusinesstest