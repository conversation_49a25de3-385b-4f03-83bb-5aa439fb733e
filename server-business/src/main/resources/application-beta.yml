server:
  port: 8013
spring:
  cloud:
    nacos:
      discovery:
        server-addr: http://jdxnacos-beta.jd.local/
        username: nacos
        password: nacos-beta@jdlX2022
  kafka:
    bootstrap-servers: broker-kafka-poiybufu79-az1-0.jvessel-open-hb.jdcloud.com:9092,broker-kafka-poiybufu79-az1-1.jvessel-open-hb.jdcloud.com:9092,broker-kafka-poiybufu79-az1-2.jvessel-open-hb.jdcloud.com:9092
  data:
    redis:
      database: 2
      host: redis-hb3hpfz16cbi-proxy-nlb.jvessel-open-hb.jdcloud.com
      password: jdlX2022
jdd:
  easyjob:
    host: http://schedule.jdfin.local
    secret: 1403d02d7423388c2864e8ea6062eae2
project:
  kafka:
    second:
      bootstrap-servers: broker-kafka-poiybufu79-az1-0.jvessel-open-hb.jdcloud.com:9092,broker-kafka-poiybufu79-az1-1.jvessel-open-hb.jdcloud.com:9092,broker-kafka-poiybufu79-az1-2.jvessel-open-hb.jdcloud.com:9092

jmq:
  address: nameserver.jmq.jd.local:80
  password: af3939d7be074020bfe6c0388c7f4877
  app: serverbusinesstest