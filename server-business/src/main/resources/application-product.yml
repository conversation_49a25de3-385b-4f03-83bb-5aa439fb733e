server:
  port: 8013
spring:
  cloud:
    nacos:
      discovery:
        server-addr: http://jdxnacos.jd.local/
        username: nacos
        password: nacos@jdlX2022
  kafka:
    bootstrap-servers: broker-kafka-dzk69k556l-az1-0.jvessel-open-hb.jdcloud.com:9092,broker-kafka-dzk69k556l-az1-1.jvessel-open-hb.jdcloud.com:9092,broker-kafka-dzk69k556l-az1-2.jvessel-open-hb.jdcloud.com:9092
  data:
    redis:
      database: 2
      host: redis-ozj2fnhyspho-proxy-nlb.jvessel-open-hb.jdcloud.com
      password: jdlX2022
jdd:
  easyjob:
    host: http://schedule.jdfin.local
    secret: 1403d02d7423388c2864e8ea6062eae2
project:
  kafka:
    second:
      bootstrap-servers: kafka-vukl3p8rk4-kafka-bootstrap.kafka-vukl3p8rk4-hb.jvessel2.jdcloud.com:9092

jmq:
  address: nameserver.jmq.jd.local:80
  password: 1e155b79021d46499f35f7f7a0e416e0
  app: roverserverbusiness
  topic:
    suffix: