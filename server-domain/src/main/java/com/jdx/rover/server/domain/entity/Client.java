/*
 * Copyright (c) 2021 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.server.domain.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * webSocket 客户端
 *
 * <AUTHOR>
 */
@Data
public class Client implements Serializable {
  private static final long serialVersionUID = 1L;

  /**
   * 用户名/车号
   */
  String name;

  /**
   * 客户端类型,对应ClientType中字段
   */
  String type;

  /**
   * 被连接的服务器主机名称,必须保证唯一
   */
  String hostName;

  /**
   * webSocket session ID
   */
  String sessionId;

  /**
   * 客户端连接时间
   */
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
  Date connectTime;

  /**
   * 服务端时间
   */
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
  Date serverTime;

  /**
   * 客户端时间
   */
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
  Date clientTime;
}
