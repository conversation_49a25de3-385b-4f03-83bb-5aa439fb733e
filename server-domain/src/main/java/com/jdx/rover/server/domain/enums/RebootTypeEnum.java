/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.server.domain.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * <p>
 * 启动操作类型
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum RebootTypeEnum {
  /**
   * <p>
   * The reboot type.
   * </p>
   */
   WEBTERMINAL("WEBTERMINAL"), SUPERVISOR("SUPERVISOR");

  /**
   * <p>
   * reboot type.
   * </p>
   */
  private String type;
}
