/*
 * Copyright (c) 2021 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.server.domain.enums;

import com.jdx.rover.server.api.domain.enums.guardian.AlarmTypeEnum;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum GuardianErrorEnum {
  // 防撞
  ERROR_DRIVER_CHASSIS_STOP_BUMPER("-1055", AlarmTypeEnum.VEHICLE_CRASH.getValue(), "VEHICLE_CRASH", 201, "碰撞"),
  ERROR_DRIVER_CHASSIS_EMG_STOP_F_BUMPER("-1042", AlarmTypeEnum.VEHICLE_CRASH.getValue(), "VEHICLE_CRASH", 202, "碰撞"),
  ERROR_DRIVER_CHASSIS_EMG_STOP_R_BUMPER("-1043", AlarmTypeEnum.VEHICLE_CRASH.getValue(), "VEHICLE_CRASH", 203, "碰撞"),
  ERROR_GUARDIAN_COLLISION_DETECTION("-13601", AlarmTypeEnum.VEHICLE_CRASH.getValue(), "VEHICLE_CRASH", 204, "碰撞"),

    // 硬件异常
    DOMAIN_CONTROLLER_ALARM("DOMAIN_CONTROLLER_ALARM", AlarmTypeEnum.DOMAIN_CONTROLLER_ALARM.getValue(), "HARDWARE_ERROR", 301, "域控告警"),
    RADAR_WARNING("RADAR_WARNING", AlarmTypeEnum.RADAR_WARNING.getValue(), "HARDWARE_ERROR", 302, "雷达预警"),
    RADAR_FAILURE("RADAR_FAILURE", AlarmTypeEnum.RADAR_FAILURE.getValue(), "HARDWARE_ERROR", 303, "雷达故障"),
    AUDIBLE_AND_VISUAL_ALARM_FAILURE("AUDIBLE_AND_VISUAL_ALARM_FAILURE", AlarmTypeEnum.AUDIBLE_AND_VISUAL_ALARM_FAILURE.getValue(), "HARDWARE_ERROR", 304, "声光电故障"),
    EDGE_SENSOR_FAILURE("EDGE_SENSOR_FAILURE", AlarmTypeEnum.EDGE_SENSOR_FAILURE.getValue(), "HARDWARE_ERROR", 305, "触边故障"),
    BATTERY_FAILURE("BATTERY_FAILURE", AlarmTypeEnum.BATTERY_FAILURE.getValue(), "HARDWARE_ERROR", 306, "电池故障"),
    BRAKE_FAILURE("BRAKE_FAILURE", AlarmTypeEnum.BRAKE_FAILURE.getValue(), "HARDWARE_ERROR", 307, "刹车故障"),
    PARKING_BRAKE_FAILURE("PARKING_BRAKE_FAILURE", AlarmTypeEnum.PARKING_BRAKE_FAILURE.getValue(), "HARDWARE_ERROR", 308, "驻车故障"),
    STEERING_EPS_FAILURE("STEERING_EPS_FAILURE", AlarmTypeEnum.STEERING_EPS_FAILURE.getValue(), "HARDWARE_ERROR", 309, "转向EPS故障"),
    ELECTRONIC_CONTROL_MOTOR_FAILURE("ELECTRONIC_CONTROL_MOTOR_FAILURE", AlarmTypeEnum.ELECTRONIC_CONTROL_MOTOR_FAILURE.getValue(), "HARDWARE_ERROR", 310, "电控、电机故障"),

    // 网络信号
    SIM_CARD_ABNORMAL("SIM_CARD_ABNORMAL", AlarmTypeEnum.SIM_CARD_ABNORMAL.getValue(), "NETWORK_SIGNAL", 401, "NETWORK_SIGNAL"),
    CONNECTIVITY_FAILURE("CONNECTIVITY_FAILURE", AlarmTypeEnum.CONNECTIVITY_FAILURE.getValue(), "NETWORK_SIGNAL", 402, "NETWORK_SIGNAL"),

  // 传感器异常
  LOCALIZATION_ERROR("LOCALIZATION_ERROR", AlarmTypeEnum.LOCALIZATION_ERROR.getValue(), "SENSOR_ERROR", 601, "定位异常"),
  ERROR_DRIVER_CLASS_DATA_IS_ERROR("-1306", AlarmTypeEnum.SENSOR_ERROR.getValue(), "SENSOR_ERROR", 602, "传感器异常"),

  // 开机异常
  BOOT_ABNORMAL_FAIL("BOOT_ABNORMAL_FAIL", AlarmTypeEnum.BOOT_ABNORMAL_FAIL.getValue(), "BOOT", 701,"异常重启失败"),
  BOOT_ABNORMAL("BOOT_ABNORMAL", AlarmTypeEnum.BOOT_ABNORMAL.getValue(), "BOOT", 702, "异常重启"),
  BOOT_FAIL("BOOT_FAIL", AlarmTypeEnum.BOOT_FAIL.getValue(), "BOOT", 703, "开机失败"),
  BOOT_TIMEOUT("BOOT_TIMEOUT", AlarmTypeEnum.BOOT_TIMEOUT.getValue(), "BOOT", 704, "开机过久"),

  // 路口异常
  PASS_NO_SIGNAL_INTERSECTION("PASS_NO_SIGNAL_INTERSECTION", AlarmTypeEnum.PASS_NO_SIGNAL_INTERSECTION.getValue(), "INTERSECTION_ABNORMAL", 801, "无保护左转"),
  ERROR_PLANNING_LEFT_TURN_FOR_SIGNAL_INVALID_GOAL_TYPE("-9400", AlarmTypeEnum.VEHICLE_STOP_TRAFFICLIGHT_TAKEUP.getValue(), "INTERSECTION_ABNORMAL", 802, "看灯点被占"),
  ERROR_PLANNING_TASK_TRAFFIC_LIGHT_DECIDER_WAIT_TOO_LONG_TYPE("-9341", AlarmTypeEnum.VEHICLE_STOP_TRAFFICLIGHT_FAIL.getValue(), "INTERSECTION_ABNORMAL", 803, "感知看灯失败"),
  ERROR_PLANNING_LEFT_TURN_FOR_SIGNAL_NO_SOLUTION_TYPE("-9401", AlarmTypeEnum.VEHICLE_STOP_TRAFFICLIGHT_ADJUST.getValue(), "INTERSECTION_ABNORMAL", 804, "路口看灯调整失败"),
  ERROR_PLANNING_STAGE_DEFAULT_NO_ENOUGH_SPACE_TO_ADJUST_VEHICLE_POSE("-9205", AlarmTypeEnum.VEHICLE_STOP_TRAFFICLIGHT_ADJUST.getValue(), "INTERSECTION_ABNORMAL", 805, "路口看灯调整失败"),
  ERROR_PLANNING_TASK_INTERSECTION_PARKING_DECIDER_WAIT_TOO_LONG_TYPE("-9342", AlarmTypeEnum.VEHICLE_STOP_INTERSECTION_STUCK.getValue(), "INTERSECTION_ABNORMAL", 806, "路口遇阻"),

  // 停车
  BUTTON_EMERGENCY_STOP_TYPE("DRIVEMODE_TAKEOVER_EMERGENCY_STOP", AlarmTypeEnum.VEHICLE_STOP_BUTTON.getValue(), "STOP", 901, "按钮停车"),
  GATE_STUCK("GATE_STUCK", AlarmTypeEnum.GATE_STUCK.getValue(), "STOP", 902, "过门遇阻"),
  ERROR_PLANNING_PARKING_INVALID_GOAL_TYPE("-9402", AlarmTypeEnum.VEHICLE_STOP_STOP_TAKEUP.getValue(), "STOP", 904, "停靠点被占"),
  ERROR_PLANNING_PARKING_NO_SOLUTION_TYPE("-9403", AlarmTypeEnum.VEHICLE_STOP_STOP_ADJUST.getValue(), "STOP", 905, "停靠点调整失败"),
  ABNORMAL_STOP_ERROR_LEVE_TYPE("FATAL", AlarmTypeEnum.VEHICLE_STOP_FATAL.getValue(), "STOP", 906, "异常停车"),
  ERROR_GUARDIAN_SYSTEM_CPU_TEMPATURE_TOO_HIGH("-13708", AlarmTypeEnum.CPU_HIGH_TEMPERATURE.getValue(), "STOP", 907, "CPU温度过高"),
  ERROR_ROUTING_ERROR_BUSINESS_ID_NOT_MATCH("-7016", AlarmTypeEnum.ROUTING_PLAN_FAIL.getValue(), "STOP", 908, "Routing规划失败"),
  ERROR_ROUTING_FAR_AWAY_FROM_LANE("-7005", AlarmTypeEnum.ROUTING_PLAN_FAIL.getValue(), "STOP", 908, "Routing规划失败"),
  ERROR_ROUTING_FIND_NO_LINK("-7006", AlarmTypeEnum.ROUTING_PLAN_FAIL.getValue(), "STOP", 908, "Routing规划失败"),
  ERROR_ROUTING_START_END_TOO_CLOSE("-7010", AlarmTypeEnum.ROUTING_PLAN_FAIL.getValue(), "STOP", 908, "Routing规划失败"),
  ERROR_ROUTING_DRIVE_TO_INDEX_OUT_OF_RANGE("-7017", AlarmTypeEnum.ROUTING_PLAN_FAIL.getValue(), "STOP", 908, "Routing规划失败"),
  ERROR_ROUTING_INVALID_CIRCLE_LIST("-7018", AlarmTypeEnum.ROUTING_PLAN_FAIL.getValue(), "STOP", 908, "Routing规划失败"),
  ERROR_ROUTING_POINT_OUT_MAP("-7020", AlarmTypeEnum.ROUTING_PLAN_FAIL.getValue(), "STOP", 908, "Routing规划失败"),
  GUARD_EMERGENCY_STOP_ERROR_CODE_TYPE("-13600", AlarmTypeEnum.VEHICLE_STOP_GUARDIAN.getValue(), "STOP", 909, "安全接管停车"),
  PARK_HARD("PARK_HARD", AlarmTypeEnum.PARK_HARD.getValue(), "STOP", 910, "停靠困难"),
  STUCK_ERROR_CODE_TYPE("VEHICLE_STOP_TIMEOUT", AlarmTypeEnum.VEHICLE_STOP_TIMEOUT.getValue(), "STOP", 911, "遇阻停车"),

  // 电量
  LOW_BATTERY_ERROR_CODE_TYPE("-13005", AlarmTypeEnum.LOW_BATTERY.getValue(), "LOW_BATTERY", 1001, "电量低"),

  // 胎压
  TIRE_PRESSURE_SENSOR_OFFLINE("TIRE_PRESSURE_SENSOR_OFFLINE", AlarmTypeEnum.TIRE_PRESSURE_SENSOR_OFFLINE.getValue(), "LOW_PRESSURE", 1101, "胎压传感器离线"),
  PRESSURE_SHARP_DECREASE("PRESSURE_SHARP_DECREASE", AlarmTypeEnum.PRESSURE_SHARP_DECREASE.getValue(), "LOW_PRESSURE", 1102, "胎压骤减"),
  ERROR_DRIVER_CHASSIS_WHEEL_ALARM("-1029", AlarmTypeEnum.LOW_PRESSURE.getValue(), "LOW_PRESSURE", 1103, "胎压异常"),
  ;

  /**
   * 错误码
   */
  private final String errorCode;
  /**
   * 报警类型
   */
  private final String alarmType;
  /**
   * 报警分类
   */
  private final String category;
  /**
   * 优先级
   */
  private final Integer priority;
  /**
   * 标题描述
   */
  private final String title;

  private static Map<String, GuardianErrorEnum> errorCodeMap = new HashMap<>();

  private static Map<String, GuardianErrorEnum> alarmTypeMap = new HashMap<>();

  static {
    for (GuardianErrorEnum itemEnum : GuardianErrorEnum.values()) {
      errorCodeMap.put(itemEnum.errorCode, itemEnum);
      alarmTypeMap.put(itemEnum.alarmType, itemEnum);
    }
  }

  public static GuardianErrorEnum of(final String errorCode) {
    return errorCodeMap.get(errorCode);
  }

  public static GuardianErrorEnum getFromAlarmType(final String alarmType) {
    return alarmTypeMap.get(alarmType);
  }
}
