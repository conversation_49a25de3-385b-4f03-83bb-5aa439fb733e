/***************************************************************************
 *
 * Copyright (c) 2020 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.server.domain.vo.guardian;

import java.util.Date;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <p>
 * This is a view object class for vehicle exception.
 * </p>
 *
 * <p>
 * <strong>Thread Safety: </strong> This class is mutable and not thread safe. But it will be used
 * as entity so it'll not cause any thread safe problem.
 * </p>
 *
 * <AUTHOR> wangguotai
 * @version 1.0
 */
@Data
public class VehicleExceptionLogAddVo {
  /**
   * <p>
   * The error code. The default value is null. It's changeable.
   * </p>
   */
  @NotNull
  private String errorCode;

  /**
   * <p>
   * The error level. The default value is null. It's changeable.
   * </p>
   */
  @NotBlank
  private String errorLevel;

  /**
   * <p>
   * The error message. The default value is null. It's changeable.
   * </p>
   */
  @NotNull
//  @NotPureBlank
  private String errorMessage;

  /**
   * <p>
   * The vehicle module. The default value is null. It's changeable.
   * </p>
   */
  @NotNull
//  @NotPureBlank
  private String module;

  /**
   * <p>
   * The timestamp of exception. The default value is null. It's changeable.
   * </p>
   */
  @NotNull
  private Date timestamp;

  /**
   * <p>
   * The error code string. The default value is null. It's changeable.
   * </p>
   */
  @NotNull
  private String errorCodeString;
}
