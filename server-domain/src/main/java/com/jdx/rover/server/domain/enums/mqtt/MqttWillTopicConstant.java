/*
 * Copyright (c) 2021-2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.server.domain.enums.mqtt;

/**
 * mqtt消息通配符
 *
 * <AUTHOR>
 */
public interface MqttWillTopicConstant {
  /**
   * 安卓遗嘱
   */
  String ANDROID_WILL = "android/will/+";

  /**
   * 车端rover遗嘱
   */
  String ROVER_WILL = "rover/will/+";

  /**
   * 视频遗嘱
   */
  String VIDEO_WILL = "video/will/+";

  /**
   * 平行驾驶大屏遗嘱
   */
  String DRIVE_SCREEN_WILL = "r/drive/screen/will/+";

  /**
   * 平行驾驶控制屏遗嘱
   */
  String DRIVE_CONTROL_PAD_WILL = "r/drive/controlPad/will/+";

  /**
   * 平行驾驶驾驶舱遗嘱
   */
  String DRIVE_COCKPIT_WILL = "r/drive/cockpit/will/+";
  /**
   * 小程序遗嘱
   */
  String DRIVE_MINIMONITOR_WILL = "r/drive/miniMonitor/will/+";

  /**
   * 监控遗嘱
   */
  String DRIVE_MONITOR_WILL = "r/drive/monitor/will/+";

  /**
   * PDU遗嘱
   */
  String PDU_WILL = "r/pdu/will/+";
}
