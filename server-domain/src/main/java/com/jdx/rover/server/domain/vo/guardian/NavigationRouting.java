/***************************************************************************
 *
 * Copyright (c) 2020 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.server.domain.vo.guardian;

import lombok.Data;

/**
 * <p>
 * This is the entity for navigation Routing credential.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class NavigationRouting {

  /**
   * <p>
   * Represents the vehicle's id. The default value is null. It's changeable.
   * </p>
   */
  private Integer vehicleId;

  /**
   * <p>
   * Represents the schedule's id. The default value is null. It's changeable.
   * </p>
   */
  private Integer scheduleId;

  /**
   * <p>
   * Represents the schedule's name. The default value is null. It's changeable.
   * </p>
   */
  private String scheduleName;

  /**
   * <p>
   * Represents the navigationg routing id. The default value is null. It's changeable.
   * </p>
   */
  private Integer routingId;
}
