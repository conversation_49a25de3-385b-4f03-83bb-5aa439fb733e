/*
 * Copyright (c) 2023 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.server.domain.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * 日志开关
 *
 * <AUTHOR>
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum LogConfigEnum {
    BUS(false, "是否打印BUS日志"),
    ;

    /**
     * 类型
     */
    private boolean value;

    /**
     * 中文描述
     */
    private String title;

    public void setValue(boolean value) {
        this.value = value;
    }
}
