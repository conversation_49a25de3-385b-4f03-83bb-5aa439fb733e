/*
 * Copyright (c) 2021 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.server.domain.entity;

import java.io.Serializable;
import lombok.Data;

/**
 * <p>
 * This is an entity to save message in topic.
 * </p>
 *
 * <p>
 * <strong>Thread Safety: </strong> This class is mutable and not thread safe. But it is an entity
 * so it'll not cause any thread safe problem.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class CommandMessage implements Serializable {
  private static final long serialVersionUID = 1L;

  /**
   * <p>
   * The data of client.
   * </p >
   */
  private Client client;
  /**
   * <p>
   * The command data.
   * </p>
   */
  private String message;
}
