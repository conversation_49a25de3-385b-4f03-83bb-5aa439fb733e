/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.server.domain.constants;

/**
 * <AUTHOR>
 */
public interface LocalCacheConstant {
  /**
   * 车辆电量信息缓存
   */
  String SERVER_KAFKA_POWER = "server_kafka_power";

  /**
   * 车辆异常信息缓存
   */
  String SERVER_EXCEPTION = "server_exception";

  /**
   * 车辆版本信息缓存
   */
  String SERVER_VERSION = "server_version";

  /**
   * 车辆PNC信息缓存
   */
  String SERVER_PNC = "server_pnc";

  /**
   * 车辆告警信息缓存
   */
  String REPORT_ALARM_HASH = "report_alarm_hash";
}
