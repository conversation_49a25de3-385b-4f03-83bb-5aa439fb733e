/***************************************************************************
 *
 * Copyright (c) 2020 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.server.domain.vo.guardian;

import java.util.List;
import lombok.Data;

/**
 * <p>
 * This is a entity for navigation planning status.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class NavigationPlanningStatus {

  /**
   * <p>
   * Represents the planning result code type. The default value is null. It's changeable.
   * </p>
   */
  private String type;

  /**
   * <p>
   * Represents the planning result code msg. The default value is null. It's changeable.
   * </p>
   */
  private String msg;

  /**
   * <p>
   * Represents the planning intersection status. The default value is null. It's changeable.
   * </p>
   */
  private String intersectionStatus;

  /**
   * <p>
   * Represents the planning traffic light info. The default value is null. It's changeable.
   * </p>
   */
  private List<TrafficLightInfo> trafficLightInfo;
  /**
   * 通过无信号路口
   */
  private PassNoSignalIntersectionVO passNoSignalIntersection;
}
