/*
 * Copyright (c) 2021 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.server.domain.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * 客户端类型.
 *
 * <AUTHOR>
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum ClientTypeEnum {
  ANDRIOD_ROVER("AR", "安卓ROVER客户端"),
  GUARDIAN_ROVER("GR", "guardian客户端"),
  STREAMING("SD", "推流"),
  USER("UC", "用户"),
  ROVER("RV", "ROVER客户端"),
  DIRECT("DR", "direct客户端"),
  ANDRIOD_CLIENT("AC", "安卓客户端"),
  OP_USER("OUC", "运营人员"),
  APP_USER("AU", "APP用户"),
  OPERATION_USER("OU", "运营用户"),
  TERMINAL_COMPUTER("T_COMPUTER", "ROVERCOMPUTER端"),
  TERMINAL_CONTROL("T_CONTROL", "ROVERCONTROL端"),
  REPORT("REPORT", "REPORT客户端");

  /**
   * 类型
   */
  private String type;

  /**
   * 中文描述
   */
  private String title;
}
