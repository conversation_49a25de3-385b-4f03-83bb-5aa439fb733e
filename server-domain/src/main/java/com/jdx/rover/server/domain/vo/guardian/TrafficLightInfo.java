/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.server.domain.vo.guardian;

import com.jdx.rover.server.domain.enums.TrafficLightSourceEnum;
import lombok.Data;

/**
 * <p>
 * This is the entity for pnc traffic light info.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class TrafficLightInfo {

  /**
   * <p>
   * Represents the traffic light id. The default value is null. It's changeable.
   * </p>
   */
  private String id;

  /**
   * <p>
   * Represents the traffic light source. The default value is null. It's changeable.
   * </p>
   */
  private TrafficLightSourceEnum trafficLightSourceEnum;
}
