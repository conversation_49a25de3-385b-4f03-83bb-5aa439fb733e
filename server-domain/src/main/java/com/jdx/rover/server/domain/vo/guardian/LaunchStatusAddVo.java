/***************************************************************************
 *
 * Copyright (c) 2020 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.server.domain.vo.guardian;

import java.util.Date;
import java.util.List;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <p>
 * This is a launch status add view object.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class LaunchStatusAddVo {

  /**
   * <p>
   * The launch status report time. The default value is null. It's changeable.
   * </p>
   */
  @NotNull
  private Date reportTime;

  /**
   * <p>
   * The launch status state. The default value is null. It's changeable.
   * </p>
   */
  @NotBlank
  private String state;

  /**
   * <p>
   * The launch status current starting module name. The default value is null. It's changeable.
   * </p>
   */
  @NotBlank
  private String currentStartingModuleName;

  /**
   * <p>
   * The launch status ip. The default value is null. It's changeable.
   * </p>
   */
  @NotBlank
  private String ip;

  /**
   * <p>
   * The launch status required module list. The default value is null. It's changeable.
   * </p>
   */
  @NotNull
  private List<String> requiredModuleList;

}
