/***************************************************************************
 *
 * Copyright (c) 2020 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.server.domain.vo.guardian;

import java.util.List;
import lombok.Data;

/**
 * <p>
 * This is the entity for navigation routing status.
 * </p>
 *
 * <AUTHOR> wangguotai
 * @version 1.0
 */
@Data
public class NavigationRoutingStatus {

  /**
   * <p>
   * Represents the routing result code type. The default value is null. It's changeable.
   * </p>
   */
  private String type;

  /**
   * <p>
   * Represents the routing result code msg. The default value is null. It's changeable.
   * </p>
   */
  private String msg;

  /**
   * <p>
   * Represents the routing Id. The default value is null. It's changeable.
   * </p>
   */
  private Integer routingId;

  /**
   * <p>
   * Represents the routing current stop index. The default value is null. It's changeable.
   * </p>
   */
  private Integer currentStopIndex;

  /**
   * <p>
   * Represents the routing global mileage. The default value is null. It's changeable.
   * </p>
   */
  private Double globalMileage;

  /**
   * <p>
   * Represents the routing power consumption prediction. The default value is null. It's
   * changeable.
   * </p>
   */
  private Double powerConsumptionPrediction;

  /**
   * <p>
   * Represents the routing remaining power. The default value is null. It's changeable.
   * </p>
   */
  private Double remainingPower;

  /**
   * <p>
   * Represents the routing position list. The default value is null. It's changeable.
   * </p>
   */
  private List<NavigationStopResult> stopResultList;

  /**
   * <p>
   * Represents the routing mileage to next stop. The default value is null. It's changeable.
   * </p>
   */
  private Double mileageToNextStop;

  /**
   * 当前站点完成里程
   */
  private Double currentStopFinishedMileage;

  /**
   * <p>
   * Represents the routing nav sequence num. The default value is null. It's changeable.
   * </p>
   */
  private Integer navSequenceNum;

  /**
   * <p>
   * Represents the refresh global path. The default value is null. It's changeable.
   * </p>
   */
  private Integer naviRefreshId;
}
