/***************************************************************************
 *
 * Copyright (c) 2020 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.server.domain.vo.guardian;

import java.util.Date;
import java.util.List;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import lombok.Data;

/**
 * <p>
 * This is a view object class for guardian info save request.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class GuardianInfoAddVo {

  /**
   * 车辆名称
   */
  @NotBlank
  private String vehicleName;

  /**
   * 上报时间
   */
  @NotNull
  private Date reportTime;

  /**
   * <p>
   * The vehicle drivable direction. The default value is null. It's changeable.
   * </p>
   */
  @NotNull
  private DrivableDirectionAddVo drivableDirectionRequest;

  /**
   * <p>
   * The list of the vehicle exception log vo. The default value is null. It's
   * changeable.
   * </p>
   */
  @NotNull
  private List<VehicleExceptionLogAddVo> vehicleExceptionLogSaveRequestList;

  /**
   * <p>
   * The list of the vehicle alarm event vo. The default value is null. It's
   * changeable.
   * </p>
   */
  private List<VehicleAlarmEventAddVo> vehicleAlarmEventAddVoList;

  /**
   * <p>
   * The request of the vehicle status vo. The default value is null. It's
   * changeable.
   * </p>
   */
  @NotNull
  private VehicleStatusAddVo vehicleStatusRequest;

  /**
   * <p>
   * The list of the vehicle launch status. The default value is null. It's
   * changeable.
   * </p>
   */
  @NotNull
  private List<LaunchStatusAddVo> launchStatusSaveRequestList;

  /**
   * <p>
   * The request of the vehicle pnc status. The default value is null. It's
   * changeable.
   * </p>
   */
  private NavigationPncStatus navigationPncStatusRequest;

  /**
   * <p>
   * The connection of the vehicle between server. The default value is null. It's
   * changeable.
   * </p>
   */
  private Boolean lostConnection;

  /**
   * <p>
   * The connection of the vehicle between server. The default value is null. It's
   * changeable.
   * </p>
   */
  private Date lostDate;

  /**
   * <p>
   * The request of the vehicle wheel info. The default value is null. It's
   * changeable.
   * </p>
   */
  private VehicleWheelInfoAddVo vehicleWheelInfo;

  /**
   * <p>
   * The request of the vehicle ota info. The default value is null. It's
   * changeable.
   * </p>
   */
  private OtaUpdateTaskStatus otaUpdateTaskStatus;

  /**
   * <p>
   * The request of the vehicle service info. The default value is null. It's
   * changeable.
   * </p>
   */
  private List<ServiceInfo> serviceInfo;
}
