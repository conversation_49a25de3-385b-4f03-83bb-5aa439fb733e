/*
 * Copyright (c) 2021 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.server.domain.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * 车胎位置枚举
 * <AUTHOR>
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum WheelPositionEnum {

  WHEEL_POSITION_NONE("WHEEL_POSITION_NONE", ""),
  WHEEL_POSITION_FRONT_LEFT("WHEEL_POSITION_FRONT_LEFT", "左前轮"),
  WHEEL_POSITION_FRONT_RIGHT("WHEEL_POSITION_FRONT_RIGHT", "右前轮"),
  WHEEL_POSITION_BACK_LEFT("WHEEL_POSITION_BACK_LEFT", "左后轮"),
  WHEEL_POSITION_BACK_RIGHT("WHEEL_POSITION_BACK_RIGHT", "右后轮"),
  ;

  /**
   * 类型
   */
  private final String type;
  /**
   * 描述
   */
  private final String name;

  public static WheelPositionEnum of(final String type) {
    for (WheelPositionEnum itemEnum : WheelPositionEnum.values()) {
      if (itemEnum.type.equals(type)) {
        return itemEnum;
      }
    }
    return WHEEL_POSITION_NONE;
  }
}
