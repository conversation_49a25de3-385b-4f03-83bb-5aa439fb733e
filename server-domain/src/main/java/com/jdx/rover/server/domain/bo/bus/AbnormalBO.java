package com.jdx.rover.server.domain.bo.bus;

import lombok.Data;

/**
 * <p>
 * 报警事件业务对象
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class AbnormalBO {

  /**
   * 模块名称
   */
  private String moduleName;

  /**
   * 错误码
   */
  private Integer errorCode;

  /**
   * 错误级别
   */
  private String errorLevel;

  /**
   * 错误信息
   */
  private String errorMsg;

  /**
   * 开始时间：单位纳秒
   */
  private Long startTime;

  /**
   * 结束时间：单位纳秒
   */
  private Long endTime;
}