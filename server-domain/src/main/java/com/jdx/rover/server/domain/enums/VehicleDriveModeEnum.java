/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.server.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

import java.util.Objects;

/**
 * <p>
 * This is a vehicle drive mode mapping enum.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@AllArgsConstructor
@Getter
@ToString
public enum VehicleDriveModeEnum {
  /**
   * <p>
   * The enumerate vehicle states.
   * </p>
   */
  UNKNOWN("UNK<PERSON>OWN", "UNKNOWN"),
  INITIALIZATION("INITIALIZATION", "INITIALIZATION"),
  READY("READY", "DRIVEMODE_ENGAGED_INIT"),
  AUTO_DRIVE("AUTO_DRIVE", "DRIVEMODE_ENGAGED_AUTO_CTRL"),
  REMOTE_CONTROL("REMOTE_CONTROL", "DRIVEMODE_TAKEOVER_REMOTE_CTRL"),
  ABNORMAL_STOP("ABNORMAL_STOP", "DRIVEMODE_ENGAGED_GUARDIAN_NORMAL"),
  XBOX_CTRL("XBOX_CTRL", "DRIVEMODE_TAKEOVER_XBOX_CTRL"),
  LOCAL_CONTROL("LOCAL_CONTROL", "DRIVEMODE_TAKEOVER_CHASSIS_CTRL"),
  EMERGENCY_STOP("EMERGENCY_STOP", "DRIVEMODE_TAKEOVER_EMERGENCY_STOP"),
  GUARDIAN_EMERGENCY("GUARDIAN_EMERGENCY", "DRIVEMODE_ENGAGED_GUARDIAN_EMERGENCY"),
  GUARDIAN_TAKE_OVER("GUARDIAN_TAKE_OVER", "DRIVEMODE_ENGAGED_GUARDIAN_FORBID_DRIVING"),
  CHASSIS_FAULT("CHASSIS_FAULT", "DRIVEMODE_ENGAGED_CHASSIS_FAULT"),
  CHASSIS_STANDBY("CHASSIS_STANDBY", "DRIVEMODE_ENGAGED_CHASSIS_STANDBY"),
  BUMPER_STOP("BUMPER_STOP", "DRIVEMODE_ENGAGED_CHASSIS_BUMPER"),
  REMOTE_SUPER_CTRL("REMOTE_SUPER_CTRL", "DRIVEMODE_TAKEOVER_REMOTE_SUPER_CTRL"),
  REMOTE_MOBILE_CTRL("REMOTE_MOBILE_CTRL", "DRIVEMODE_TAKEOVER_REMOTE_MOBILE_CTRL"),
  GUARDIAN_COLLISION("GUARDIAN_COLLISION", "DRIVEMODE_ENGAGED_GUARDIAN_COLLISION"),
  REMOTE_JOYSTICK_CTRL("REMOTE_JOYSTICK_CTRL", "DRIVEMODE_TAKEOVER_REMOTE_JOYSTICK_CTRL"),
  REMOTE_JOYSTICK_SUPER_CTRL("REMOTE_JOYSTICK_SUPER_CTRL", "DRIVEMODE_TAKEOVER_REMOTE_JOYSTICK_SUPER_CTRL"),
  ;

  /**
   * <p>
   * The state of vehicle.
   * </p>
   */
  private String vehicleState;

  /**
   * <p>
   * The drive mode corresponding to the enumeration.
   * </p>
   */
  private String driveMode;

  public static VehicleDriveModeEnum getByVehicleState(String vehicleState) {
    for (VehicleDriveModeEnum em : VehicleDriveModeEnum.values()) {
      if (Objects.equals(vehicleState, em.getVehicleState())) {
        return em;
      }
    }
    return UNKNOWN;
  }

  public static VehicleDriveModeEnum getByDriveMode(String driveMode) {
    for (VehicleDriveModeEnum em : VehicleDriveModeEnum.values()) {
      if (Objects.equals(driveMode, em.getDriveMode())) {
        return em;
      }
    }
    return UNKNOWN;
  }

    /**
   * <p>
   * Check state control.
   * </p>
   */
  public boolean isControlState() {
    return this == REMOTE_SUPER_CTRL || this == REMOTE_CONTROL
     || this == XBOX_CTRL || this == LOCAL_CONTROL || this == REMOTE_MOBILE_CTRL;
  }
}
