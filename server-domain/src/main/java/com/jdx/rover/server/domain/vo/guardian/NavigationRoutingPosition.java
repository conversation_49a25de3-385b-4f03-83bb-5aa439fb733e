/***************************************************************************
 *
 * Copyright (c) 2020 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.server.domain.vo.guardian;

import lombok.Data;

/**
 * <p>
 * This is the entity for navigation routing position.
 * </p>
 *
 * <AUTHOR> wangguotai
 * @version 1.0
 */
@Data
public class NavigationRoutingPosition {

  /**
   * <p>
   * Represents the navigation routing position xcsX. The default value is null. It's changeable.
   * </p>
   */
  private Double xcsX;

  /**
   * <p>
   * Represents the navigation routing position xcsY. The default value is null. It's changeable.
   * </p>
   */
  private Double xcsY;

  /**
   * <p>
   * Represents the navigation routing position mapX. The default value is null. It's changeable.
   * </p>
   */
  private Double mapX;

  /**
   * <p>
   * Represents the navigation routing position mapY. The default value is null. It's changeable.
   * </p>
   */
  private Double mapY;

  /**
   * <p>
   * Represents the navigation routing position longitude. The default value is null. It's
   * changeable.
   * </p>
   */
  private Double longitude;

  /**
   * <p>
   * Represents the navigation routing position latitude. The default value is null. It's
   * changeable.
   * </p>
   */
  private Double latitude;
}
