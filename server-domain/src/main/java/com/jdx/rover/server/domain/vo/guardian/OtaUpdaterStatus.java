/***************************************************************************
 *
 * Copyright (c) 2020 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.server.domain.vo.guardian;

import lombok.Data;

import java.util.List;

/**
 * <p>
 * This is the entity for ota update status.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class OtaUpdaterStatus {

  /**
   * <p>
   * Represents the module version. The default value is null. It's changeable.
   * </p>
   */
  private List<OtaModuleVersion> otaModuleVersion;

  /**
   * <p>
   * Represents the ota update task status. The default value is null. It's changeable.
   * </p>
   */
  private OtaUpdateTaskStatus otaUpdateTaskStatus;

}
