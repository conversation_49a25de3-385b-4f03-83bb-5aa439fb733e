/***************************************************************************
 *
 * Copyright (c) 2020 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.server.domain.vo.guardian;

import lombok.Data;

/**
 * <p>
 * This is a entity for navigation pnc status.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class NavigationPncStatus {

  /**
   * <p>
   * Represents the vehicle's planning status. The default value is null. It's changeable.
   * </p>
   */
  private NavigationPlanningStatus planningStatus;

  /**
   * <p>
   * Represents the vehicle's routing status. The default value is null. It's changeable.
   * </p>
   */
  private NavigationRoutingStatus routingStatus;
}
