/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.server.domain.entity;

import lombok.Data;

/**
 * <p>
 * This is the entity for map position point.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MapPoint {

  /**
   * <p>
   * Represents the position longitude of map point. The default value is null. It's
   * changeable.
   * </p>
   */
  private Double longitude;

  /**
   * <p>
   * Represents the  position latitude of map point. The default value is null. It's
   * changeable.
   * </p>
   */
  private Double latitude;
}
