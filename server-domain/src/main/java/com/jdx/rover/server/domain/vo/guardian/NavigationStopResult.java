/***************************************************************************
 *
 * Copyright (c) 2020 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.server.domain.vo.guardian;

import java.util.List;
import lombok.Data;

/**
 * <p>
 * This is the entity for navigation routing position.
 * </p>
 *
 * <AUTHOR> wangguotai
 * @version 1.0
 */
@Data
public class NavigationStopResult {

  /**
   * <p>
   * Represents the stop id. The default value is null. It's changeable.
   * </p>
   */
  private Integer stopId;

  /**
   * <p>
   * Represents the navigation routing mileage. The default value is null. It's changeable.
   * </p>
   */
  private Double routingMileage;
  /**
   * <p>
   * Represents the navigation routing position. The default value is null. It's changeable.
   * </p>
   */
  private List<NavigationRoutingPosition> routingPositionList;
}
