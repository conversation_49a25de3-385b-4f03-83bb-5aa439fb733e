/***************************************************************************
 *
 * Copyright (c) 2020 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.server.domain.vo.guardian;

import java.util.Date;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <p>
 * This is a view object class for vehicle exception.
 * </p>
 *
 * <p>
 * <strong>Thread Safety: </strong> This class is mutable and not thread safe. But it will be used
 * as entity so it'll not cause any thread safe problem.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class VehicleAlarmEventAddVo {
  /**
   * <p>
   * The type of the vehicle alarm event. The default value is null. It's changeable.
   * </p>
   */
  @NotNull
  private String type;

  /**
   * <p>
   * The priority of the vehicle alarm event. The default value is null. It's changeable.
   * </p>
   */
  @NotNull
  private Integer priority;

  /**
   * <p>
   * The occurrence timestamp of exception. The default value is null. It's changeable.
   * </p>
   */
  @NotNull
  private Date occurrenceTimestamp;

  /**
   * <p>
   * The last timestamp of exception. The default value is null. It's changeable.
   * </p>
   */
  @NotNull
  private Date timestamp;

  /**
   * <p>
   * Represents the record timestamp of guardian exception log. The default value is
   * CURRENT_TIMESTAMP. It's changeable.
   * </p>
   */
  @NotNull
  private Date recordTimestamp;
}
