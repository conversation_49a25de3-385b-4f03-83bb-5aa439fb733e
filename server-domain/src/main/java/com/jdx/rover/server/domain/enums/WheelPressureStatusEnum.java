/*
 * Copyright (c) 2021 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.server.domain.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * 车胎位置枚举
 * <AUTHOR>
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum WheelPressureStatusEnum {

  TIRE_PRESSURE_NONE("TIRE_PRESSURE_NONE", ""),
  TIRE_PRESSURE_NORMAL("TIRE_PRESSURE_NORMAL", "正常"),
  TIRE_PRESSURE_OVER("TIRE_PRESSURE_OVER", "胎压高"),
  TIRE_PRESSURE_OVER_LIMIT("TIRE_PRESSURE_OVER_LIMIT", "胎压极高"),
  TIRE_PRESSURE_UNDER("TIRE_PRESSURE_UNDER", "胎压低"),
  TIRE_PRESSURE_UNDER_LIMIT("TIRE_PRESSURE_UNDER_LIMIT", "胎压极低"),
  ;

  /**
   * 类型
   */
  private final String type;
  /**
   * 描述
   */
  private final String name;

  public static WheelPressureStatusEnum of(final String type) {
    for (WheelPressureStatusEnum itemEnum : WheelPressureStatusEnum.values()) {
      if (itemEnum.type.equals(type)) {
        return itemEnum;
      }
    }
    return TIRE_PRESSURE_NONE;
  }
}
