/*
 * Copyright (c) 2021 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.server.domain.vo.guardian;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class ServiceInfo {
  /**
   * <p>
   * Represents the bundle name. The default value is null. It's changeable.
   * </p>
   */
  private String serviceName;

  /**
   * <p>
   * Represents the bundle ip. The default value is null. It's changeable.
   * </p>
   */
  private String ip;

  /**
   * <p>
   * Represents the service state. The default value is null. It's changeable.
   * </p>
   */
  private String serviceState;

  /**
   * <p>
   * Represents the service state. The default value is null. It's changeable.
   * </p>
   */
  private Integer progressPermill;

  /**
   * <p>
   * Represents the bundle msg. The default value is null. It's changeable.
   * </p>
   */
  private String msg;

  /**
   * <p>
   * Represents the report time. The default value is null. It's changeable.
   * </p>
   */
  private Long timestamp;

  /**
   * <p>
   * Represents the error code. The default value is null. It's changeable.
   * </p>
   */
  private Integer errorCode;

  /**
   * <p>
   * Represents the elapsed time. The default value is null. It's changeable.
   * </p>
   */
  private Long elapsedTime;

}