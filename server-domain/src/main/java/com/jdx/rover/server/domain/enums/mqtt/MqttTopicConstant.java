/*
 * Copyright (c) 2021-2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.server.domain.enums.mqtt;

/**
 * mqtt消息通配符
 *
 * <AUTHOR>
 */
public interface MqttTopicConstant {
  /**
   * 服务端共享订阅分组
   */
  String SHARE_GROUP = "server";

  /**
   * 安卓遗嘱
   */
  String ANDROID_WILL = "android/will/+";

  /**
   * 车端rover遗嘱
   */
  String ROVER_WILL = "rover/will/+";

  /**
   * 视频遗嘱
   */
  String VIDEO_WILL = "r/video/will/+";

  /**
   * 安卓指令
   */
  String ANDROID_COMMAND = "android/command/+";

  /**
   * 回复安卓指令
   */
  String REPLY_ANDROID_COMMAND = "reply/android/command/+";

  /**
   * 安卓上报消息
   */
  String ANDROID_INFO = "android/info/+";

  /**
   * 回复安卓上报消息
   */
  String REPLY_ANDROID_INFO = "reply/android/info/+";

  /**
   * 平行驾驶车端状态信息群发
   */
  String DRIVE_VEHICLE_GROUP_INFO = "r/drive/vehicle/group/info/#";

  /**
   * 平行驾驶驾驶舱指令
   */
  String DRIVE_COCKPIT_COMMAND = "r/drive/cockpit/command/#";

  /**
   * ota升级进度
   */
  String OTA_PROGRESS = "ota/progress/+/server";

  /**
   * ota转静默下载
   */
  String OTA_UPDATER_DELAY_REPLY = "ota/updater_delay_reply/+/server";

  /**
   * PDU指令
   */
  String PDU_COMMAND = "r/pdu/command/+";

  /**
   * 回复PDU指令
   */
  String REPLY_PDU_COMMAND = "r/reply/pdu/command/+";

  /**
   * 车身域硬件状态上报
   */
  String R_HARDWARE_STATE = "r/hardware/data/+";
  /**
   * 车身域硬件属性上报
   */
  String R_HARDWARE_PROPERTY = "r/hardware/property/+";
  /**
   * 车身域硬件指令下发回复
   */
  String R_HARDWARE_SERVICES_REPLY = "r/hardware/services_reply/+";
}
