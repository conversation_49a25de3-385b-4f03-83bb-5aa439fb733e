/*
 * Copyright (c) 2021-2021 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.server.domain.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 人工远程重启
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class VehicleManualRebootEntity implements Serializable {
  private static final long serialVersionUID = 1L;

  /**
   * <p>
   * 车号
   * </p>
   */
  private String vehicleName;

  /**
   * <p>
   * 重启类型
   * </p>
   */
  private String type;

  /**
   * <p>
   * 操作时间
   * </p>
   */
  private Date recordTime;

  /**
   * <p>
   * 硬启动
   * </p>
   */
  private Long bootUuid;

  /**
   * <p>
   * 软启动
   * </p>
   */
  private Long bootId;

  /**
   * <p>
   * 告警
   * </p>
   */
  private String alarm;

}
