/*
 * Copyright (c) 2023 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.server.domain.dto.vehicle;

import lombok.Data;

import java.io.Serializable;

/**
 * 车辆状态DTO
 *
 * <AUTHOR>
 */
@Data
public class VehicleStatusDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 车号
     */
    private String vehicleName;

    /**
     * guardian在线
     */
    private String guardianOnline;

    /**
     * 车辆模式
     */
    private String vehicleState;

    /**
     * 路口状态
     */
    private String intersectionStatus;

    /**
     * 运行地图状态
     */
    private String runMapState;

    /**
     * 记录时间
     */
    private String recordTime;

    /**
     * gps信号
     */
    private String gpsSignal;

    /**
     * 定位置信度
     */
    private String sceneSignal;

    /**
     * 零偏状态
     */
    private String steerZero;

    /**
     * 上报最大速度值，xgflags，单位：m/s
     */
    private Double reportMaxVelocity;
}
