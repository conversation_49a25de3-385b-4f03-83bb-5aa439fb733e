/*
 * Copyright (c) 2021-2021 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.server.domain.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 车辆远程指令缓存
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class VehicleRemoteCommandEntity implements Serializable {
  private static final long serialVersionUID = 1L;

  /**
   * <p>
   *  车号
   * </p>
   */
  private String vehicleName;

  /**
   * <p>
   *   指令类型
   * </p>
   */
  private String commandType;

  /**
   * <p>
   * 触发时间
   * </p>
   */
  private Date reportTime;

}
