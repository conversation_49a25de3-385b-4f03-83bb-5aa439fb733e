/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.server.domain.vo.guardian;

import lombok.Data;

/**
 * <p>
 * This is an add view object for vehicle wheel entity save request.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class VehicleWheelEntityAddVo {

  /**
   * <p>
   * The vehicle wheel position. The defaule value is null. It's changeable.
   * </p>
   */
  private String wheelPosition;

  /**
   * <p>
   * The pressure data. The defaule value is null. It's changeable.
   * </p>
   */
  private Integer pressure;

  /**
   * <p>
   * The vehicle wheel pressure status. The default value is null. It's changeable.
   * </p>
   */
  private String pressureStatus;
}
