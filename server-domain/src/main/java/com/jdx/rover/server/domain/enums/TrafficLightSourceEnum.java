/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.server.domain.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * <p>
 * This is a traffic light source enum.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum TrafficLightSourceEnum {
  /**
   * <p>
   * The enumerate traffic light source.
   * </p>
   */
  UNKNOWN("UNKNOWN"), PERCE<PERSON><PERSON>ON("PERCEPTION"), SUPERVISOR("SUPERVISOR");

  /**
   * <p>
   * The traffic light source corresponding to the enumeration.
   * </p>
   */
  private String source;
}
