package com.jdx.rover.server.domain.bo.bus;

import java.util.List;
import lombok.Data;

/**
 * <p>
 * 运营统计启动时长数据业务对象
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class BusinessBootDataBO {

  /**
   * 车号
   */
  private String vehicleName;

  /**
   * 启动唯一编号
   */
  private Long runtimeUuid;

  /**
   * 启动状态
   */
  private Integer status;

  /**
   * 节点名称
   */
  private String nodeName;

  /**
   * 节点IP
   */
  private String nodeIp;

  /**
   * 启动开始时间：单位纳秒
   */
  private Long startTime;

  /**
   * 启动时长：单位纳秒
   */
  private Long elapsedTime;

  /**
   * 上报时间：单位纳秒（用于心跳计算运行时长）
   */
  private Long reportTime;

  /**
   * 各模块启动信息列表
   */
  private List<BusinessModuleBootDataBO> moduleBootDataBOList;
}