package com.jdx.rover.server.domain.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * 消息类型.
 *
 * <AUTHOR>
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum MessageTypeEnum {

  VEHICLE_STATUS_DATA(1, "VEHICLE_STATUS_DATA"),
  BUSINESS_STATISTICS_DATA(2, "BUSINESS_STATISTICS_DATA"),
  BUSINESS_STATISTICS_HEARTBEAT(3, "BUSINESS_STATISTICS_HEARTBEAT"),
  ;

  /**
   * 类型
   */
  private final Integer value;

  /**
   * 中文描述
   */
  private final String name;
}