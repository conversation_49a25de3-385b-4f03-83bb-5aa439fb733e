package com.jdx.rover.server.domain.dto.vehicle;

import lombok.Data;

import java.io.Serializable;

/**
 * 启动详情
 *
 * <AUTHOR>
 * @date 2023/5/9
 */
@Data
public class ReportBootDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 车辆名称
     */
    private String vehicleName;

    /**
     * 硬件启动ID
     */
    private Long runtimeUuid;

    /**
     * 启动ID(可能含软重启)
     */
    private Long bootId;

    /**
     * 启动开始时间：单位纳秒
     */
    private Long startTime;

    /**
     * 启动结束时长：单位纳秒
     */
    private Long endTime;

    /**
     * 整车启动状态
     */
    private String allBootStatus;

    /**
     * 上报时间：单位纳秒
     */
    private Long reportTime;
}
