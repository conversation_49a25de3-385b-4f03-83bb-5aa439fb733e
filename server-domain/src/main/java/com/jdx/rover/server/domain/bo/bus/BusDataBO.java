package com.jdx.rover.server.domain.bo.bus;

import java.util.List;
import lombok.Data;

/**
 * <p>
 * 车辆总线数据业务对象
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class BusDataBO {

  /**
   * <p>
   * 车号
   * </p>
   */
  private String vehicleName;

  /**
   * <p>
   * 车号（别名）
   * </p>
   */
  private String alias;

  /**
   * <p>
   * 车辆模式
   * </p>
   */
  private Integer driveModeValue;

  /**
   * <p>
   * 车辆模式
   * </p>
   */
  private String driveMode;

  /**
   * <p>
   * 经度
   * </p>
   */
  private Float lon;

  /**
   * <p>
   * 纬度
   * </p>
   */
  private Float lat;

  /**
   * <p>
   * 朝向
   * </p>
   */
  private Double heading;

  /**
   * <p>
   * 速度
   * </p>
   */
  private Double speed;

  /**
   * <p>
   * 电量
   * </p>
   */
  private Double power;

  /**
   * <p>
   * 路径规划
   * </p>
   */
  private List<PointBO> navPoints;

  /**
   * <p>
   * 档位
   * </p>
   */
  private Integer tapPosition;

  /**
   * <p>
   * 方向盘转角
   * </p>
   */
  private Double steerAngle;

  /**
   * <p>
   * 车灯状态
   * </p>
   */
  private Integer lightState;

  /**
   * <p>
   * 纵向加速度
   * </p>
   */
  private Double accelerationV;

  /**
   * <p>
   * 横向加速度
   * </p>
   */
  private Double accelerationH;

  /**
   * <p>
   * 油门开度
   * </p>
   */
  private Double torque;

  /**
   * <p>
   * 制动踏板开度
   * </p>
   */
  private Double brake;

  /**
   * <p>
   * 轮速（左前）
   * </p>
   */
  private Double wheelSpeedFL;

  /**
   * <p>
   * 轮速（右前）
   * </p>
   */
  private Double wheelSpeedFR;

  /**
   * <p>
   * 轮速（左后）
   * </p>
   */
  private Double wheelSpeedBL;

  /**
   * <p>
   * 轮速（右后）
   * </p>
   */
  private Double wheelSpeedBR;

  /**
   * <p>
   * 车端上报时间（毫秒）
   * </p>
   */
  private Long timestamp;
}