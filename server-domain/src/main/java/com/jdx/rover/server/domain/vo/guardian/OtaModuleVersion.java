/***************************************************************************
 *
 * Copyright (c) 2020 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.server.domain.vo.guardian;

import lombok.Data;

/**
 * <p>
 * This is the entity for ota update status.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class OtaModuleVersion {

  /**
   * <p>
   * Represents the module version. The default value is null. It's changeable.
   * </p>
   */
  private String name;

  /**
   * <p>
   * Represents the cur version. The default value is null. It's changeable.
   * </p>
   */
  private String curVersion;

  /**
   * <p>
   * Represents the ota version. The default value is null. It's changeable.
   * </p>
   */
  private String otaVersion;

}
