package com.jdx.rover.server.domain.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * 处理报警枚举.
 *
 * <AUTHOR>
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum HandleAlarmEnum {

    IGNORE(1, "忽略处理"),
    ADD(2, "添加报警"),
    REMOVE(3, "删除报警"),
    ;

    /**
     * 类型
     */
    private final Integer value;

    /**
     * 中文描述
     */
    private final String title;
}