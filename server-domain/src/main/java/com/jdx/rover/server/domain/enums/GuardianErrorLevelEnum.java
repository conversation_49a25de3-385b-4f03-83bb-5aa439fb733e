/*
 * Copyright (c) 2021 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.server.domain.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum GuardianErrorLevelEnum {
  FATAL("FATAL", "FATAL异常级别"),
  ERROR("ERROR", "ERROR异常级别"),
  ;

  /**
   * 错误级别
   */
  private final String errorLevel;
  /**
   * 标题描述
   */
  private final String title;

  public static GuardianErrorLevelEnum of(final String errorLevel) {
    for (GuardianErrorLevelEnum itemEnum : GuardianErrorLevelEnum.values()) {
      if (itemEnum.errorLevel.equals(errorLevel)) {
        return itemEnum;
      }
    }
    return null;
  }
}
