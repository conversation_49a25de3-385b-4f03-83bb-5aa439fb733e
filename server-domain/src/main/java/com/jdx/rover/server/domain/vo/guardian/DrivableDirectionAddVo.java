/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.server.domain.vo.guardian;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <p>
 * This is a drivable direction add view object.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class DrivableDirectionAddVo {

  /**
   * <p>
   * The enable front of the vehicle drivable direction. The default value is
   * null. It's changeable.
   * </p>
   */
  @NotNull
  private Boolean enableFront;

  /**
   * <p>
   * The enable back of the vehicle drivable direction. The default value is null.
   * It's changeable.
   * </p>
   */
  @NotNull
  private Boolean enableBack;

  /**
   * <p>
   * The enable left of the vehicle drivable direction. The default value is null.
   * It's changeable.
   * </p>
   */
  @NotNull
  private Boolean enableLeft;

  /**
   * <p>
   * The enable right of the vehicle drivable direction. The default value is
   * null. It's changeable.
   * </p>
   */
  @NotNull
  private Boolean enableRight;
}