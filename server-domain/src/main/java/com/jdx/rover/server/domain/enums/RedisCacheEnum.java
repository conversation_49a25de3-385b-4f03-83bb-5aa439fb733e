/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.server.domain.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * 客户端类型.
 *
 * <AUTHOR>
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum RedisCacheEnum {
  SERVER_GUARDIAN_PNC("server_guardian_pnc_", "Guardian导航信息缓存"),
  GUARDIAN_CONNECT_HASH("guardian:connect:hash", "Guardian连接信息"),
  GUARDIAN_OTA_VERSION("guardian_ota_version_", "车端OTA版本信息"),
  GUARDIAN_VERSION_INFO("guardian_version_info_", "车端版本信息"),
  SERVER_ABNORMAL_HASH("server:abnormal:hash:", "Guardian异常信息"),
  REPORT_ABNORMAL_HASH("report:abnormal:hash:", "Report异常信息"),
  SERVER_ALARM_HASH("server:alarm:hash:", "Guardian告警信息"),
  SERVER_COMMAND_HASH("server:command:hash", "Server指令信息"),
  REPORT_ALARM_HASH("report:alarm:hash:", "Report告警信息"),
  WEB_TERMINAL_CONNECT_HASH("webTerminal:connect:hash", "WebTerminal连接信息"),

  VEHICLE_RESTART_ROVER("vehicle_restart_rover_", "软重启ROVER"),

  VEHICLE_LAST_ROVER_BOOT_SUCCESS("vehicle_last_rover_boot_success_", "ROVER启动成功"),

  ;
  /**
   * 缓存键值前缀,缓存键值=value_ID
   */
  private String value;

  /**
   * 描述标题
   */
  private String title;

  public String getKey(String id) {
    return this.value + id;
  }
}
