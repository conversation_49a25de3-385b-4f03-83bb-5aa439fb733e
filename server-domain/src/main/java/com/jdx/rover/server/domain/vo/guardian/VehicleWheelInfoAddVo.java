/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.server.domain.vo.guardian;

import java.util.List;
import lombok.Data;

/**
 * <p>
 * This is an add view object for vehicle wheel info save request.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class VehicleWheelInfoAddVo {

  /**
   * <p>
   * The wheel alarm flag. The defaule value is null. It's changeable.
   * </p>
   */
  private Boolean wheelAlarm;

  /**
   * <p>
   * The list of vehicle wheel entity. The defaule value is null. It's
   * changeable.
   * </p>
   */
  private List<VehicleWheelEntityAddVo> vehicleWheelEntityAddVo;
}
