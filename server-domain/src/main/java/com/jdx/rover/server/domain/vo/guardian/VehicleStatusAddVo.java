/***************************************************************************
 *
 * Copyright (c) 2020 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.server.domain.vo.guardian;

import java.util.Date;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <p>
 * This is a view object class for vehicle status.
 * </p>
 *
 * <p>
 * <strong>Thread Safety: </strong> This class is mutable and not thread safe. But it will be used
 * as entity so it'll not cause any thread safe problem.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class VehicleStatusAddVo {

  /**
   * <p>
   * The status of the chassis. The default value is null. It's changeable.
   * </p>
   */
  @NotNull
  private String chassisStatus;

  /**
   * <p>
   * The status of the solution. The default value is null. It's changeable.
   * </p>
   */
  @NotNull
  private String solutionStatus;

  /**
   * <p>
   * The usage of the power. The default value is null. It's changeable.
   * </p>
   */
  @NotNull
  private Double powerUsage;

  /**
   * <p>
   * The speed of the vehicle. The default value is null. It's changeable.
   * </p>
   */
  @NotNull
  private Double speed;

  /**
   * <p>
   * The last time for the solution updating. The default value is null. It's changeable.
   * </p>
   */
  @NotNull
  private Date lastUpdateTimeForSolution;

  /**
   * <p>
   * The last time for the chassis updating. The default value is null. It's changeable.
   * </p>
   */
  @NotNull
  private Date lastUpdateTimeForChassis;

  /**
   * <p>
   * The latitude for the vehicle. The default value is null. It's changeable.
   * </p>
   */
  @NotNull
  private Double lat;

  /**
   * <p>
   * The longitude for the vehicle. The default value is null. It's changeable.
   * </p>
   */
  @NotNull
  private Double lon;

  /**
   * <p>
   * The heading for the vehicle. The default value is null. It's changeable.
   * </p>
   */
  @NotNull
  private Double heading;

  /**
   * <p>
   * The odometer for the vehicle. The default value is null. It's changeable.
   * </p>
   */
  private Double odometer;

  /**
   * <p>
   * The charge state for the vehicle. The default value is null. It's changeable.
   * </p>
   */
  private String chargeState;

  /**
   * <p>
   * The steer zero state for the vehicle. The default value is null. It's changeable.
   * </p>
   */
  private Double steerZero;
}
